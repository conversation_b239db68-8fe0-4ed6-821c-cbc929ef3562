/**
 * 关系强度图表组件
 * 使用雷达图显示多维度关系分析
 */

import React, { useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  BadgeText,
} from '@gluestack-ui/themed';
import Svg, { 
  Polygon, 
  Line, 
  Circle, 
  Text as SvgText,
  G,
  Defs,
  LinearGradient,
  Stop,
} from 'react-native-svg';

import { Contact, Communication, BusinessEvent } from '../../types';

interface RelationshipDimension {
  name: string;
  value: number; // 0-100
  color: string;
  description: string;
}

interface RelationshipStrengthChartProps {
  contact: Contact;
  communications?: Communication[];
  events?: BusinessEvent[];
  size?: number;
  showLabels?: boolean;
  showValues?: boolean;
}

export const RelationshipStrengthChart: React.FC<RelationshipStrengthChartProps> = ({
  contact,
  communications = [],
  events = [],
  size = 200,
  showLabels = true,
  showValues = true,
}) => {
  // 计算关系维度数据
  const dimensions = useMemo(() => {
    return calculateRelationshipDimensions(contact, communications, events);
  }, [contact, communications, events]);

  const center = size / 2;
  const radius = (size - 60) / 2;
  const angleStep = (2 * Math.PI) / dimensions.length;

  // 生成雷达图坐标点
  const generatePolygonPoints = (values: number[]) => {
    return values.map((value, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const distance = (value / 100) * radius;
      const x = center + distance * Math.cos(angle);
      const y = center + distance * Math.sin(angle);
      return `${x},${y}`;
    }).join(' ');
  };

  // 生成网格线
  const generateGridLines = () => {
    const lines = [];
    const gridLevels = [20, 40, 60, 80, 100];

    // 同心圆网格
    gridLevels.forEach((level, levelIndex) => {
      const points = dimensions.map((_, index) => {
        const angle = index * angleStep - Math.PI / 2;
        const distance = (level / 100) * radius;
        const x = center + distance * Math.cos(angle);
        const y = center + distance * Math.sin(angle);
        return `${x},${y}`;
      }).join(' ');

      lines.push(
        <Polygon
          key={`grid-${level}`}
          points={points}
          fill="none"
          stroke="#E5E7EB"
          strokeWidth="1"
          opacity={0.3}
        />
      );
    });

    // 径向线
    dimensions.forEach((_, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const endX = center + radius * Math.cos(angle);
      const endY = center + radius * Math.sin(angle);

      lines.push(
        <Line
          key={`radial-${index}`}
          x1={center}
          y1={center}
          x2={endX}
          y2={endY}
          stroke="#E5E7EB"
          strokeWidth="1"
          opacity={0.3}
        />
      );
    });

    return lines;
  };

  // 生成标签
  const generateLabels = () => {
    if (!showLabels) return [];

    return dimensions.map((dimension, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const labelDistance = radius + 20;
      const x = center + labelDistance * Math.cos(angle);
      const y = center + labelDistance * Math.sin(angle);

      return (
        <SvgText
          key={`label-${index}`}
          x={x}
          y={y}
          fontSize="12"
          fill="#374151"
          textAnchor="middle"
          alignmentBaseline="middle"
        >
          {dimension.name}
        </SvgText>
      );
    });
  };

  // 生成数值点
  const generateValuePoints = () => {
    if (!showValues) return [];

    return dimensions.map((dimension, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const distance = (dimension.value / 100) * radius;
      const x = center + distance * Math.cos(angle);
      const y = center + distance * Math.sin(angle);

      return (
        <G key={`point-${index}`}>
          <Circle
            cx={x}
            cy={y}
            r="4"
            fill={dimension.color}
            stroke="#FFFFFF"
            strokeWidth="2"
          />
          <SvgText
            x={x}
            y={y - 15}
            fontSize="10"
            fill="#374151"
            textAnchor="middle"
            fontWeight="bold"
          >
            {dimension.value}
          </SvgText>
        </G>
      );
    });
  };

  const polygonPoints = generatePolygonPoints(dimensions.map(d => d.value));
  const overallStrength = Math.round(dimensions.reduce((sum, d) => sum + d.value, 0) / dimensions.length);

  return (
    <VStack space="md" alignItems="center">
      {/* 图表 */}
      <Box position="relative">
        <Svg width={size} height={size}>
          <Defs>
            <LinearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#3B82F6" stopOpacity="0.3" />
              <Stop offset="100%" stopColor="#8B5CF6" stopOpacity="0.1" />
            </LinearGradient>
          </Defs>

          {/* 网格 */}
          {generateGridLines()}

          {/* 数据区域 */}
          <Polygon
            points={polygonPoints}
            fill="url(#chartGradient)"
            stroke="#3B82F6"
            strokeWidth="2"
          />

          {/* 标签 */}
          {generateLabels()}

          {/* 数值点 */}
          {generateValuePoints()}
        </Svg>

        {/* 中心分数 */}
        <Box
          position="absolute"
          top="50%"
          left="50%"
          transform={[{ translateX: -25 }, { translateY: -25 }]}
          width={50}
          height={50}
          borderRadius="$full"
          bg="$primary600"
          justifyContent="center"
          alignItems="center"
        >
          <Text
            color="$white"
            fontWeight="$bold"
            fontSize="$lg"
          >
            {overallStrength}
          </Text>
        </Box>
      </Box>

      {/* 维度说明 */}
      <VStack space="sm" width="100%">
        <Text fontWeight="$bold" textAlign="center" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
          关系强度分析
        </Text>
        
        <VStack space="xs">
          {dimensions.map((dimension, index) => (
            <HStack key={index} justifyContent="space-between" alignItems="center">
              <HStack space="sm" alignItems="center" flex={1}>
                <Box
                  width={12}
                  height={12}
                  borderRadius="$sm"
                  bg={dimension.color}
                />
                <Text
                  size="sm"
                  color="$textLight700"
                  sx={{ _dark: { color: '$textDark300' } }}
                  flex={1}
                >
                  {dimension.name}
                </Text>
              </HStack>
              
              <HStack space="sm" alignItems="center">
                <Text
                  size="sm"
                  fontWeight="$medium"
                  color="$textLight900"
                  sx={{ _dark: { color: '$textDark100' } }}
                >
                  {dimension.value}
                </Text>
                <Badge
                  size="xs"
                  variant="solid"
                  action={dimension.value >= 80 ? 'success' : dimension.value >= 60 ? 'warning' : 'muted'}
                >
                  <BadgeText>
                    {dimension.value >= 80 ? '强' : dimension.value >= 60 ? '中' : '弱'}
                  </BadgeText>
                </Badge>
              </HStack>
            </HStack>
          ))}
        </VStack>

        {/* 总体评价 */}
        <Box
          mt="$2"
          p="$3"
          bg="$backgroundLight100"
          borderRadius="$md"
          sx={{ _dark: { bg: '$backgroundDark800' } }}
        >
          <HStack justifyContent="space-between" alignItems="center">
            <Text
              fontWeight="$medium"
              color="$textLight900"
              sx={{ _dark: { color: '$textDark100' } }}
            >
              总体关系强度
            </Text>
            <HStack space="sm" alignItems="center">
              <Text
                fontSize="$xl"
                fontWeight="$bold"
                color="$primary600"
              >
                {overallStrength}
              </Text>
              <Badge
                size="sm"
                variant="solid"
                action={overallStrength >= 80 ? 'success' : overallStrength >= 60 ? 'warning' : 'muted'}
              >
                <BadgeText>
                  {overallStrength >= 80 ? '强关系' : overallStrength >= 60 ? '中等关系' : '弱关系'}
                </BadgeText>
              </Badge>
            </HStack>
          </HStack>
        </Box>
      </VStack>
    </VStack>
  );
};

/**
 * 计算关系维度数据
 */
function calculateRelationshipDimensions(
  contact: Contact,
  communications: Communication[],
  events: BusinessEvent[]
): RelationshipDimension[] {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);

  // 沟通频率 (0-100)
  const recentComms = communications.filter(c => new Date(c.date) > thirtyDaysAgo);
  const communicationFrequency = Math.min(100, recentComms.length * 10);

  // 沟通质量 (基于沟通类型和内容长度)
  const qualityScore = communications.reduce((score, comm) => {
    let typeScore = 0;
    switch (comm.type) {
      case 'meeting': typeScore = 30; break;
      case 'phone': typeScore = 25; break;
      case 'email': typeScore = 20; break;
      case 'message': typeScore = 15; break;
      default: typeScore = 10;
    }
    const lengthScore = Math.min(20, comm.content.length / 10);
    return score + typeScore + lengthScore;
  }, 0);
  const communicationQuality = Math.min(100, qualityScore / communications.length || 0);

  // 互动深度 (基于活动参与)
  const sharedEvents = events.filter(e => 
    e.participants.some(p => p.contactId === contact.id)
  );
  const interactionDepth = Math.min(100, sharedEvents.length * 20);

  // 关系稳定性 (基于时间跨度和一致性)
  const daysSinceFirstContact = Math.floor(
    (now.getTime() - new Date(contact.createdAt).getTime()) / (1000 * 60 * 60 * 24)
  );
  const relationshipStability = Math.min(100, (daysSinceFirstContact / 365) * 50 + 
    (communications.length > 0 ? 50 : 0));

  // 商业价值 (基于联系人重要性和合作潜力)
  let businessValue = 30; // 基础分
  if (contact.isImportant) businessValue += 30;
  if (contact.company) businessValue += 20;
  if (contact.position?.includes('CEO') || contact.position?.includes('总')) businessValue += 20;
  businessValue = Math.min(100, businessValue);

  // 响应活跃度 (基于最近的互动)
  const recentActivity = communications.filter(c => new Date(c.date) > ninetyDaysAgo);
  const responsiveness = Math.min(100, recentActivity.length * 15);

  return [
    {
      name: '沟通频率',
      value: communicationFrequency,
      color: '#3B82F6',
      description: '最近30天的沟通次数',
    },
    {
      name: '沟通质量',
      value: Math.round(communicationQuality),
      color: '#10B981',
      description: '沟通方式和内容的质量',
    },
    {
      name: '互动深度',
      value: interactionDepth,
      color: '#8B5CF6',
      description: '共同参与活动的程度',
    },
    {
      name: '关系稳定性',
      value: Math.round(relationshipStability),
      color: '#F59E0B',
      description: '关系建立时间和持续性',
    },
    {
      name: '商业价值',
      value: businessValue,
      color: '#EF4444',
      description: '联系人的商业重要性',
    },
    {
      name: '响应活跃度',
      value: responsiveness,
      color: '#06B6D4',
      description: '最近90天的互动活跃度',
    },
  ];
}

export default RelationshipStrengthChart;
