/**
 * AI解析服务
 * 用于解析联系人信息文本并提取结构化数据
 */

export interface ParsedContactInfo {
  name?: string;
  company?: string;
  position?: string;
  phones?: string[];
  emails?: string[];
  address?: string;
  website?: string;
  socialLinks?: {
    platform: string;
    url: string;
  }[];
  notes?: string;
  confidence: number; // 解析置信度 0-1
}

export interface AIParsingOptions {
  language?: 'zh' | 'en' | 'auto';
  includeNotes?: boolean;
  strictMode?: boolean; // 严格模式，只返回高置信度结果
}

class AIParsingService {
  private apiKey: string | null = null;
  private baseURL = 'https://openrouter.ai/api/v1';

  constructor() {
    // TODO: 从环境变量或配置中获取API密钥
    this.apiKey = process.env.EXPO_PUBLIC_OPENROUTER_API_KEY || null;
  }

  /**
   * 设置API密钥
   */
  setApiKey(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * 解析联系人信息文本
   */
  async parseContactInfo(
    text: string, 
    options: AIParsingOptions = {}
  ): Promise<ParsedContactInfo> {
    if (!this.apiKey) {
      throw new Error('AI API密钥未配置');
    }

    if (!text.trim()) {
      throw new Error('输入文本不能为空');
    }

    try {
      const prompt = this.buildParsingPrompt(text, options);
      const response = await this.callAIAPI(prompt);
      const parsed = this.parseAIResponse(response);
      
      return {
        ...parsed,
        confidence: this.calculateConfidence(parsed, text),
      };
    } catch (error) {
      console.error('AI解析失败:', error);
      throw new Error('AI解析服务暂时不可用，请稍后重试');
    }
  }

  /**
   * 构建解析提示词
   */
  private buildParsingPrompt(text: string, options: AIParsingOptions): string {
    const language = options.language || 'auto';
    const includeNotes = options.includeNotes !== false;

    return `
请从以下文本中提取联系人信息，并以JSON格式返回。如果某些信息不存在，请不要包含该字段。

文本内容：
"""
${text}
"""

请提取以下信息：
- name: 姓名（完整姓名）
- company: 公司名称
- position: 职位/头衔
- phones: 电话号码数组（包括手机、座机等）
- emails: 邮箱地址数组
- address: 地址信息
- website: 网站地址
- socialLinks: 社交媒体链接数组，格式为 [{platform: "平台名", url: "链接"}]
${includeNotes ? '- notes: 其他备注信息' : ''}

要求：
1. 只返回JSON格式，不要包含其他文字
2. 电话号码请保持原格式，不要添加或删除符号
3. 邮箱地址必须是有效格式
4. 如果文本是${language === 'zh' ? '中文' : language === 'en' ? '英文' : '中英文混合'}，请正确识别
5. 对于不确定的信息，宁可不提取也不要猜测

示例输出：
{
  "name": "张三",
  "company": "科技有限公司",
  "position": "产品经理",
  "phones": ["+86 138 0013 8000"],
  "emails": ["<EMAIL>"],
  "address": "北京市朝阳区xxx街道"
}
`;
  }

  /**
   * 调用AI API
   */
  private async callAIAPI(prompt: string): Promise<string> {
    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://relationhub.app',
        'X-Title': 'RelationHub',
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3-haiku', // 使用性价比较高的模型
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 1000,
        temperature: 0.1, // 低温度确保结果稳定
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`AI API调用失败: ${response.status} ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  /**
   * 解析AI响应
   */
  private parseAIResponse(response: string): Partial<ParsedContactInfo> {
    try {
      // 尝试提取JSON部分
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('AI响应中未找到有效的JSON');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // 验证和清理数据
      return this.validateAndCleanData(parsed);
    } catch (error) {
      console.error('解析AI响应失败:', error);
      throw new Error('AI响应格式无效');
    }
  }

  /**
   * 验证和清理解析数据
   */
  private validateAndCleanData(data: any): Partial<ParsedContactInfo> {
    const cleaned: Partial<ParsedContactInfo> = {};

    // 验证姓名
    if (data.name && typeof data.name === 'string' && data.name.trim()) {
      cleaned.name = data.name.trim();
    }

    // 验证公司
    if (data.company && typeof data.company === 'string' && data.company.trim()) {
      cleaned.company = data.company.trim();
    }

    // 验证职位
    if (data.position && typeof data.position === 'string' && data.position.trim()) {
      cleaned.position = data.position.trim();
    }

    // 验证电话号码
    if (data.phones && Array.isArray(data.phones)) {
      const validPhones = data.phones
        .filter((phone: any) => typeof phone === 'string' && phone.trim())
        .map((phone: string) => phone.trim());
      if (validPhones.length > 0) {
        cleaned.phones = validPhones;
      }
    }

    // 验证邮箱
    if (data.emails && Array.isArray(data.emails)) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const validEmails = data.emails
        .filter((email: any) => typeof email === 'string' && emailRegex.test(email.trim()))
        .map((email: string) => email.trim().toLowerCase());
      if (validEmails.length > 0) {
        cleaned.emails = validEmails;
      }
    }

    // 验证地址
    if (data.address && typeof data.address === 'string' && data.address.trim()) {
      cleaned.address = data.address.trim();
    }

    // 验证网站
    if (data.website && typeof data.website === 'string' && data.website.trim()) {
      cleaned.website = data.website.trim();
    }

    // 验证社交链接
    if (data.socialLinks && Array.isArray(data.socialLinks)) {
      const validSocialLinks = data.socialLinks
        .filter((link: any) => 
          link && 
          typeof link.platform === 'string' && 
          typeof link.url === 'string' &&
          link.platform.trim() && 
          link.url.trim()
        )
        .map((link: any) => ({
          platform: link.platform.trim(),
          url: link.url.trim(),
        }));
      if (validSocialLinks.length > 0) {
        cleaned.socialLinks = validSocialLinks;
      }
    }

    // 验证备注
    if (data.notes && typeof data.notes === 'string' && data.notes.trim()) {
      cleaned.notes = data.notes.trim();
    }

    return cleaned;
  }

  /**
   * 计算解析置信度
   */
  private calculateConfidence(parsed: Partial<ParsedContactInfo>, originalText: string): number {
    let score = 0;
    let maxScore = 0;

    // 姓名权重最高
    maxScore += 30;
    if (parsed.name) {
      score += 30;
    }

    // 联系方式权重较高
    maxScore += 25;
    if (parsed.phones && parsed.phones.length > 0) {
      score += 15;
    }
    if (parsed.emails && parsed.emails.length > 0) {
      score += 10;
    }

    // 公司和职位
    maxScore += 20;
    if (parsed.company) {
      score += 10;
    }
    if (parsed.position) {
      score += 10;
    }

    // 其他信息
    maxScore += 25;
    if (parsed.address) score += 8;
    if (parsed.website) score += 7;
    if (parsed.socialLinks && parsed.socialLinks.length > 0) score += 5;
    if (parsed.notes) score += 5;

    return Math.min(score / maxScore, 1);
  }

  /**
   * 批量解析多个文本
   */
  async parseMultipleTexts(
    texts: string[], 
    options: AIParsingOptions = {}
  ): Promise<ParsedContactInfo[]> {
    const results = await Promise.allSettled(
      texts.map(text => this.parseContactInfo(text, options))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        console.error(`解析第${index + 1}个文本失败:`, result.reason);
        return {
          confidence: 0,
          notes: `解析失败: ${result.reason.message}`,
        };
      }
    });
  }
}

// 导出单例实例
export const aiParsingService = new AIParsingService();
export default aiParsingService;
