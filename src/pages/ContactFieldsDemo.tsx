import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Button,
  ButtonText,
  ButtonIcon,
  ScrollView,
  Divider,
  Badge,
  BadgeText,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';
import { Plus, Users, Settings, Eye } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// Import components and stores
import { 
  ContactGroupManager, 
  ContactGroupSelector, 
  EnhancedContactForm 
} from '../components/Contacts';
import { 
  useContactsStore, 
  useContactGroupsStore, 
  useContactGroupsSelectors 
} from '../store';
import { ErrorBoundary } from '../components/ui';
import { usePerformanceMonitor } from '../hooks';
import { RootStackParamList } from '../types';

type ContactFieldsDemoNavigationProp = NativeStackNavigationProp<RootStackParamList>;

/**
 * 联系人字段和分组功能演示页面
 * 展示新增的联系人字段和分组管理功能
 */
const ContactFieldsDemo: React.FC = () => {
  const navigation = useNavigation<ContactFieldsDemoNavigationProp>();
  const toast = useToast();
  
  // Performance monitoring
  usePerformanceMonitor('ContactFieldsDemo');
  
  // State
  const [showGroupManager, setShowGroupManager] = useState(false);
  const [showGroupSelector, setShowGroupSelector] = useState(false);
  const [showContactForm, setShowContactForm] = useState(false);
  
  // Store hooks - 临时注释掉store使用，避免无限循环
  // const { contacts, addContact } = useContactsStore();
  // const { initializeDefaultGroups } = useContactGroupsStore();
  // const groups = useContactGroupsSelectors.useGroups();

  // 使用静态数据进行测试
  const contacts = [];
  const addContact = () => {};
  const initializeDefaultGroups = () => {};
  const groups = [];

  // Initialize default groups on mount - 临时注释掉避免无限循环
  // useEffect(() => {
  //   initializeDefaultGroups();
  // }, [initializeDefaultGroups]);
  
  // Demo data
  const demoStats = {
    totalContacts: contacts.length,
    totalGroups: groups.length,
    favoriteContacts: contacts.filter(c => c.isFavorite).length,
    recentContacts: contacts.filter(c => {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return new Date(c.createdAt) > weekAgo;
    }).length,
  };
  
  const handleCreateDemoContact = () => {
    const demoContact = {
      name: '张三',
      firstName: '三',
      lastName: '张',
      company: '示例科技有限公司',
      position: '高级软件工程师',
      email: '<EMAIL>',
      phone: '+86 138 0013 8000',
      notes: '这是一个演示联系人，展示了新的字段功能。',
      tags: ['同事', '技术'],
      relationshipType: 'colleague' as const,
      priority: 'high' as const,
      isFavorite: true,
      groupIds: groups.filter(g => g.name === '工作').map(g => g.id),
    };
    
    addContact(demoContact);
    
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={id} action="success" variant="accent">
          <ToastTitle>演示联系人已创建</ToastTitle>
          <ToastDescription>
            已创建一个包含新字段的演示联系人
          </ToastDescription>
        </Toast>
      ),
    });
  };
  
  const handleGroupSelect = (groupId: string | null) => {
    setShowGroupSelector(false);
    
    const selectedGroup = groups.find(g => g.id === groupId);
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={id} action="info" variant="accent">
          <ToastTitle>分组已选择</ToastTitle>
          <ToastDescription>
            已选择分组: {selectedGroup?.name || '全部联系人'}
          </ToastDescription>
        </Toast>
      ),
    });
  };
  
  const handleSaveContact = (contactData: any) => {
    addContact(contactData);
    setShowContactForm(false);
    
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={id} action="success" variant="accent">
          <ToastTitle>联系人已保存</ToastTitle>
          <ToastDescription>
            新联系人已成功保存到通讯录
          </ToastDescription>
        </Toast>
      ),
    });
  };

  return (
    <ErrorBoundary>
      <Box flex={1} bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark900' } }}>
        <ScrollView flex={1} p="$4">
          <VStack space="lg">
            {/* 页面标题 */}
            <VStack space="sm">
              <Heading size="xl" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                联系人功能演示
              </Heading>
              <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                体验新增的联系人字段和分组管理功能
              </Text>
            </VStack>

            {/* 统计信息 */}
            <VStack space="md">
              <Text fontWeight="$bold" color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                当前统计
              </Text>
              <HStack space="md" flexWrap="wrap">
                <Badge size="lg" variant="outline" action="info">
                  <BadgeText>联系人: {demoStats.totalContacts}</BadgeText>
                </Badge>
                <Badge size="lg" variant="outline" action="success">
                  <BadgeText>分组: {demoStats.totalGroups}</BadgeText>
                </Badge>
                <Badge size="lg" variant="outline" action="warning">
                  <BadgeText>收藏: {demoStats.favoriteContacts}</BadgeText>
                </Badge>
                <Badge size="lg" variant="outline" action="error">
                  <BadgeText>最近: {demoStats.recentContacts}</BadgeText>
                </Badge>
              </HStack>
            </VStack>

            <Divider />

            {/* 功能演示按钮 */}
            <VStack space="md">
              <Text fontWeight="$bold" color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                功能演示
              </Text>
              
              <VStack space="sm">
                <Button onPress={() => setShowContactForm(true)} variant="solid" action="primary">
                  <ButtonIcon as={Plus} />
                  <ButtonText>创建增强联系人</ButtonText>
                </Button>
                
                <Button onPress={handleCreateDemoContact} variant="outline" action="secondary">
                  <ButtonIcon as={Users} />
                  <ButtonText>创建演示联系人</ButtonText>
                </Button>
                
                <Button onPress={() => setShowGroupManager(true)} variant="outline" action="secondary">
                  <ButtonIcon as={Settings} />
                  <ButtonText>管理联系人分组</ButtonText>
                </Button>
                
                <Button onPress={() => setShowGroupSelector(true)} variant="outline" action="secondary">
                  <ButtonIcon as={Eye} />
                  <ButtonText>查看分组选择器</ButtonText>
                </Button>
              </VStack>
            </VStack>

            <Divider />

            {/* 新功能说明 */}
            <VStack space="md">
              <Text fontWeight="$bold" color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                新增功能
              </Text>
              
              <VStack space="sm">
                <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  • 多个电话号码和邮箱地址
                </Text>
                <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  • 详细地址信息管理
                </Text>
                <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  • 社交媒体链接
                </Text>
                <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  • 重要日期提醒
                </Text>
                <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  • 自定义字段
                </Text>
                <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  • 关系类型和优先级
                </Text>
                <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  • 联系人分组管理
                </Text>
                <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  • 交互历史记录
                </Text>
              </VStack>
            </VStack>

            {/* 返回按钮 */}
            <Button onPress={() => navigation.goBack()} variant="outline" mt="$4">
              <ButtonText>返回</ButtonText>
            </Button>
          </VStack>
        </ScrollView>

        {/* 分组管理模态框 */}
        <ContactGroupManager
          visible={showGroupManager}
          onClose={() => setShowGroupManager(false)}
        />

        {/* 分组选择模态框 */}
        {showGroupSelector && (
          <Box
            position="absolute"
            top={0}
            left={0}
            right={0}
            bottom={0}
            bg="rgba(0,0,0,0.5)"
            justifyContent="center"
            alignItems="center"
            p="$4"
          >
            <Box
              bg="$backgroundLight0"
              borderRadius="$lg"
              maxWidth="$96"
              width="100%"
              sx={{ _dark: { bg: '$backgroundDark900' } }}
            >
              <ContactGroupSelector
                onGroupSelect={handleGroupSelect}
                onManageGroups={() => {
                  setShowGroupSelector(false);
                  setShowGroupManager(true);
                }}
                showContactCounts={true}
              />
              <Button
                onPress={() => setShowGroupSelector(false)}
                variant="outline"
                m="$4"
              >
                <ButtonText>关闭</ButtonText>
              </Button>
            </Box>
          </Box>
        )}

        {/* 增强联系人表单模态框 */}
        {showContactForm && (
          <Box
            position="absolute"
            top={0}
            left={0}
            right={0}
            bottom={0}
            bg="$backgroundLight0"
            sx={{ _dark: { bg: '$backgroundDark900' } }}
          >
            <EnhancedContactForm
              onSave={handleSaveContact}
              onCancel={() => setShowContactForm(false)}
            />
          </Box>
        )}
      </Box>
    </ErrorBoundary>
  );
};

export default ContactFieldsDemo;
