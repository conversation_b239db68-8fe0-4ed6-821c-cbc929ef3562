/**
 * 提醒系统状态管理
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  Reminder, 
  ReminderRule, 
  ReminderStats, 
  ReminderConfig,
  SmartReminderSuggestion,
  ReminderStatus,
  ReminderType 
} from '../types';
import { reminderEngine } from '../services/reminderEngine';
import { notificationService } from '../services/notificationService';

interface ReminderState {
  // 数据状态
  reminders: Reminder[];
  reminderRules: ReminderRule[];
  suggestions: SmartReminderSuggestion[];
  config: ReminderConfig;
  
  // UI状态
  isLoading: boolean;
  error: string | null;
  selectedReminder: Reminder | null;
  
  // 过滤和排序
  filterType: ReminderType | 'all';
  filterStatus: ReminderStatus | 'all';
  sortBy: 'date' | 'priority' | 'type';
  sortOrder: 'asc' | 'desc';
}

interface ReminderActions {
  // 提醒CRUD操作
  addReminder: (reminder: Omit<Reminder, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateReminder: (id: string, updates: Partial<Reminder>) => Promise<void>;
  deleteReminder: (id: string) => Promise<void>;
  completeReminder: (id: string) => Promise<void>;
  dismissReminder: (id: string) => Promise<void>;
  snoozeReminder: (id: string, snoozeUntil: Date) => Promise<void>;
  
  // 批量操作
  completeMultipleReminders: (ids: string[]) => Promise<void>;
  deleteMultipleReminders: (ids: string[]) => Promise<void>;
  
  // 提醒规则管理
  addReminderRule: (rule: Omit<ReminderRule, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateReminderRule: (id: string, updates: Partial<ReminderRule>) => void;
  deleteReminderRule: (id: string) => void;
  toggleReminderRule: (id: string) => void;
  
  // 智能建议
  generateSuggestions: (contacts: any[]) => Promise<void>;
  acceptSuggestion: (suggestionId: string) => Promise<void>;
  dismissSuggestion: (suggestionId: string) => void;
  
  // 自动生成提醒
  generateBirthdayReminders: (contacts: any[]) => Promise<void>;
  generateFollowUpReminders: (contacts: any[]) => Promise<void>;
  
  // 过滤和排序
  setFilterType: (type: ReminderType | 'all') => void;
  setFilterStatus: (status: ReminderStatus | 'all') => void;
  setSortBy: (sortBy: 'date' | 'priority' | 'type') => void;
  setSortOrder: (order: 'asc' | 'desc') => void;
  
  // 配置管理
  updateConfig: (config: Partial<ReminderConfig>) => void;
  
  // 统计信息
  getStats: () => ReminderStats;
  getUpcomingReminders: (days: number) => Reminder[];
  getOverdueReminders: () => Reminder[];
  
  // 选择和UI
  setSelectedReminder: (reminder: Reminder | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 初始化和清理
  initializeReminders: () => Promise<void>;
  clearAllReminders: () => void;
}

type ReminderStore = ReminderState & ReminderActions;

// 默认配置
const defaultConfig: ReminderConfig = {
  isEnabled: true,
  defaultReminderTime: '09:00',
  enablePushNotifications: true,
  enableEmailNotifications: false,
  quietHours: {
    enabled: true,
    startTime: '22:00',
    endTime: '08:00',
  },
  weekendMode: 'normal',
  maxDailyReminders: 10,
  autoSnoozeEnabled: false,
  autoSnoozeMinutes: 15,
};

// 辅助函数
const generateId = () => `reminder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const sortReminders = (
  reminders: Reminder[], 
  sortBy: 'date' | 'priority' | 'type', 
  sortOrder: 'asc' | 'desc'
): Reminder[] => {
  return [...reminders].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'date':
        comparison = a.triggerDate.getTime() - b.triggerDate.getTime();
        break;
      case 'priority':
        const priorityOrder = { low: 1, medium: 2, high: 3, urgent: 4 };
        comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
    }
    
    return sortOrder === 'asc' ? comparison : -comparison;
  });
};

const filterReminders = (
  reminders: Reminder[],
  filterType: ReminderType | 'all',
  filterStatus: ReminderStatus | 'all'
): Reminder[] => {
  return reminders.filter(reminder => {
    const typeMatch = filterType === 'all' || reminder.type === filterType;
    const statusMatch = filterStatus === 'all' || reminder.status === filterStatus;
    return typeMatch && statusMatch;
  });
};

export const useReminderStore = create<ReminderStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      reminders: [],
      reminderRules: [],
      suggestions: [],
      config: defaultConfig,
      isLoading: false,
      error: null,
      selectedReminder: null,
      filterType: 'all',
      filterStatus: 'all',
      sortBy: 'date',
      sortOrder: 'asc',

      // 提醒CRUD操作
      addReminder: async (reminderData) => {
        const reminder: Reminder = {
          ...reminderData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          reminders: [...state.reminders, reminder],
        }));

        // 调度通知
        if (state.config.enablePushNotifications) {
          await notificationService.scheduleReminderNotification(reminder);
        }
      },

      updateReminder: async (id, updates) => {
        set((state) => ({
          reminders: state.reminders.map(reminder =>
            reminder.id === id
              ? { ...reminder, ...updates, updatedAt: new Date() }
              : reminder
          ),
        }));
      },

      deleteReminder: async (id) => {
        set((state) => ({
          reminders: state.reminders.filter(reminder => reminder.id !== id),
          selectedReminder: state.selectedReminder?.id === id ? null : state.selectedReminder,
        }));
      },

      completeReminder: async (id) => {
        const { updateReminder } = get();
        await updateReminder(id, {
          status: 'completed',
          completedAt: new Date(),
        });
      },

      dismissReminder: async (id) => {
        const { updateReminder } = get();
        await updateReminder(id, {
          status: 'dismissed',
          dismissedAt: new Date(),
        });
      },

      snoozeReminder: async (id, snoozeUntil) => {
        const { updateReminder } = get();
        await updateReminder(id, {
          status: 'snoozed',
          snoozeUntil,
        });
      },

      // 批量操作
      completeMultipleReminders: async (ids) => {
        const { completeReminder } = get();
        for (const id of ids) {
          await completeReminder(id);
        }
      },

      deleteMultipleReminders: async (ids) => {
        const { deleteReminder } = get();
        for (const id of ids) {
          await deleteReminder(id);
        }
      },

      // 提醒规则管理
      addReminderRule: (ruleData) => {
        const rule: ReminderRule = {
          ...ruleData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          reminderRules: [...state.reminderRules, rule],
        }));
      },

      updateReminderRule: (id, updates) => {
        set((state) => ({
          reminderRules: state.reminderRules.map(rule =>
            rule.id === id
              ? { ...rule, ...updates, updatedAt: new Date() }
              : rule
          ),
        }));
      },

      deleteReminderRule: (id) => {
        set((state) => ({
          reminderRules: state.reminderRules.filter(rule => rule.id !== id),
        }));
      },

      toggleReminderRule: (id) => {
        const { updateReminderRule } = get();
        const rule = get().reminderRules.find(r => r.id === id);
        if (rule) {
          updateReminderRule(id, { isActive: !rule.isActive });
        }
      },

      // 智能建议
      generateSuggestions: async (contacts) => {
        set({ isLoading: true, error: null });
        try {
          const suggestions = reminderEngine.generateSmartSuggestions(contacts);
          set({ suggestions, isLoading: false });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '生成建议失败',
            isLoading: false 
          });
        }
      },

      acceptSuggestion: async (suggestionId) => {
        const { suggestions, addReminder } = get();
        const suggestion = suggestions.find(s => s.id === suggestionId);
        
        if (suggestion) {
          await addReminder({
            type: suggestion.type,
            title: `${suggestion.type === 'birthday' ? '🎂' : '💬'} ${suggestion.reason}`,
            description: suggestion.reason,
            contactId: suggestion.contactId,
            triggerDate: suggestion.suggestedDate,
            status: 'pending',
            priority: 'medium',
            metadata: {
              autoGenerated: true,
              fromSuggestion: true,
              suggestionId: suggestion.id,
              ...suggestion.metadata,
            },
          });

          // 标记建议为已接受
          set((state) => ({
            suggestions: state.suggestions.map(s =>
              s.id === suggestionId ? { ...s, isAccepted: true } : s
            ),
          }));
        }
      },

      dismissSuggestion: (suggestionId) => {
        set((state) => ({
          suggestions: state.suggestions.filter(s => s.id !== suggestionId),
        }));
      },

      // 自动生成提醒
      generateBirthdayReminders: async (contacts) => {
        set({ isLoading: true, error: null });
        try {
          const birthdayReminders = reminderEngine.generateBirthdayReminders(contacts);
          
          // 添加到现有提醒中，避免重复
          const existingReminderIds = new Set(get().reminders.map(r => r.id));
          const newReminders = birthdayReminders.filter(r => !existingReminderIds.has(r.id));
          
          set((state) => ({
            reminders: [...state.reminders, ...newReminders],
            isLoading: false,
          }));

          // 调度通知
          if (get().config.enablePushNotifications) {
            await notificationService.scheduleMultipleReminders(newReminders);
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '生成生日提醒失败',
            isLoading: false 
          });
        }
      },

      generateFollowUpReminders: async (contacts) => {
        set({ isLoading: true, error: null });
        try {
          const followUpReminders = reminderEngine.generateFollowUpReminders(contacts);
          
          const existingReminderIds = new Set(get().reminders.map(r => r.id));
          const newReminders = followUpReminders.filter(r => !existingReminderIds.has(r.id));
          
          set((state) => ({
            reminders: [...state.reminders, ...newReminders],
            isLoading: false,
          }));

          if (get().config.enablePushNotifications) {
            await notificationService.scheduleMultipleReminders(newReminders);
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '生成联系提醒失败',
            isLoading: false 
          });
        }
      },

      // 过滤和排序
      setFilterType: (type) => set({ filterType: type }),
      setFilterStatus: (status) => set({ filterStatus: status }),
      setSortBy: (sortBy) => set({ sortBy }),
      setSortOrder: (order) => set({ sortOrder: order }),

      // 配置管理
      updateConfig: (configUpdates) => {
        set((state) => ({
          config: { ...state.config, ...configUpdates },
        }));
      },

      // 统计信息
      getStats: () => {
        const { reminders } = get();
        const now = new Date();
        
        const stats: ReminderStats = {
          total: reminders.length,
          pending: reminders.filter(r => r.status === 'pending').length,
          triggered: reminders.filter(r => r.status === 'triggered').length,
          completed: reminders.filter(r => r.status === 'completed').length,
          dismissed: reminders.filter(r => r.status === 'dismissed').length,
          overdue: reminders.filter(r => 
            r.status === 'pending' && r.triggerDate < now
          ).length,
          byType: {
            birthday: reminders.filter(r => r.type === 'birthday').length,
            follow_up: reminders.filter(r => r.type === 'follow_up').length,
            meeting: reminders.filter(r => r.type === 'meeting').length,
            anniversary: reminders.filter(r => r.type === 'anniversary').length,
            task_deadline: reminders.filter(r => r.type === 'task_deadline').length,
            relationship_maintenance: reminders.filter(r => r.type === 'relationship_maintenance').length,
            custom: reminders.filter(r => r.type === 'custom').length,
          },
          byPriority: {
            low: reminders.filter(r => r.priority === 'low').length,
            medium: reminders.filter(r => r.priority === 'medium').length,
            high: reminders.filter(r => r.priority === 'high').length,
            urgent: reminders.filter(r => r.priority === 'urgent').length,
          },
          upcomingWeek: reminders.filter(r => {
            const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
            return r.status === 'pending' && r.triggerDate >= now && r.triggerDate <= weekFromNow;
          }).length,
          upcomingMonth: reminders.filter(r => {
            const monthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
            return r.status === 'pending' && r.triggerDate >= now && r.triggerDate <= monthFromNow;
          }).length,
        };
        
        return stats;
      },

      getUpcomingReminders: (days) => {
        const { reminders } = get();
        const now = new Date();
        const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
        
        return reminders.filter(r => 
          r.status === 'pending' && 
          r.triggerDate >= now && 
          r.triggerDate <= futureDate
        ).sort((a, b) => a.triggerDate.getTime() - b.triggerDate.getTime());
      },

      getOverdueReminders: () => {
        const { reminders } = get();
        const now = new Date();
        
        return reminders.filter(r => 
          r.status === 'pending' && r.triggerDate < now
        ).sort((a, b) => a.triggerDate.getTime() - b.triggerDate.getTime());
      },

      // 选择和UI
      setSelectedReminder: (reminder) => set({ selectedReminder: reminder }),
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),

      // 初始化和清理
      initializeReminders: async () => {
        set({ isLoading: true, error: null });
        try {
          // 初始化通知服务
          await notificationService.initialize();
          set({ isLoading: false });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '初始化提醒系统失败',
            isLoading: false 
          });
        }
      },

      // 活动集成功能
      createEventReminders: async (eventId, eventTitle, eventDate) => {
        const { addReminder } = get();
        const eventDateTime = new Date(eventDate);

        // 创建活动前1小时提醒
        const oneHourBefore = new Date(eventDateTime.getTime() - 60 * 60 * 1000);
        await addReminder({
          type: 'meeting',
          title: `📅 即将开始：${eventTitle}`,
          description: `您的活动"${eventTitle}"将在1小时后开始`,
          triggerDate: oneHourBefore,
          status: 'pending',
          priority: 'high',
          metadata: {
            eventId,
            eventTitle,
            reminderType: 'event_start',
            autoGenerated: true,
          },
        });

        // 创建活动前1天提醒
        const oneDayBefore = new Date(eventDateTime.getTime() - 24 * 60 * 60 * 1000);
        if (oneDayBefore > new Date()) {
          await addReminder({
            type: 'meeting',
            title: `📋 明日活动：${eventTitle}`,
            description: `提醒您明天有活动"${eventTitle}"，请做好准备`,
            triggerDate: oneDayBefore,
            status: 'pending',
            priority: 'medium',
            metadata: {
              eventId,
              eventTitle,
              reminderType: 'event_preparation',
              autoGenerated: true,
            },
          });
        }
      },

      createFollowUpReminders: async (eventId, participantIds, eventTitle) => {
        const { addReminder } = get();
        const now = new Date();

        // 为每个参与者创建跟进提醒
        for (const participantId of participantIds) {
          // 活动后24小时跟进提醒
          const followUpDate = new Date(now.getTime() + 24 * 60 * 60 * 1000);
          await addReminder({
            type: 'follow_up',
            title: `🤝 跟进联系人`,
            description: `建议跟进在"${eventTitle}"活动中认识的联系人`,
            contactId: participantId,
            triggerDate: followUpDate,
            status: 'pending',
            priority: 'medium',
            metadata: {
              eventId,
              eventTitle,
              participantId,
              reminderType: 'event_follow_up',
              autoGenerated: true,
            },
          });
        }
      },

      clearAllReminders: () => {
        set({
          reminders: [],
          suggestions: [],
          selectedReminder: null,
          error: null,
        });
      },
    }),
    {
      name: 'reminder-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        reminders: state.reminders,
        reminderRules: state.reminderRules,
        config: state.config,
      }),
    }
  )
);

// 导出选择器
export const useReminderSelectors = {
  useFilteredReminders: () => {
    const { reminders, filterType, filterStatus, sortBy, sortOrder } = useReminderStore();
    const filtered = filterReminders(reminders, filterType, filterStatus);
    return sortReminders(filtered, sortBy, sortOrder);
  },
  
  useReminderStats: () => {
    const getStats = useReminderStore(state => state.getStats);
    return getStats();
  },
  
  useUpcomingReminders: (days: number = 7) => {
    const getUpcomingReminders = useReminderStore(state => state.getUpcomingReminders);
    return getUpcomingReminders(days);
  },
  
  useOverdueReminders: () => {
    const getOverdueReminders = useReminderStore(state => state.getOverdueReminders);
    return getOverdueReminders();
  },
};
