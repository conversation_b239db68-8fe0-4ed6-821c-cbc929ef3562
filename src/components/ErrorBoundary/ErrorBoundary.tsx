import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  VStack,
  Text,
  Button,
  ButtonText,
  Icon,
  Box
} from '@gluestack-ui/themed';
import { AlertTriangle, RefreshCw } from 'lucide-react-native';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

/**
 * 错误边界组件
 * 捕获子组件中的JavaScript错误，记录错误并显示备用UI
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新state以显示错误UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误到错误报告服务
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // 调用自定义错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <VStack
          flex={1}
          justifyContent="center"
          alignItems="center"
          p="$6"
          bg="$backgroundLight0"
          sx={{ _dark: { bg: '$backgroundDark900' } }}
        >
          <Box
            w="$16"
            h="$16"
            bg="$error100"
            borderRadius="$full"
            justifyContent="center"
            alignItems="center"
            mb="$4"
            sx={{ _dark: { bg: '$error900' } }}
          >
            <Icon
              as={AlertTriangle}
              size="xl"
              color="$error600"
              sx={{ _dark: { color: '$error400' } }}
            />
          </Box>

          <Text
            fontSize="$xl"
            fontWeight="$semibold"
            color="$textLight900"
            textAlign="center"
            mb="$2"
            sx={{ _dark: { color: '$textDark100' } }}
          >
            Oops! Something went wrong
          </Text>

          <Text
            fontSize="$sm"
            color="$textLight600"
            textAlign="center"
            mb="$6"
            maxWidth="$80"
            sx={{ _dark: { color: '$textDark400' } }}
          >
            We're sorry for the inconvenience. Please try again or contact support if the problem persists.
          </Text>

          {__DEV__ && this.state.error && (
            <Box
              bg="$backgroundLight100"
              p="$4"
              borderRadius="$md"
              mb="$6"
              maxWidth="$96"
              sx={{ _dark: { bg: '$backgroundDark800' } }}
            >
              <Text
                fontSize="$xs"
                fontFamily="$mono"
                color="$textLight700"
                sx={{ _dark: { color: '$textDark300' } }}
              >
                {this.state.error.message}
              </Text>
            </Box>
          )}

          <Button
            onPress={this.handleRetry}
            variant="solid"
            action="primary"
            size="md"
          >
            <Icon as={RefreshCw} size="sm" mr="$2" />
            <ButtonText>Try Again</ButtonText>
          </Button>
        </VStack>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
