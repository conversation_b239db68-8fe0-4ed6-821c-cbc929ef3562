import React from 'react';
import { Pressable, HStack, VStack, Avatar, Text, Icon, AvatarFallbackText } from '@gluestack-ui/themed';
import { ChevronRight } from 'lucide-react-native';
import { Contact } from '../../../types';

interface ContactListItemProps {
  contact: Contact;
  onPress: () => void;
}

const ContactListItem: React.FC<ContactListItemProps> = ({ contact, onPress }) => {
  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('');
  };

  return (
    <Pressable
      onPress={onPress}
      p="$4"
      borderBottomWidth="$1"
      borderColor="$trueGray200"
      sx={{
        ':hover': {
          bg: '$trueGray100',
        },
        '_dark': {
          borderColor: '$trueGray700',
          ':hover': {
            bg: '$trueGray800',
          },
        },
      }}
    >
      <HStack space="md" alignItems="center">
        <Avatar size="md">
          <AvatarFallbackText>{getInitials(contact.name)}</AvatarFallbackText>
          {contact.avatar && <Avatar.Image source={{ uri: contact.avatar }} alt={contact.name} />}
        </Avatar>
        <VStack flex={1}>
          <Text size="md" fontWeight="$bold" color="$coolGray800" sx={{ _dark: { color: '$coolGray100' } }}>
            {contact.name}
          </Text>
          <Text size="sm" color="$coolGray600" sx={{ _dark: { color: '$coolGray400' } }}>
            {contact.position || contact.company}
          </Text>
        </VStack>
        <Icon as={ChevronRight} color="$coolGray400" />
      </HStack>
    </Pressable>
  );
};

export default ContactListItem;
