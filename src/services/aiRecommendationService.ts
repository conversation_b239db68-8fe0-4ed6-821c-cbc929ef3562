/**
 * AI智能推荐服务
 * 基于用户行为、关系网络和商务活动数据提供智能推荐
 */

import { Contact, BusinessEvent, ContactRelationship, Communication } from '../types';

// 推荐类型
export type RecommendationType = 
  | 'contact_connection'     // 联系人推荐
  | 'event_suggestion'       // 活动推荐
  | 'relationship_maintenance' // 关系维护提醒
  | 'business_opportunity'   // 商务机会
  | 'network_expansion'      // 网络扩展
  | 'follow_up_reminder';    // 跟进提醒

// 推荐项目接口
export interface RecommendationItem {
  id: string;
  type: RecommendationType;
  title: string;
  description: string;
  confidence: number; // 置信度 0-1
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionable: boolean; // 是否可执行
  metadata: Record<string, any>;
  createdAt: Date;
  expiresAt?: Date;
}

// 推荐上下文
export interface RecommendationContext {
  contacts: Contact[];
  events: BusinessEvent[];
  relationships: ContactRelationship[];
  communications: Communication[];
  userPreferences?: UserPreferences;
}

// 用户偏好设置
export interface UserPreferences {
  industries: string[];
  eventTypes: string[];
  relationshipGoals: string[];
  communicationFrequency: 'low' | 'medium' | 'high';
  networkingStyle: 'conservative' | 'moderate' | 'aggressive';
}

// 推荐引擎接口
export interface RecommendationEngine {
  generateRecommendations(context: RecommendationContext): Promise<RecommendationItem[]>;
  getRecommendationsByType(type: RecommendationType, context: RecommendationContext): Promise<RecommendationItem[]>;
  updateUserFeedback(recommendationId: string, feedback: 'positive' | 'negative' | 'neutral'): Promise<void>;
}

/**
 * 基于规则的推荐引擎实现
 */
export class RuleBasedRecommendationEngine implements RecommendationEngine {
  private feedbackHistory: Map<string, string> = new Map();

  async generateRecommendations(context: RecommendationContext): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = [];

    // 并行生成各类推荐
    const [
      contactRecommendations,
      eventRecommendations,
      maintenanceRecommendations,
      opportunityRecommendations,
      expansionRecommendations,
      followUpRecommendations
    ] = await Promise.all([
      this.generateContactRecommendations(context),
      this.generateEventRecommendations(context),
      this.generateMaintenanceRecommendations(context),
      this.generateOpportunityRecommendations(context),
      this.generateExpansionRecommendations(context),
      this.generateFollowUpRecommendations(context)
    ]);

    recommendations.push(
      ...contactRecommendations,
      ...eventRecommendations,
      ...maintenanceRecommendations,
      ...opportunityRecommendations,
      ...expansionRecommendations,
      ...followUpRecommendations
    );

    // 按优先级和置信度排序
    return this.sortRecommendations(recommendations);
  }

  async getRecommendationsByType(
    type: RecommendationType, 
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    switch (type) {
      case 'contact_connection':
        return this.generateContactRecommendations(context);
      case 'event_suggestion':
        return this.generateEventRecommendations(context);
      case 'relationship_maintenance':
        return this.generateMaintenanceRecommendations(context);
      case 'business_opportunity':
        return this.generateOpportunityRecommendations(context);
      case 'network_expansion':
        return this.generateExpansionRecommendations(context);
      case 'follow_up_reminder':
        return this.generateFollowUpRecommendations(context);
      default:
        return [];
    }
  }

  async updateUserFeedback(
    recommendationId: string, 
    feedback: 'positive' | 'negative' | 'neutral'
  ): Promise<void> {
    this.feedbackHistory.set(recommendationId, feedback);
    // TODO: 使用反馈数据优化推荐算法
  }

  // 生成联系人连接推荐
  private async generateContactRecommendations(context: RecommendationContext): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = [];
    const { contacts, relationships } = context;

    // 基于共同联系人的推荐
    const mutualConnectionRecommendations = this.findMutualConnections(contacts, relationships);
    recommendations.push(...mutualConnectionRecommendations);

    // 基于行业相关性的推荐
    const industryRecommendations = this.findIndustryConnections(contacts);
    recommendations.push(...industryRecommendations);

    return recommendations;
  }

  // 生成活动推荐
  private async generateEventRecommendations(context: RecommendationContext): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = [];
    const { contacts, events } = context;

    // 基于历史参与活动类型推荐
    const typeBasedRecommendations = this.recommendEventsByType(events);
    recommendations.push(...typeBasedRecommendations);

    // 基于联系人网络推荐
    const networkBasedRecommendations = this.recommendEventsByNetwork(contacts, events);
    recommendations.push(...networkBasedRecommendations);

    return recommendations;
  }

  // 生成关系维护推荐
  private async generateMaintenanceRecommendations(context: RecommendationContext): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = [];
    const { contacts, communications } = context;

    // 找出长期未联系的重要联系人
    const dormantContacts = this.findDormantImportantContacts(contacts, communications);
    
    dormantContacts.forEach(contact => {
      const daysSinceLastContact = this.getDaysSinceLastContact(contact.id, communications);
      
      recommendations.push({
        id: `maintenance_${contact.id}_${Date.now()}`,
        type: 'relationship_maintenance',
        title: `建议联系 ${contact.name}`,
        description: `您已经 ${daysSinceLastContact} 天没有联系 ${contact.name} 了，建议主动联系维护关系`,
        confidence: this.calculateMaintenanceConfidence(contact, daysSinceLastContact),
        priority: daysSinceLastContact > 90 ? 'high' : daysSinceLastContact > 60 ? 'medium' : 'low',
        actionable: true,
        metadata: {
          contactId: contact.id,
          daysSinceLastContact,
          suggestedActions: ['发送问候消息', '邀请喝咖啡', '分享有价值信息']
        },
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后过期
      });
    });

    return recommendations;
  }

  // 生成商务机会推荐
  private async generateOpportunityRecommendations(context: RecommendationContext): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = [];
    const { contacts, events, relationships } = context;

    // 基于最近活动发现机会
    const recentEventOpportunities = this.findOpportunitiesFromRecentEvents(events, contacts);
    recommendations.push(...recentEventOpportunities);

    // 基于关系网络发现机会
    const networkOpportunities = this.findNetworkOpportunities(relationships, contacts);
    recommendations.push(...networkOpportunities);

    return recommendations;
  }

  // 生成网络扩展推荐
  private async generateExpansionRecommendations(context: RecommendationContext): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = [];
    const { contacts } = context;

    // 分析网络缺口
    const networkGaps = this.analyzeNetworkGaps(contacts);
    
    networkGaps.forEach(gap => {
      recommendations.push({
        id: `expansion_${gap.industry}_${Date.now()}`,
        type: 'network_expansion',
        title: `扩展${gap.industry}行业网络`,
        description: `您在${gap.industry}行业的联系人较少，建议参加相关活动或寻找引荐`,
        confidence: gap.confidence,
        priority: gap.priority,
        actionable: true,
        metadata: {
          industry: gap.industry,
          currentCount: gap.currentCount,
          suggestedTarget: gap.suggestedTarget,
          suggestedActions: gap.suggestedActions
        },
        createdAt: new Date()
      });
    });

    return recommendations;
  }

  // 生成跟进提醒推荐
  private async generateFollowUpRecommendations(context: RecommendationContext): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = [];
    const { events } = context;

    // 找出需要跟进的活动
    const eventsNeedingFollowUp = events.filter(event => 
      event.status === 'completed' && 
      event.participants.some(p => p.followUpRequired) &&
      this.getDaysSinceEvent(event) <= 7 // 活动结束后7天内
    );

    eventsNeedingFollowUp.forEach(event => {
      const participantsNeedingFollowUp = event.participants.filter(p => p.followUpRequired);
      
      participantsNeedingFollowUp.forEach(participant => {
        recommendations.push({
          id: `followup_${event.id}_${participant.contactId}_${Date.now()}`,
          type: 'follow_up_reminder',
          title: `跟进 ${participant.name}`,
          description: `在"${event.title}"活动中与 ${participant.name} 有良好互动，建议及时跟进`,
          confidence: 0.8,
          priority: 'medium',
          actionable: true,
          metadata: {
            eventId: event.id,
            contactId: participant.contactId,
            eventTitle: event.title,
            interactionLevel: participant.interactionLevel,
            suggestedActions: this.getSuggestedFollowUpActions(participant)
          },
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3天后过期
        });
      });
    });

    return recommendations;
  }

  // 辅助方法
  private findMutualConnections(contacts: Contact[], relationships: ContactRelationship[]): RecommendationItem[] {
    // TODO: 实现基于共同联系人的推荐逻辑
    return [];
  }

  private findIndustryConnections(contacts: Contact[]): RecommendationItem[] {
    // TODO: 实现基于行业相关性的推荐逻辑
    return [];
  }

  private recommendEventsByType(events: BusinessEvent[]): RecommendationItem[] {
    // TODO: 实现基于活动类型的推荐逻辑
    return [];
  }

  private recommendEventsByNetwork(contacts: Contact[], events: BusinessEvent[]): RecommendationItem[] {
    // TODO: 实现基于网络的活动推荐逻辑
    return [];
  }

  private findDormantImportantContacts(contacts: Contact[], communications: Communication[]): Contact[] {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    return contacts.filter(contact => {
      // 只考虑重要联系人
      if (contact.priority !== 'high' && contact.priority !== 'critical') {
        return false;
      }

      // 检查最近30天是否有沟通
      const recentCommunications = communications.filter(comm => 
        comm.primaryContactId === contact.id && 
        new Date(comm.date) > thirtyDaysAgo
      );

      return recentCommunications.length === 0;
    });
  }

  private getDaysSinceLastContact(contactId: string, communications: Communication[]): number {
    const contactCommunications = communications
      .filter(comm => comm.primaryContactId === contactId)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    if (contactCommunications.length === 0) {
      return 999; // 从未联系
    }

    const lastContactDate = new Date(contactCommunications[0].date);
    const now = new Date();
    return Math.floor((now.getTime() - lastContactDate.getTime()) / (24 * 60 * 60 * 1000));
  }

  private calculateMaintenanceConfidence(contact: Contact, daysSinceLastContact: number): number {
    // 基于联系人重要性和时间间隔计算置信度
    let confidence = 0.5;

    // 重要性加权
    switch (contact.priority) {
      case 'critical':
        confidence += 0.4;
        break;
      case 'high':
        confidence += 0.3;
        break;
      case 'medium':
        confidence += 0.1;
        break;
    }

    // 时间间隔加权
    if (daysSinceLastContact > 90) {
      confidence += 0.3;
    } else if (daysSinceLastContact > 60) {
      confidence += 0.2;
    } else if (daysSinceLastContact > 30) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  private findOpportunitiesFromRecentEvents(events: BusinessEvent[], contacts: Contact[]): RecommendationItem[] {
    // TODO: 实现基于最近活动的机会发现逻辑
    return [];
  }

  private findNetworkOpportunities(relationships: ContactRelationship[], contacts: Contact[]): RecommendationItem[] {
    // TODO: 实现基于关系网络的机会发现逻辑
    return [];
  }

  private analyzeNetworkGaps(contacts: Contact[]): Array<{
    industry: string;
    currentCount: number;
    suggestedTarget: number;
    confidence: number;
    priority: 'low' | 'medium' | 'high';
    suggestedActions: string[];
  }> {
    // 分析各行业联系人分布
    const industryCount = new Map<string, number>();
    
    contacts.forEach(contact => {
      if (contact.industry) {
        industryCount.set(contact.industry, (industryCount.get(contact.industry) || 0) + 1);
      }
    });

    const gaps: Array<{
      industry: string;
      currentCount: number;
      suggestedTarget: number;
      confidence: number;
      priority: 'low' | 'medium' | 'high';
      suggestedActions: string[];
    }> = [];

    // 定义重要行业的目标联系人数量
    const targetIndustries = ['科技', '金融', '医疗', '教育', '制造业'];
    
    targetIndustries.forEach(industry => {
      const currentCount = industryCount.get(industry) || 0;
      const suggestedTarget = 10; // 建议每个重要行业至少10个联系人

      if (currentCount < suggestedTarget) {
        gaps.push({
          industry,
          currentCount,
          suggestedTarget,
          confidence: 0.7,
          priority: currentCount === 0 ? 'high' : currentCount < 3 ? 'medium' : 'low',
          suggestedActions: [
            `参加${industry}行业会议`,
            `寻找${industry}行业引荐`,
            `加入${industry}专业社群`
          ]
        });
      }
    });

    return gaps;
  }

  private getDaysSinceEvent(event: BusinessEvent): number {
    const now = new Date();
    const eventDate = new Date(event.date);
    return Math.floor((now.getTime() - eventDate.getTime()) / (24 * 60 * 60 * 1000));
  }

  private getSuggestedFollowUpActions(participant: any): string[] {
    const actions = ['发送感谢消息'];

    if (participant.businessCardExchanged) {
      actions.push('添加到LinkedIn');
    }

    switch (participant.interactionLevel) {
      case 'extensive':
        actions.push('安排一对一会面', '分享相关资源');
        break;
      case 'moderate':
        actions.push('发送相关信息', '邀请参加下次活动');
        break;
      case 'brief':
        actions.push('发送简短问候');
        break;
    }

    return actions;
  }

  private sortRecommendations(recommendations: RecommendationItem[]): RecommendationItem[] {
    return recommendations.sort((a, b) => {
      // 首先按优先级排序
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      
      if (priorityDiff !== 0) {
        return priorityDiff;
      }

      // 然后按置信度排序
      return b.confidence - a.confidence;
    });
  }
}

// 导出默认推荐引擎实例
export const recommendationEngine = new RuleBasedRecommendationEngine();
