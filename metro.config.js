const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Disable package exports that cause issues with Gluestack UI
config.resolver.unstable_enablePackageExports = false;
config.resolver.preferNativePlatform = true;

// Set explicit resolver main fields prioritizing react-native
config.resolver.resolverMainFields = ['react-native', 'browser', 'module', 'main'];

// Add explicit node modules paths to ensure proper resolution
config.resolver.nodeModulesPaths = [
  path.resolve(__dirname, 'node_modules'),
];

// Ensure all common file extensions are handled, including jsx
config.resolver.sourceExts = process.env.EXPO_DEBUG
  ? ['js', 'jsx', 'json', 'ts', 'tsx', 'cjs', 'mjs']
  : ['js', 'jsx', 'json', 'ts', 'tsx', 'cjs', 'mjs'];

// Add platform extensions
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Add asset extensions  
config.resolver.assetExts = [
  ...config.resolver.assetExts,
  'png', 'jpg', 'jpeg', 'gif', 'svg'
];

module.exports = withNativeWind(config, {
  input: "./global.css"
});