import React from 'react';

import './src/i18n'; // 导入 i18n 配置文件以初始化
import { StyleSheet } from 'react-native';
import { SafeAreaProvider } from "react-native-safe-area-context"
import { GluestackUIProvider } from '@gluestack-ui/themed';
import { config } from './gluestack-ui.config'; // v2 主题配置文件
import { Toaster } from 'sonner-native';
import AppNavigator from './src/navigation/AppNavigator';
import { AlertProvider } from './src/context/AlertContext';



export default function App() {
  return (
    <GluestackUIProvider config={config}>
      <SafeAreaProvider>
        <AlertProvider>
          {/* <Toaster /> */}
          <AppNavigator />
        </AlertProvider>
      </SafeAreaProvider>
    </GluestackUIProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    userSelect: "none"
  }
});