import React from 'react';
import {
  HStack,
  VStack,
  Text,
  Pressable,
  Icon,
  Switch,
  Box
} from '@gluestack-ui/themed';
import { ChevronRight } from 'lucide-react-native';

interface SettingItemProps {
  icon: React.ComponentType<any>;
  iconColor?: string;
  title: string;
  description?: string;
  value?: boolean | string;
  toggle?: boolean;
  onValueChange?: (newValue: boolean) => void;
  onPress?: () => void;
  showChevron?: boolean;
}

/**
 * 设置项组件
 * 支持开关、点击操作和显示值
 */
const SettingItem: React.FC<SettingItemProps> = ({
  icon: IconComponent,
  iconColor = '$primary500',
  title,
  description,
  value,
  toggle = false,
  onValueChange,
  onPress,
  showChevron = false
}) => {
  const handlePress = () => {
    if (onPress) {
      onPress();
    }
  };

  return (
    <Pressable
      onPress={handlePress}
      bg="$backgroundLight0"
      px="$4"
      py="$3"
      flexDirection="row"
      alignItems="center"
      borderBottomWidth={1}
      borderBottomColor="$borderLight100"
      sx={{
        _dark: {
          bg: '$backgroundDark900',
          borderBottomColor: '$borderDark800'
        },
        _pressed: {
          bg: '$backgroundLight50',
          _dark: {
            bg: '$backgroundDark800'
          }
        }
      }}
    >
      {/* Icon Container */}
      <Box
        w="$9"
        h="$9"
        bg={iconColor}
        borderRadius="$lg"
        justifyContent="center"
        alignItems="center"
        mr="$3"
      >
        <IconComponent size={20} color="white" />
      </Box>

      {/* Content */}
      <VStack flex={1} space="xs">
        <Text 
          fontSize="$md" 
          fontWeight="$medium" 
          color="$textLight900"
          sx={{ _dark: { color: '$textDark100' } }}
        >
          {title}
        </Text>
        
        {description && (
          <Text 
            fontSize="$xs" 
            color="$textLight500"
            sx={{ _dark: { color: '$textDark400' } }}
          >
            {description}
          </Text>
        )}
      </VStack>

      {/* Right Side Content */}
      <HStack alignItems="center" space="sm">
        {/* Toggle Switch */}
        {toggle && (
          <Switch
            value={typeof value === 'boolean' ? value : false}
            onValueChange={onValueChange}
            size="sm"
            trackColor={{
              false: '$backgroundLight300',
              true: '$primary500'
            }}
            thumbColor={value ? '$primary500' : '$backgroundLight100'}
          />
        )}

        {/* Value Display */}
        {!toggle && typeof value === 'string' && (
          <Text 
            fontSize="$sm" 
            color="$textLight500"
            sx={{ _dark: { color: '$textDark400' } }}
          >
            {value}
          </Text>
        )}

        {/* Chevron */}
        {(showChevron || onPress) && !toggle && (
          <Icon 
            as={ChevronRight} 
            size="sm" 
            color="$textLight400"
            sx={{ _dark: { color: '$textDark600' } }}
          />
        )}
      </HStack>
    </Pressable>
  );
};

export default SettingItem;
