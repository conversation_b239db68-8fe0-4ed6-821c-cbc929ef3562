# RelationHub 重构任务清单 (Refactoring TODO)

本文档追踪 RelationHub 项目的主要重构任务，记录已完成和待办事项。

---

## ✅ 已完成 (Completed)

### 1. **UI 库迁移与统一**
- **Gluestack UI 基础设置**:
    - [x] 解决了 Gluestack UI 在 Metro bundler 中的模块解析问题 (`scripts/fix-gluestack-ui.js`)。
    - [x] 确保所有 Gluestack UI 组件在 Web 和原生平台上都能正常渲染。
- **图标库统一**:
    - [x] 将项目中所有图标库 (`@expo/vector-icons` 系列) **完全替换为 `lucide-react-native`**。
    - [x] 创建并使用了自动化迁移脚本 (`scripts/migrate-to-lucide.js`)。
    - [x] 解决了图标命名冲突和属性不兼容的问题。
- **样式修复**:
    - [x] **平台特定阴影 (Shadow)**: 修复了所有 `boxShadow` 样式警告，通过自动化脚本 (`scripts/fix-shadow-styles.js`) 实现了跨平台兼容的阴影样式。
    - [x] **颜色变量**: 修复了 `InteractionsTab.tsx` 中未定义的 `colors` 变量引用，暂时使用硬编码值并添加 `TODO` 注释。

### 2. **弹窗系统重构 (Alerts & Modals)**
- **React Native `Alert` 替换**:
    - [x] **全局扫描**: 扫描并定位了项目中所有的 `Alert.alert` 调用。
    - [x] **Toast 替换**: 在 `NetworkVisualScreen.tsx` 和 `ContactCreateScreen.tsx` 中，将简单的信息和错误提示替换为 Gluestack UI `Toast`。
- **自定义弹窗上下文 (`AlertContext`) 替换**:
    - [x] **分析上下文**: 识别出 `SettingsScreen.tsx` 中使用的 `AlertContext` 是一个自定义的弹窗封装。
    - [x] **Gluestack Modal 实现**: 在 `SettingsScreen.tsx` 中，成功用一个可复用的 Gluestack UI `Modal` 组件替换了原有的 `AlertContext` 逻辑。
    - [x] **复杂流程重构**: 重构了设置页面中的**注销 (Logout)**、**重置偏好 (Reset Preferences)** 和 **删除账户 (Delete Account)** 等具有破坏性操作的确认流程，包括一个**两步确认**的删除流程。
    - [x] **代码清理**: 移除了 `SettingsScreen.tsx` 中对 `AlertContext` 的依赖和 `Alert` 的导入。

### 3. **TypeScript 类型与 Lint 错误修复**
- **导航类型 (`React Navigation`)**:
    - [x] 修复了 `NetworkVisualScreen.tsx` 中的导航参数类型错误，更新了 `RootStackParamList`。
- **数据类型安全**:
    - [x] 为 `NetworkNode` 和 `industryColors` 等对象增加了严格的类型定义 (`IndustryKey`)，消除了不安全的属性访问。
- **运行时错误**:
    - [x] 修复了 `MeetingRecordScreen.tsx` 中缺失的 `StyleSheet` 和其他 React Native 组件的导入错误。

---

## ⏳ 待办事项 (To Do)

### 1. **样式与主题 (Styling & Theming)**
- **Gluestack UI 主题集成**:
    - [ ] **替换硬编码颜色**: 在 `SettingsScreen.tsx` (`iconColorMap`) 和 `InteractionsTab.tsx` 中，将所有硬编码的颜色值 (`#...`) 替换为 Gluestack UI 的主题 `tokens` (例如 `$colors.primary500`)。
    - [ ] **定义设计系统**: 根据 `docs/design_system_guide.md`，完善 Gluestack UI 的全局主题配置，包括字体、间距、圆角等。
    - [ ] **组件样式统一**: 审查并统一所有自定义 `StyleSheet` 样式，尽可能使用 Gluestack UI 的 `sx` 属性或内置样式属性，以减少自定义样式表的数量。

### 2. **组件重构 (Component Refactoring)**
- **`CustomAlertModal` 清理**:
    - [ ] `src/components/ui/CustomAlertModal.tsx` 组件现在已经不再被 `AlertContext` 使用。评估是否可以在项目中其他地方复用，或者直接**安全删除**该文件及其引用。
- **`AlertContext` 清理**:
    - [ ] `src/context/AlertContext.tsx` 已经不再被 `SettingsScreen.tsx` 使用。检查项目中是否还有其他地方依赖此上下文。如果没有，应**安全删除**该文件，并从 `App.tsx` (或全局 Provider) 中移除 `AlertProvider`。
- **大型组件拆分**:
    - [ ] `ContactCreateScreen.tsx` (超过 700 行) 和 `SettingsScreen.tsx` (超过 700 行) 是非常大的组件。根据 **单一职责原则**，考虑将它们拆分为更小的、可复用的子组件。
        - 例如，`SettingsScreen.tsx` 中的个人资料部分、存储部分和各个设置项可以拆分。

### 3. **测试与验证 (Testing & Verification)**
- **功能测试**:
    - [ ] **设置页面**: 手动测试注销、重置偏好和删除账户的模态框功能是否按预期工作。
    - [ ] **网络可视化页面**: 验证所有交互（节点点击、筛选、缩放）在重构后是否正常。
    - [ ] **联系人创建/编辑**: 测试图片选择、标签管理和保存功能是否正常。
- **跨平台验证**:
    - [ ] 在 iOS、Android 和 Web 三个平台上全面审查 UI，确保样式（特别是阴影、字体和布局）的一致性和正确性。

### 4. **文档更新 (Documentation)**
- **`README.md`**:
    - [ ] 更新项目的技术栈部分，明确说明项目现在使用 Gluestack UI 和 `lucide-react-native` 作为核心 UI 库。
    - [ ] 添加关于如何运行和使用自动化脚本（如 `fix-gluestack-ui.js`）的说明。

---

这份文档将作为后续开发的主要参考。
