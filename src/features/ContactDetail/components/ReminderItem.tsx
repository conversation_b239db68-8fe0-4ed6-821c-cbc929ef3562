import React from 'react';
import { Box, Text, Pressable, HStack, VStack, Icon } from '@gluestack-ui/themed';
import { CheckCircle2 } from 'lucide-react-native';
import { Reminder } from '../hooks/useContactDetail';

interface ReminderItemProps {
  reminder: Reminder;
  onComplete: (reminderId: string, reminderTitle: string) => void;
  onPress?: (reminder: Reminder) => void; // For handling item press for editing
}

const ReminderItem: React.FC<ReminderItemProps> = ({ reminder, onComplete, onPress }) => {
  const getPriorityColor = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high': return '$error600';
      case 'medium': return '$warning500';
      case 'low': return '$primary500';
      default: return '$backgroundLight400';
    }
  };

  return (
    <HStack 
      alignItems="center" 
      mb={'$3.5'} 
      py={'$2'} 
      borderBottomWidth={1} 
      borderBottomColor={'$backgroundLight200'}
    >
      <Pressable 
        flex={1} 
        onPress={() => onPress && onPress(reminder)} 
        disabled={!onPress}
      >
        <HStack alignItems="center">
          <Box 
            bg={getPriorityColor(reminder.priority)} 
            w={'$1'} 
            h={'$10'} 
            borderRadius={'$full'} 
            mr={'$3'} 
          />
          <VStack flex={1}>
            <Text fontSize={'$md'} fontWeight={'$medium'} color={'$textLight800'}>{reminder.title}</Text>
            <Text fontSize={'$xs'} color={'$textLight500'} mt={'$0.5'}>Due: {reminder.dueDate}</Text>
          </VStack>
        </HStack>
      </Pressable>
      <Pressable onPress={() => onComplete(reminder.id, reminder.title)} p={'$1.5'}>
        <Icon as={CheckCircle2} size="xl" color={'$success500'} />
      </Pressable>
    </HStack>
  );
};



export default ReminderItem;
