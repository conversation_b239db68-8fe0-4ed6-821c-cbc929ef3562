import React from 'react';
import { Tabs, TabsTab, TabsTabList, TabsTabTitle } from '@gluestack-ui/themed';

interface TabNavigatorProps {
  activeTab: string;
  setActiveTab: (tabName: string) => void;
}

const TABS = [
  { key: 'profile', title: 'Profile' },
  { key: 'interactions', title: 'Interactions' },
  { key: 'communications', title: 'Communications' },
  { key: 'analysis', title: 'Analysis' },
  { key: 'network', title: 'Network' },
];

const TabNavigator: React.FC<TabNavigatorProps> = ({ activeTab, setActiveTab }) => {
  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} bg="$white" pt={'$1'}>
      <TabsTabList>
        {TABS.map((tab) => (
          <TabsTab key={tab.key} value={tab.key}>
            <TabsTabTitle>{tab.title}</TabsTabTitle>
          </TabsTab>
        ))}
      </TabsTabList>
    </Tabs>
  );
};



export default TabNavigator;
