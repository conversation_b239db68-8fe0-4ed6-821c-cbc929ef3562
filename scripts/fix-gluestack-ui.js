#!/usr/bin/env node

/**
 * Postinstall script to fix Gluestack UI packages module resolution
 * This script fixes the missing file extensions in main/module fields
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Gluestack UI module resolution...');

// List of packages that need fixing
const packagesToFix = [
  '@gluestack-ui/accordion',
  '@gluestack-ui/actionsheet', 
  '@gluestack-ui/alert-dialog',
  '@gluestack-ui/alert',
  '@gluestack-ui/avatar',
  '@gluestack-ui/button',
  '@gluestack-ui/checkbox',
  '@gluestack-ui/divider',
  '@gluestack-ui/fab',
  '@gluestack-ui/form-control',
  '@gluestack-ui/hooks',
  '@gluestack-ui/icon',
  '@gluestack-ui/image',
  '@gluestack-ui/input',
  '@gluestack-ui/link',
  '@gluestack-ui/menu',
  '@gluestack-ui/modal',
  '@gluestack-ui/overlay',
  '@gluestack-ui/popover',
  '@gluestack-ui/pressable',
  '@gluestack-ui/progress',
  '@gluestack-ui/provider',
  '@gluestack-ui/radio',
  '@gluestack-ui/select',
  '@gluestack-ui/slider',
  '@gluestack-ui/spinner',
  '@gluestack-ui/switch',
  '@gluestack-ui/tabs',
  '@gluestack-ui/textarea',
  '@gluestack-ui/themed',
  '@gluestack-ui/toast',
  '@gluestack-ui/tooltip',
  '@gluestack-ui/transitions',
  '@gluestack-ui/utils'
];

let fixedCount = 0;

packagesToFix.forEach(packageName => {
  const packageJsonPath = path.join('node_modules', packageName, 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.log(`⏭️  ${packageName} - not found, skipping`);
    return;
  }

  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const packageDir = path.dirname(packageJsonPath);
    
    let needsUpdate = false;
    const updates = [];
    
    // Fix main field
    if (packageJson.main && !packageJson.main.includes('.')) {
      const possibleExtensions = ['.jsx', '.js'];
      
      for (const ext of possibleExtensions) {
        const fullPath = packageJson.main + ext;
        if (fs.existsSync(path.join(packageDir, fullPath))) {
          packageJson.main = fullPath;
          updates.push(`main: ${fullPath}`);
          needsUpdate = true;
          break;
        }
      }
    }
    
    // Fix module field
    if (packageJson.module && !packageJson.module.includes('.')) {
      const possibleExtensions = ['.jsx', '.js'];
      
      for (const ext of possibleExtensions) {
        const fullPath = packageJson.module + ext;
        if (fs.existsSync(path.join(packageDir, fullPath))) {
          packageJson.module = fullPath;
          updates.push(`module: ${fullPath}`);
          needsUpdate = true;
          break;
        }
      }
    }
    
    if (needsUpdate) {
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      fixedCount++;
      console.log(`✅ ${packageName} - fixed: ${updates.join(', ')}`);
    } else {
      console.log(`⏭️  ${packageName} - no fix needed`);
    }
  } catch (error) {
    console.error(`❌ Error fixing ${packageName}:`, error.message);
  }
});

console.log(`\n🎉 Fixed ${fixedCount} packages`);
console.log('✨ Gluestack UI module resolution should now work correctly!');
