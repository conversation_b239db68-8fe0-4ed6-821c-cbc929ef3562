{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "license": "0BSD", "private": true, "main": "index.ts", "scripts": {"start": "npx expo start", "android": "cross-env DARK_MODE=media npx expo run:android", "ios": "cross-env DARK_MODE=media npx expo run:ios", "web": "cross-env DARK_MODE=media npx expo start --web", "lint": "eslint .", "postinstall": "node scripts/fix-gluestack-ui.js"}, "dependencies": {"@expo/html-elements": "^0.4.2", "@expo/metro-runtime": "~3.1.3", "@gluestack-style/react": "^1.0.57", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.53", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/checkbox": "^0.1.39", "@gluestack-ui/config": "^1.0.0", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.28", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/link": "^0.1.29", "@gluestack-ui/menu": "^0.2.43", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/popover": "^0.1.49", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/select": "^0.1.31", "@gluestack-ui/slider": "^0.1.32", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.29", "@gluestack-ui/textarea": "^0.1.25", "@gluestack-ui/themed": "^1.1.73", "@gluestack-ui/toast": "^1.0.9", "@gluestack-ui/tooltip": "^0.1.44", "@legendapp/motion": "^2.4.0", "@react-native-community/datetimepicker": "7.7.0", "@react-native-picker/picker": "2.6.1", "@react-navigation/bottom-tabs": "^7.3.15", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@react-navigation/stack": "^7.3.3", "babel-plugin-module-resolver": "^5.0.2", "expo": "^50.0.0", "expo-image-picker": "~14.7.1", "expo-linear-gradient": "~12.7.2", "expo-localization": "~14.8.3", "i18next": "^23.11.5", "lucide-react-native": "^0.515.0", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^14.1.2", "react-native": "0.73.6", "react-native-css-interop": "^0.1.22", "react-native-dropdown-picker": "^5.4.6", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "react-native-svg-transformer": "^1.3.0", "react-native-web": "~0.19.9", "sonner-native": "^0.21.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@gluestack-ui/nativewind-utils": "^1.0.26", "@types/react": "~18.2.0", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^5.0.0", "globals": "^16.2.0", "jscodeshift": "^0.15.2", "nativewind": "^4.1.23", "patch-package": "^8.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.4.5"}}