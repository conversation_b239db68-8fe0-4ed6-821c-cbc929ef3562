#!/usr/bin/env node

/**
 * 检查并修复项目中所有使用 React Native API 但没有正确导入的文件
 * 包括: Platform, Alert, Dimensions, Linking, Keyboard, StatusBar 等
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 需要检查的 React Native API
const REACT_NATIVE_APIS = [
  'Platform',
  'Alert', 
  'Dimensions',
  'Linking',
  'Keyboard',
  'StatusBar'
];

// 检查文件是否使用了 React Native API 但没有导入
function checkAndFixReactNativeImports(content) {
  const usedAPIs = [];
  const missingAPIs = [];
  
  // 检查哪些 API 被使用了
  REACT_NATIVE_APIS.forEach(api => {
    const apiUsageRegex = new RegExp(`\\b${api}\\.(\\w+|OS)`, 'g');
    if (apiUsageRegex.test(content)) {
      usedAPIs.push(api);
      
      // 检查是否已经导入
      const importRegex = new RegExp(`import.*${api}.*from\\s+['"\`]react-native['"\`]`);
      if (!importRegex.test(content)) {
        missingAPIs.push(api);
      }
    }
  });
  
  if (missingAPIs.length === 0) {
    return { 
      content, 
      hasChanges: false, 
      usedAPIs, 
      missingAPIs: [] 
    };
  }
  
  // 需要添加缺失的 API 导入
  let modifiedContent = content;
  let hasChanges = false;
  
  // 查找 react-native 的导入语句并添加缺失的 API
  const reactNativeImportRegex = /(import\s+\{[^}]*)\}\s+from\s+['"`]react-native['"`]/;
  const match = modifiedContent.match(reactNativeImportRegex);
  
  if (match) {
    const importPart = match[1];
    const currentImports = importPart.replace(/import\s+\{/, '').split(',').map(s => s.trim());
    
    // 检查哪些 API 还没有导入
    const apisToAdd = missingAPIs.filter(api => 
      !currentImports.some(imp => imp.includes(api))
    );
    
    if (apisToAdd.length > 0) {
      // 添加缺失的 API 到导入列表
      const newImportList = currentImports.concat(apisToAdd).filter(Boolean);
      const newImport = `import {\n  ${newImportList.join(',\n  ')},\n} from 'react-native'`;
      modifiedContent = modifiedContent.replace(reactNativeImportRegex, newImport);
      hasChanges = true;
    }
  } else {
    // 没有找到 react-native 导入，需要创建一个新的导入语句
    // 在文件开始处添加导入（通常在其他导入之后）
    const importInsertRegex = /(import.*from\s+['"`][^'"`]*['"`];\s*\n)(?=\n|\s*export|\s*const|\s*function|\s*interface|\s*type)/;
    const insertMatch = modifiedContent.match(importInsertRegex);
    
    if (insertMatch) {
      const newImport = `import { ${missingAPIs.join(', ')} } from 'react-native';\n`;
      modifiedContent = modifiedContent.replace(insertMatch[0], insertMatch[0] + newImport);
      hasChanges = true;
    } else {
      console.warn(`警告：无法确定在哪里插入 react-native 导入语句`);
    }
  }
  
  return { 
    content: modifiedContent, 
    hasChanges, 
    usedAPIs,
    missingAPIs: hasChanges ? [] : missingAPIs 
  };
}

// 处理单个文件
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { 
      content: newContent, 
      hasChanges, 
      usedAPIs, 
      missingAPIs 
    } = checkAndFixReactNativeImports(content);
    
    if (hasChanges) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✅ 已修复: ${filePath} (添加: ${missingAPIs.join(', ')})`);
      return { fixed: true, usedAPIs, missingAPIs: [] };
    } else if (usedAPIs.length > 0) {
      console.log(`✓ 已正确: ${filePath} (使用: ${usedAPIs.join(', ')})`);
      return { fixed: false, usedAPIs, missingAPIs };
    }
    
    return { fixed: false, usedAPIs: [], missingAPIs: [] };
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return { fixed: false, usedAPIs: [], missingAPIs: [] };
  }
}

// 主函数
function main() {
  console.log('🔧 检查并修复项目中的 React Native API 导入问题...\n');
  console.log(`检查的 API: ${REACT_NATIVE_APIS.join(', ')}\n`);
  
  // 查找所有 TypeScript/JavaScript 文件
  const srcPattern = path.join(__dirname, '../src/**/*.{ts,tsx,js,jsx}');
  const files = glob.sync(srcPattern);
  
  let processedCount = 0;
  let modifiedCount = 0;
  let apiUsageCount = 0;
  
  const results = [];
  const allMissingAPIs = new Set();
  
  files.forEach(filePath => {
    processedCount++;
    const result = processFile(filePath);
    
    if (result.fixed) {
      modifiedCount++;
    }
    
    if (result.usedAPIs.length > 0) {
      apiUsageCount++;
      results.push({
        file: path.relative(path.join(__dirname, '..'), filePath),
        fixed: result.fixed,
        usedAPIs: result.usedAPIs,
        missingAPIs: result.missingAPIs
      });
      
      result.missingAPIs.forEach(api => allMissingAPIs.add(api));
    }
  });
  
  console.log(`\n📊 检查完成:`);
  console.log(`   - 扫描文件: ${processedCount}`);
  console.log(`   - 使用 React Native API 的文件: ${apiUsageCount}`);
  console.log(`   - 修复文件: ${modifiedCount}`);
  
  if (allMissingAPIs.size > 0) {
    console.log(`   - 发现缺失的 API: ${Array.from(allMissingAPIs).join(', ')}`);
  }
  
  if (results.length > 0) {
    console.log('\n📋 API 使用情况:');
    results.forEach(result => {
      const status = result.fixed ? '🔧 已修复' : (result.missingAPIs.length > 0 ? '⚠️  缺失导入' : '✅ 正确');
      const apis = result.usedAPIs.join(', ');
      console.log(`   ${status}: ${result.file} (${apis})`);
      
      if (result.missingAPIs.length > 0) {
        console.log(`        缺失: ${result.missingAPIs.join(', ')}`);
      }
    });
  }
  
  if (modifiedCount > 0) {
    console.log('\n✅ 修复完成！所有文件现在都正确导入了所需的 React Native API。');
  } else if (allMissingAPIs.size > 0) {
    console.log('\n⚠️  仍有文件缺少必要的导入，可能需要手动检查。');
  } else {
    console.log('\n✅ 所有文件的 React Native API 导入都是正确的！');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { checkAndFixReactNativeImports, processFile };
