/**
 * 智能标签生成服务
 * 基于联系人信息、沟通内容和活动数据自动生成相关标签
 */

import { Contact, Communication, BusinessEvent } from '../types';

// 标签类型
export type TagType = 
  | 'industry'      // 行业标签
  | 'role'          // 职位角色标签
  | 'relationship'  // 关系类型标签
  | 'interest'      // 兴趣爱好标签
  | 'skill'         // 技能标签
  | 'location'      // 地理位置标签
  | 'event'         // 活动相关标签
  | 'communication' // 沟通特征标签
  | 'custom';       // 自定义标签

// 智能标签接口
export interface IntelligentTag {
  id: string;
  text: string;
  type: TagType;
  confidence: number; // 置信度 0-1
  source: 'profile' | 'communication' | 'event' | 'behavior' | 'manual';
  metadata?: {
    extractedFrom?: string;
    relatedEntities?: string[];
    frequency?: number;
    lastUsed?: Date;
  };
  createdAt: Date;
}

// 标签建议
export interface TagSuggestion {
  tag: IntelligentTag;
  reason: string;
  applicableEntities: string[]; // 适用的联系人/活动ID
}

/**
 * 智能标签生成服务类
 */
export class IntelligentTaggingService {
  private industryKeywords: Record<string, string[]> = {
    '科技': ['AI', '人工智能', '机器学习', '软件', '互联网', '科技', '技术', 'IT', '程序', '开发', '算法', '数据'],
    '金融': ['银行', '投资', '基金', '保险', '证券', '金融', '财务', '会计', '风控', '资本'],
    '医疗': ['医院', '医生', '护士', '药品', '医疗', '健康', '诊断', '治疗', '康复', '医学'],
    '教育': ['学校', '老师', '教授', '学生', '教育', '培训', '课程', '学习', '研究', '学术'],
    '制造': ['工厂', '生产', '制造', '加工', '设备', '机械', '工程', '质量', '供应链'],
    '零售': ['商店', '销售', '零售', '电商', '购物', '消费', '品牌', '市场', '客户'],
    '房地产': ['房产', '地产', '建筑', '装修', '物业', '投资', '开发', '设计'],
    '媒体': ['媒体', '新闻', '记者', '编辑', '广告', '营销', '传播', '内容', '创意'],
  };

  private roleKeywords: Record<string, string[]> = {
    'CEO': ['CEO', '首席执行官', '总裁', '董事长'],
    'CTO': ['CTO', '首席技术官', '技术总监', '技术负责人'],
    'CFO': ['CFO', '首席财务官', '财务总监', '财务负责人'],
    '产品经理': ['产品经理', 'PM', '产品总监', '产品负责人'],
    '工程师': ['工程师', '开发', '程序员', '技术', '架构师'],
    '销售': ['销售', '业务', '客户经理', '商务'],
    '市场': ['市场', '营销', '推广', '品牌'],
    '运营': ['运营', '操作', '管理', '执行'],
    '设计师': ['设计师', '设计', 'UI', 'UX', '视觉'],
    '投资人': ['投资', '投资人', 'VC', 'PE', '基金'],
  };

  private communicationPatterns: Record<string, string[]> = {
    '正式': ['您好', '尊敬的', '感谢', '请', '谢谢', '此致敬礼'],
    '友好': ['哈哈', '😊', '👍', '不错', '很好', '棒'],
    '专业': ['项目', '方案', '计划', '分析', '报告', '数据'],
    '紧急': ['紧急', '急', '马上', '立即', '尽快', '重要'],
    '合作': ['合作', '协作', '共同', '一起', '配合', '支持'],
  };

  /**
   * 为联系人生成智能标签
   */
  generateContactTags(
    contact: Contact,
    communications: Communication[] = [],
    events: BusinessEvent[] = []
  ): TagSuggestion[] {
    const suggestions: TagSuggestion[] = [];

    // 基于个人信息生成标签
    suggestions.push(...this.generateProfileTags(contact));

    // 基于沟通内容生成标签
    const contactCommunications = communications.filter(c => 
      c.primaryContactId === contact.id || c.participants?.includes(contact.id)
    );
    suggestions.push(...this.generateCommunicationTags(contact, contactCommunications));

    // 基于活动参与生成标签
    const contactEvents = events.filter(e => 
      e.participants.some(p => p.contactId === contact.id)
    );
    suggestions.push(...this.generateEventTags(contact, contactEvents));

    // 基于行为模式生成标签
    suggestions.push(...this.generateBehaviorTags(contact, contactCommunications, contactEvents));

    // 去重和排序
    return this.deduplicateAndRankSuggestions(suggestions);
  }

  /**
   * 为活动生成智能标签
   */
  generateEventTags(event: BusinessEvent, contacts: Contact[] = []): TagSuggestion[] {
    const suggestions: TagSuggestion[] = [];

    // 基于活动类型
    suggestions.push({
      tag: {
        id: `event_type_${event.eventType}`,
        text: this.getEventTypeDisplayName(event.eventType),
        type: 'event',
        confidence: 0.9,
        source: 'event',
        createdAt: new Date(),
      },
      reason: '基于活动类型自动生成',
      applicableEntities: [event.id],
    });

    // 基于活动规模
    suggestions.push({
      tag: {
        id: `event_scale_${event.scale}`,
        text: this.getScaleDisplayName(event.scale),
        type: 'event',
        confidence: 0.8,
        source: 'event',
        createdAt: new Date(),
      },
      reason: '基于活动规模自动生成',
      applicableEntities: [event.id],
    });

    // 基于参与者行业分布
    const participantContacts = contacts.filter(c => 
      event.participants.some(p => p.contactId === c.id)
    );
    const industryTags = this.analyzeParticipantIndustries(participantContacts);
    suggestions.push(...industryTags.map(tag => ({
      tag,
      reason: '基于参与者行业分布生成',
      applicableEntities: [event.id],
    })));

    return suggestions;
  }

  /**
   * 基于个人信息生成标签
   */
  private generateProfileTags(contact: Contact): TagSuggestion[] {
    const suggestions: TagSuggestion[] = [];

    // 行业标签
    if (contact.industry) {
      suggestions.push({
        tag: {
          id: `industry_${contact.industry}`,
          text: contact.industry,
          type: 'industry',
          confidence: 0.9,
          source: 'profile',
          createdAt: new Date(),
        },
        reason: '基于联系人行业信息',
        applicableEntities: [contact.id],
      });
    }

    // 职位标签
    if (contact.position) {
      const roleTag = this.extractRoleFromPosition(contact.position);
      if (roleTag) {
        suggestions.push({
          tag: roleTag,
          reason: '基于联系人职位信息',
          applicableEntities: [contact.id],
        });
      }
    }

    // 公司标签
    if (contact.company) {
      suggestions.push({
        tag: {
          id: `company_${contact.company}`,
          text: contact.company,
          type: 'custom',
          confidence: 0.8,
          source: 'profile',
          createdAt: new Date(),
        },
        reason: '基于联系人公司信息',
        applicableEntities: [contact.id],
      });
    }

    // 地理位置标签
    if (contact.location) {
      suggestions.push({
        tag: {
          id: `location_${contact.location}`,
          text: contact.location,
          type: 'location',
          confidence: 0.7,
          source: 'profile',
          createdAt: new Date(),
        },
        reason: '基于联系人地理位置',
        applicableEntities: [contact.id],
      });
    }

    return suggestions;
  }

  /**
   * 基于沟通内容生成标签
   */
  private generateCommunicationTags(contact: Contact, communications: Communication[]): TagSuggestion[] {
    const suggestions: TagSuggestion[] = [];

    if (communications.length === 0) return suggestions;

    // 分析沟通频率
    const frequency = this.analyzeCommunicationFrequency(communications);
    suggestions.push({
      tag: {
        id: `comm_frequency_${frequency}`,
        text: `${frequency}联系`,
        type: 'communication',
        confidence: 0.7,
        source: 'communication',
        createdAt: new Date(),
      },
      reason: '基于沟通频率分析',
      applicableEntities: [contact.id],
    });

    // 分析沟通风格
    const style = this.analyzeCommunicationStyle(communications);
    if (style) {
      suggestions.push({
        tag: style,
        reason: '基于沟通内容风格分析',
        applicableEntities: [contact.id],
      });
    }

    // 提取关键词标签
    const keywordTags = this.extractKeywordsFromCommunications(communications);
    suggestions.push(...keywordTags.map(tag => ({
      tag,
      reason: '基于沟通内容关键词提取',
      applicableEntities: [contact.id],
    })));

    return suggestions;
  }

  /**
   * 基于活动参与生成标签
   */
  private generateEventTags(contact: Contact, events: BusinessEvent[]): TagSuggestion[] {
    const suggestions: TagSuggestion[] = [];

    if (events.length === 0) return suggestions;

    // 活动类型偏好
    const eventTypePreferences = this.analyzeEventTypePreferences(events);
    suggestions.push(...eventTypePreferences.map(tag => ({
      tag,
      reason: '基于活动参与偏好分析',
      applicableEntities: [contact.id],
    })));

    // 网络活跃度
    const networkActivity = this.analyzeNetworkActivity(events);
    suggestions.push({
      tag: networkActivity,
      reason: '基于网络活动参与度分析',
      applicableEntities: [contact.id],
    });

    return suggestions;
  }

  /**
   * 基于行为模式生成标签
   */
  private generateBehaviorTags(
    contact: Contact,
    communications: Communication[],
    events: BusinessEvent[]
  ): TagSuggestion[] {
    const suggestions: TagSuggestion[] = [];

    // 响应速度分析
    const responseSpeed = this.analyzeResponseSpeed(communications);
    if (responseSpeed) {
      suggestions.push({
        tag: responseSpeed,
        reason: '基于沟通响应速度分析',
        applicableEntities: [contact.id],
      });
    }

    // 关系发展阶段
    const relationshipStage = this.analyzeRelationshipStage(contact, communications, events);
    suggestions.push({
      tag: relationshipStage,
      reason: '基于关系发展历史分析',
      applicableEntities: [contact.id],
    });

    return suggestions;
  }

  /**
   * 从职位信息提取角色标签
   */
  private extractRoleFromPosition(position: string): IntelligentTag | null {
    const lowerPosition = position.toLowerCase();
    
    for (const [role, keywords] of Object.entries(this.roleKeywords)) {
      if (keywords.some(keyword => lowerPosition.includes(keyword.toLowerCase()))) {
        return {
          id: `role_${role}`,
          text: role,
          type: 'role',
          confidence: 0.8,
          source: 'profile',
          metadata: { extractedFrom: position },
          createdAt: new Date(),
        };
      }
    }

    return null;
  }

  /**
   * 分析沟通频率
   */
  private analyzeCommunicationFrequency(communications: Communication[]): string {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const recentComms = communications.filter(c => new Date(c.date) > thirtyDaysAgo);

    if (recentComms.length >= 10) return '高频';
    if (recentComms.length >= 5) return '中频';
    if (recentComms.length >= 1) return '低频';
    return '很少';
  }

  /**
   * 分析沟通风格
   */
  private analyzeCommunicationStyle(communications: Communication[]): IntelligentTag | null {
    const allContent = communications.map(c => c.content).join(' ');
    
    for (const [style, keywords] of Object.entries(this.communicationPatterns)) {
      const matchCount = keywords.filter(keyword => 
        allContent.toLowerCase().includes(keyword.toLowerCase())
      ).length;
      
      if (matchCount >= 2) {
        return {
          id: `style_${style}`,
          text: `${style}风格`,
          type: 'communication',
          confidence: Math.min(0.9, 0.5 + matchCount * 0.1),
          source: 'communication',
          metadata: { frequency: matchCount },
          createdAt: new Date(),
        };
      }
    }

    return null;
  }

  /**
   * 从沟通内容提取关键词
   */
  private extractKeywordsFromCommunications(communications: Communication[]): IntelligentTag[] {
    const tags: IntelligentTag[] = [];
    const allContent = communications.map(c => c.content).join(' ');

    // 检查行业关键词
    for (const [industry, keywords] of Object.entries(this.industryKeywords)) {
      const matchCount = keywords.filter(keyword => 
        allContent.toLowerCase().includes(keyword.toLowerCase())
      ).length;
      
      if (matchCount >= 2) {
        tags.push({
          id: `keyword_${industry}`,
          text: `${industry}相关`,
          type: 'interest',
          confidence: Math.min(0.8, 0.4 + matchCount * 0.1),
          source: 'communication',
          metadata: { frequency: matchCount },
          createdAt: new Date(),
        });
      }
    }

    return tags;
  }

  /**
   * 分析活动类型偏好
   */
  private analyzeEventTypePreferences(events: BusinessEvent[]): IntelligentTag[] {
    const typeCount = new Map<string, number>();
    
    events.forEach(event => {
      typeCount.set(event.eventType, (typeCount.get(event.eventType) || 0) + 1);
    });

    const tags: IntelligentTag[] = [];
    for (const [type, count] of typeCount.entries()) {
      if (count >= 2) {
        tags.push({
          id: `preference_${type}`,
          text: `偏好${this.getEventTypeDisplayName(type)}`,
          type: 'interest',
          confidence: Math.min(0.9, 0.5 + count * 0.1),
          source: 'event',
          metadata: { frequency: count },
          createdAt: new Date(),
        });
      }
    }

    return tags;
  }

  /**
   * 分析网络活跃度
   */
  private analyzeNetworkActivity(events: BusinessEvent[]): IntelligentTag {
    const totalParticipants = events.reduce((sum, event) => sum + event.participants.length, 0);
    const avgParticipants = totalParticipants / events.length;

    let activity: string;
    let confidence: number;

    if (avgParticipants >= 20) {
      activity = '高度活跃';
      confidence = 0.9;
    } else if (avgParticipants >= 10) {
      activity = '中度活跃';
      confidence = 0.8;
    } else {
      activity = '低度活跃';
      confidence = 0.7;
    }

    return {
      id: `activity_${activity}`,
      text: `网络${activity}`,
      type: 'relationship',
      confidence,
      source: 'behavior',
      metadata: { avgParticipants },
      createdAt: new Date(),
    };
  }

  /**
   * 分析响应速度
   */
  private analyzeResponseSpeed(communications: Communication[]): IntelligentTag | null {
    // 简化实现：基于沟通频率推断响应速度
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const recentComms = communications.filter(c => new Date(c.date) > sevenDaysAgo);

    if (recentComms.length >= 5) {
      return {
        id: 'response_fast',
        text: '响应迅速',
        type: 'communication',
        confidence: 0.8,
        source: 'behavior',
        createdAt: new Date(),
      };
    } else if (recentComms.length >= 2) {
      return {
        id: 'response_normal',
        text: '响应正常',
        type: 'communication',
        confidence: 0.7,
        source: 'behavior',
        createdAt: new Date(),
      };
    }

    return null;
  }

  /**
   * 分析关系发展阶段
   */
  private analyzeRelationshipStage(
    contact: Contact,
    communications: Communication[],
    events: BusinessEvent[]
  ): IntelligentTag {
    const daysSinceCreated = Math.floor(
      (new Date().getTime() - new Date(contact.createdAt).getTime()) / (1000 * 60 * 60 * 24)
    );

    let stage: string;
    let confidence: number;

    if (daysSinceCreated < 30 && communications.length < 3) {
      stage = '初识阶段';
      confidence = 0.8;
    } else if (communications.length >= 10 || events.length >= 3) {
      stage = '深度合作';
      confidence = 0.9;
    } else if (communications.length >= 5 || events.length >= 1) {
      stage = '发展阶段';
      confidence = 0.8;
    } else {
      stage = '维护阶段';
      confidence = 0.7;
    }

    return {
      id: `stage_${stage}`,
      text: stage,
      type: 'relationship',
      confidence,
      source: 'behavior',
      metadata: { daysSinceCreated, commCount: communications.length, eventCount: events.length },
      createdAt: new Date(),
    };
  }

  /**
   * 分析参与者行业分布
   */
  private analyzeParticipantIndustries(contacts: Contact[]): IntelligentTag[] {
    const industryCount = new Map<string, number>();
    
    contacts.forEach(contact => {
      if (contact.industry) {
        industryCount.set(contact.industry, (industryCount.get(contact.industry) || 0) + 1);
      }
    });

    const tags: IntelligentTag[] = [];
    for (const [industry, count] of industryCount.entries()) {
      if (count >= 2) {
        tags.push({
          id: `event_industry_${industry}`,
          text: `${industry}聚会`,
          type: 'event',
          confidence: Math.min(0.9, 0.6 + count * 0.1),
          source: 'event',
          metadata: { participantCount: count },
          createdAt: new Date(),
        });
      }
    }

    return tags;
  }

  /**
   * 去重和排序建议
   */
  private deduplicateAndRankSuggestions(suggestions: TagSuggestion[]): TagSuggestion[] {
    const uniqueSuggestions = new Map<string, TagSuggestion>();

    suggestions.forEach(suggestion => {
      const key = `${suggestion.tag.text}_${suggestion.tag.type}`;
      const existing = uniqueSuggestions.get(key);
      
      if (!existing || suggestion.tag.confidence > existing.tag.confidence) {
        uniqueSuggestions.set(key, suggestion);
      }
    });

    return Array.from(uniqueSuggestions.values())
      .sort((a, b) => b.tag.confidence - a.tag.confidence)
      .slice(0, 10); // 限制返回数量
  }

  /**
   * 获取活动类型显示名称
   */
  private getEventTypeDisplayName(eventType: string): string {
    const displayNames: Record<string, string> = {
      business_meeting: '商务会议',
      industry_conference: '行业会议',
      exhibition: '展会',
      seminar: '研讨会',
      networking: '社交活动',
      client_visit: '客户拜访',
      training: '培训',
      lecture: '讲座',
      product_launch: '产品发布',
      business_dinner: '商务晚宴',
      workshop: '工作坊',
      other: '其他',
    };
    return displayNames[eventType] || eventType;
  }

  /**
   * 获取规模显示名称
   */
  private getScaleDisplayName(scale: string): string {
    const displayNames: Record<string, string> = {
      small: '小型活动',
      medium: '中型活动',
      large: '大型活动',
      massive: '超大型活动',
    };
    return displayNames[scale] || scale;
  }
}

// 导出默认实例
export const intelligentTaggingService = new IntelligentTaggingService();
