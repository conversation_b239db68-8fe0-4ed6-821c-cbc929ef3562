import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  HStack,
  Box,
  Text,
  Heading,
  Button,
  ButtonText,
  ButtonIcon,
  Input,
  InputField,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  Modal<PERSON>ooter,
  Pressable,
  Icon,
  Badge,
  BadgeText,
  ScrollView,
  Divider,
} from '@gluestack-ui/themed';
import {
  Plus,
  Edit,
  Trash2,
  Users,
  Briefcase,
  Heart,
  Home,
  Star,
  Bookmark,
  X,
  Check,
} from 'lucide-react-native';
import { useContactGroupsStore, useContactGroupsSelectors } from '../../store/contactGroupsStore';
import { ContactGroup } from '../../types';

interface ContactGroupManagerProps {
  visible: boolean;
  onClose: () => void;
}

/**
 * 联系人分组管理组件
 * 允许用户创建、编辑、删除联系人分组
 */
const ContactGroupManager: React.FC<ContactGroupManagerProps> = ({
  visible,
  onClose,
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [editingGroup, setEditingGroup] = useState<ContactGroup | null>(null);
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [selectedColor, setSelectedColor] = useState('#3B82F6');
  const [selectedIcon, setSelectedIcon] = useState('users');

  // Store hooks
  const groups = useContactGroupsSelectors.useGroups();
  const defaultGroups = useContactGroupsSelectors.useDefaultGroups();
  const customGroups = useContactGroupsSelectors.useCustomGroups();
  const { addGroup, updateGroup, deleteGroup, clearError } = useContactGroupsStore();

  // 预定义颜色
  const colors = [
    '#3B82F6', // Blue
    '#10B981', // Green
    '#F59E0B', // Yellow
    '#EF4444', // Red
    '#8B5CF6', // Purple
    '#F97316', // Orange
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#EC4899', // Pink
    '#6B7280', // Gray
  ];

  // 预定义图标
  const icons = [
    { name: 'users', icon: Users },
    { name: 'briefcase', icon: Briefcase },
    { name: 'heart', icon: Heart },
    { name: 'home', icon: Home },
    { name: 'star', icon: Star },
    { name: 'bookmark', icon: Bookmark },
  ];

  const handleCreateGroup = () => {
    setIsCreating(true);
    setEditingGroup(null);
    setGroupName('');
    setGroupDescription('');
    setSelectedColor('#3B82F6');
    setSelectedIcon('users');
  };

  const handleEditGroup = (group: ContactGroup) => {
    setEditingGroup(group);
    setIsCreating(true);
    setGroupName(group.name);
    setGroupDescription(group.description || '');
    setSelectedColor(group.color);
    setSelectedIcon(group.icon);
  };

  const handleSaveGroup = () => {
    if (!groupName.trim()) return;

    const groupData = {
      name: groupName.trim(),
      description: groupDescription.trim() || undefined,
      color: selectedColor,
      icon: selectedIcon,
      isDefault: false,
    };

    if (editingGroup) {
      updateGroup(editingGroup.id, groupData);
    } else {
      addGroup(groupData);
    }

    setIsCreating(false);
    setEditingGroup(null);
    clearError();
  };

  const handleDeleteGroup = (group: ContactGroup) => {
    if (group.isDefault) return;
    
    // TODO: 添加确认对话框
    deleteGroup(group.id);
  };

  const handleCancel = () => {
    setIsCreating(false);
    setEditingGroup(null);
    clearError();
  };

  const renderGroupItem = (group: ContactGroup) => {
    const IconComponent = icons.find(i => i.name === group.icon)?.icon || Users;

    return (
      <Box
        key={group.id}
        p="$4"
        bg="$backgroundLight0"
        borderRadius="$md"
        borderWidth="$1"
        borderColor="$borderLight200"
        sx={{
          _dark: {
            bg: '$backgroundDark900',
            borderColor: '$borderDark700',
          },
        }}
      >
        <HStack justifyContent="space-between" alignItems="center">
          <HStack space="md" alignItems="center" flex={1}>
            <Box
              w="$10"
              h="$10"
              borderRadius="$full"
              bg={group.color}
              justifyContent="center"
              alignItems="center"
            >
              <Icon as={IconComponent} size="md" color="white" />
            </Box>
            
            <VStack flex={1}>
              <HStack alignItems="center" space="sm">
                <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  {group.name}
                </Text>
                {group.isDefault && (
                  <Badge size="sm" variant="outline" action="muted">
                    <BadgeText>默认</BadgeText>
                  </Badge>
                )}
              </HStack>
              
              {group.description && (
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  {group.description}
                </Text>
              )}
              
              <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                {group.contactCount} 个联系人
              </Text>
            </VStack>
          </HStack>

          {!group.isDefault && (
            <HStack space="sm">
              <Pressable onPress={() => handleEditGroup(group)} p="$2">
                <Icon as={Edit} size="sm" color="$textLight600" />
              </Pressable>
              <Pressable onPress={() => handleDeleteGroup(group)} p="$2">
                <Icon as={Trash2} size="sm" color="$error600" />
              </Pressable>
            </HStack>
          )}
        </HStack>
      </Box>
    );
  };

  return (
    <Modal isOpen={visible} onClose={onClose} size="lg">
      <ModalBackdrop />
      <ModalContent>
        <ModalHeader>
          <Heading size="md">分组管理</Heading>
          <ModalCloseButton>
            <Icon as={X} />
          </ModalCloseButton>
        </ModalHeader>

        <ModalBody>
          {!isCreating ? (
            <VStack space="lg">
              {/* 创建新分组按钮 */}
              <Button onPress={handleCreateGroup} variant="outline">
                <ButtonIcon as={Plus} />
                <ButtonText>创建新分组</ButtonText>
              </Button>

              {/* 默认分组 */}
              {defaultGroups.length > 0 && (
                <VStack space="md">
                  <Text fontWeight="$bold" color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                    默认分组
                  </Text>
                  {defaultGroups.map(renderGroupItem)}
                </VStack>
              )}

              {/* 自定义分组 */}
              {customGroups.length > 0 && (
                <VStack space="md">
                  <Text fontWeight="$bold" color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                    自定义分组
                  </Text>
                  {customGroups.map(renderGroupItem)}
                </VStack>
              )}

              {customGroups.length === 0 && (
                <Box p="$4" alignItems="center">
                  <Text color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                    还没有自定义分组
                  </Text>
                </Box>
              )}
            </VStack>
          ) : (
            <VStack space="lg">
              {/* 分组名称 */}
              <VStack space="sm">
                <Text fontWeight="$medium">分组名称</Text>
                <Input>
                  <InputField
                    placeholder="输入分组名称"
                    value={groupName}
                    onChangeText={setGroupName}
                  />
                </Input>
              </VStack>

              {/* 分组描述 */}
              <VStack space="sm">
                <Text fontWeight="$medium">描述（可选）</Text>
                <Input>
                  <InputField
                    placeholder="输入分组描述"
                    value={groupDescription}
                    onChangeText={setGroupDescription}
                  />
                </Input>
              </VStack>

              {/* 颜色选择 */}
              <VStack space="sm">
                <Text fontWeight="$medium">选择颜色</Text>
                <HStack space="sm" flexWrap="wrap">
                  {colors.map((color) => (
                    <Pressable
                      key={color}
                      onPress={() => setSelectedColor(color)}
                      w="$8"
                      h="$8"
                      borderRadius="$full"
                      bg={color}
                      borderWidth={selectedColor === color ? 3 : 0}
                      borderColor="$primary500"
                      justifyContent="center"
                      alignItems="center"
                    >
                      {selectedColor === color && (
                        <Icon as={Check} size="sm" color="white" />
                      )}
                    </Pressable>
                  ))}
                </HStack>
              </VStack>

              {/* 图标选择 */}
              <VStack space="sm">
                <Text fontWeight="$medium">选择图标</Text>
                <HStack space="sm" flexWrap="wrap">
                  {icons.map((iconItem) => (
                    <Pressable
                      key={iconItem.name}
                      onPress={() => setSelectedIcon(iconItem.name)}
                      w="$10"
                      h="$10"
                      borderRadius="$md"
                      bg={selectedIcon === iconItem.name ? '$primary100' : '$backgroundLight100'}
                      borderWidth={selectedIcon === iconItem.name ? 2 : 1}
                      borderColor={selectedIcon === iconItem.name ? '$primary500' : '$borderLight200'}
                      justifyContent="center"
                      alignItems="center"
                      sx={{
                        _dark: {
                          bg: selectedIcon === iconItem.name ? '$primary900' : '$backgroundDark800',
                          borderColor: selectedIcon === iconItem.name ? '$primary500' : '$borderDark700',
                        },
                      }}
                    >
                      <Icon
                        as={iconItem.icon}
                        size="md"
                        color={selectedIcon === iconItem.name ? '$primary600' : '$textLight600'}
                      />
                    </Pressable>
                  ))}
                </HStack>
              </VStack>
            </VStack>
          )}
        </ModalBody>

        <ModalFooter>
          {isCreating ? (
            <HStack space="md">
              <Button variant="outline" onPress={handleCancel} flex={1}>
                <ButtonText>取消</ButtonText>
              </Button>
              <Button onPress={handleSaveGroup} flex={1} isDisabled={!groupName.trim()}>
                <ButtonText>{editingGroup ? '更新' : '创建'}</ButtonText>
              </Button>
            </HStack>
          ) : (
            <Button onPress={onClose} flex={1}>
              <ButtonText>完成</ButtonText>
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ContactGroupManager;
