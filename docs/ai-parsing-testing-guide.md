# AI联系人信息解析功能测试指南

## 功能概述

新增的AI联系人信息解析功能允许用户通过以下方式快速添加联系人：
1. **拍照识别**：拍摄名片或包含联系人信息的图片，自动识别文字并解析
2. **文本粘贴**：直接粘贴或输入包含联系人信息的文本，AI自动解析

## 测试前准备

### 1. 环境配置
1. 复制 `.env.example` 为 `.env`
2. 配置OpenRouter API密钥：
   ```
   EXPO_PUBLIC_OPENROUTER_API_KEY=your_api_key_here
   ```
3. （可选）配置Google Vision API密钥用于OCR：
   ```
   EXPO_PUBLIC_GOOGLE_VISION_API_KEY=your_google_vision_api_key_here
   ```

### 2. 获取API密钥
- **OpenRouter**: 访问 https://openrouter.ai/keys 注册并获取API密钥
- **Google Vision**: 访问 https://console.cloud.google.com/ 创建项目并启用Vision API

## 测试用例

### 测试用例1：文本解析功能

**测试数据**：
```
张三
产品经理
北京科技有限公司
电话：138-0013-8000
邮箱：<EMAIL>
地址：北京市朝阳区建国路88号
```

**测试步骤**：
1. 打开RelationHub应用
2. 导航到"联系人"页面
3. 点击"+"按钮创建新联系人
4. 点击右上角的魔法棒图标（🪄）
5. 选择"输入文本"
6. 粘贴或输入测试数据
7. 点击"开始解析"
8. 验证解析结果

**预期结果**：
- 姓名：张三
- 公司：北京科技有限公司
- 职位：产品经理
- 电话：138-0013-8000
- 邮箱：<EMAIL>
- 地址：北京市朝阳区建国路88号

### 测试用例2：英文联系人信息

**测试数据**：
```
John Smith
Software Engineer
Tech Corp Inc.
Phone: +****************
Email: <EMAIL>
Address: 123 Main St, San Francisco, CA 94105
LinkedIn: https://linkedin.com/in/johnsmith
```

**测试步骤**：同测试用例1

**预期结果**：
- 姓名：John Smith
- 公司：Tech Corp Inc.
- 职位：Software Engineer
- 电话：+****************
- 邮箱：<EMAIL>
- 地址：123 Main St, San Francisco, CA 94105

### 测试用例3：复杂格式解析

**测试数据**：
```
李明 | 高级架构师
上海创新科技有限公司
📱 手机：186-2345-6789
📧 邮箱：<EMAIL>
🏢 办公室：021-6789-0123
📍 地址：上海市浦东新区张江高科技园区
🌐 网站：www.innovation.com.cn
💼 专业领域：云计算、大数据、AI
```

**测试步骤**：同测试用例1

**预期结果**：
- 能够正确识别emoji符号后的信息
- 正确解析多个电话号码
- 识别专业领域信息

### 测试用例4：拍照识别功能

**测试步骤**：
1. 准备一张清晰的名片或包含联系人信息的图片
2. 打开RelationHub应用
3. 导航到"联系人"页面
4. 点击"+"按钮创建新联系人
5. 点击右上角的魔法棒图标（🪄）
6. 选择"拍照识别"
7. 选择"拍照"或"从相册选择"
8. 拍摄或选择包含联系人信息的图片
9. 等待OCR识别和AI解析
10. 验证解析结果

**预期结果**：
- 能够成功识别图片中的文字
- AI能够正确解析联系人信息
- 解析结果准确填充到表单中

## 错误处理测试

### 测试用例5：无效输入处理

**测试数据**：
```
这是一段没有任何联系人信息的随机文本。
今天天气很好，我去公园散步了。
```

**预期结果**：
- 系统应该提示未能识别到有效的联系人信息
- 不应该崩溃或产生错误的解析结果

### 测试用例6：网络错误处理

**测试步骤**：
1. 断开网络连接
2. 尝试使用AI解析功能
3. 观察错误处理

**预期结果**：
- 显示友好的错误提示
- 建议用户检查网络连接
- 不应该崩溃

### 测试用例7：API密钥错误

**测试步骤**：
1. 在.env文件中设置错误的API密钥
2. 尝试使用AI解析功能

**预期结果**：
- 显示API配置错误的提示
- 建议用户检查API密钥配置

## 性能测试

### 测试用例8：解析速度测试

**测试目标**：验证AI解析的响应时间

**测试步骤**：
1. 使用标准测试数据进行解析
2. 记录从点击"开始解析"到显示结果的时间

**预期结果**：
- 文本解析应在5秒内完成
- 图片OCR+解析应在10秒内完成

### 测试用例9：大文本处理

**测试数据**：包含多个联系人信息的长文本

**预期结果**：
- 能够处理较长的文本输入
- 正确识别第一个或最相关的联系人信息

## 用户体验测试

### 测试用例10：界面交互

**测试重点**：
- 魔法棒按钮是否易于发现
- 模态框的打开和关闭是否流畅
- 解析过程中的加载指示是否清晰
- 解析结果的展示是否直观

### 测试用例11：表单填充

**测试重点**：
- 解析结果是否正确填充到对应的表单字段
- 用户是否可以编辑AI填充的内容
- 表单验证是否正常工作

## 已知限制

1. **OCR功能**：需要配置Google Vision API密钥，否则只能使用文本输入
2. **AI解析**：需要网络连接和有效的OpenRouter API密钥
3. **语言支持**：主要支持中文和英文，其他语言可能识别率较低
4. **图片质量**：OCR识别效果依赖图片清晰度和文字可读性

## 故障排除

### 常见问题

1. **"AI API密钥未配置"错误**
   - 检查.env文件是否存在
   - 确认EXPO_PUBLIC_OPENROUTER_API_KEY已正确设置

2. **"OCR服务未配置"错误**
   - 这是正常的，如果没有配置Google Vision API
   - 可以使用文本输入功能作为替代

3. **解析结果不准确**
   - 检查输入文本的格式和清晰度
   - 尝试重新组织文本格式
   - 对于图片，确保文字清晰可读

4. **网络超时错误**
   - 检查网络连接
   - 重试操作
   - 如果持续出现，可能是API服务暂时不可用

## 反馈和改进

如果在测试过程中发现问题或有改进建议，请记录：
1. 具体的测试步骤
2. 预期结果 vs 实际结果
3. 错误信息（如果有）
4. 设备和环境信息

这些信息将帮助我们持续改进AI解析功能的准确性和用户体验。
