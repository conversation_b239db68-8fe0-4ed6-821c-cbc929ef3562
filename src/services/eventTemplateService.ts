/**
 * 活动模板服务
 * 提供预定义的活动模板和智能模板推荐
 */

import { BusinessEvent, BusinessEventType, BusinessEventScale } from '../types';

// 活动模板接口
export interface EventTemplate {
  id: string;
  name: string;
  description: string;
  eventType: BusinessEventType;
  category: string;
  tags: string[];
  scale: BusinessEventScale;
  isVirtual: boolean;
  defaultDuration: number; // 分钟
  agenda?: string;
  objectives: string[];
  suggestedTasks: Array<{
    title: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
  }>;
  followUpTemplate: Array<{
    type: 'thank_you' | 'business_proposal' | 'meeting_request' | 'information_sharing';
    timing: 'immediate' | 'within_24h' | 'within_week';
    content: string;
  }>;
  icon: string;
  color: string;
  isCustom: boolean;
  createdAt: Date;
  usageCount: number;
}

// 模板类别
export type TemplateCategory = 
  | 'networking'
  | 'business_meeting'
  | 'conference'
  | 'training'
  | 'client_engagement'
  | 'team_building'
  | 'product_launch'
  | 'custom';

/**
 * 活动模板服务类
 */
export class EventTemplateService {
  private templates: EventTemplate[] = [];
  private customTemplates: EventTemplate[] = [];

  constructor() {
    this.initializeDefaultTemplates();
  }

  /**
   * 获取所有模板
   */
  getAllTemplates(): EventTemplate[] {
    return [...this.templates, ...this.customTemplates]
      .sort((a, b) => b.usageCount - a.usageCount);
  }

  /**
   * 按类别获取模板
   */
  getTemplatesByCategory(category: TemplateCategory): EventTemplate[] {
    return this.getAllTemplates().filter(template => {
      if (category === 'custom') return template.isCustom;
      
      switch (category) {
        case 'networking':
          return ['networking', 'business_dinner'].includes(template.eventType);
        case 'business_meeting':
          return ['business_meeting', 'client_visit'].includes(template.eventType);
        case 'conference':
          return ['industry_conference', 'seminar'].includes(template.eventType);
        case 'training':
          return ['training', 'workshop', 'lecture'].includes(template.eventType);
        case 'client_engagement':
          return ['client_visit', 'product_launch'].includes(template.eventType);
        case 'team_building':
          return template.tags.includes('团队建设');
        case 'product_launch':
          return template.eventType === 'product_launch';
        default:
          return false;
      }
    });
  }

  /**
   * 根据模板创建活动
   */
  createEventFromTemplate(
    templateId: string,
    customizations: Partial<BusinessEvent>
  ): Omit<BusinessEvent, 'id' | 'createdAt' | 'updatedAt'> {
    const template = this.getTemplateById(templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // 增加使用次数
    this.incrementTemplateUsage(templateId);

    const now = new Date();
    const defaultEndTime = new Date(now.getTime() + template.defaultDuration * 60 * 1000);

    return {
      title: customizations.title || `${template.name} - ${now.toLocaleDateString()}`,
      description: customizations.description || template.description,
      date: customizations.date || now,
      startTime: customizations.startTime || now,
      endTime: customizations.endTime || defaultEndTime,
      location: customizations.location || (template.isVirtual ? 'https://meet.example.com' : ''),
      isVirtual: customizations.isVirtual ?? template.isVirtual,
      eventLink: customizations.eventLink,
      eventType: template.eventType,
      category: customizations.category || template.category,
      tags: customizations.tags || template.tags,
      scale: customizations.scale || template.scale,
      participants: customizations.participants || [],
      organizerId: customizations.organizerId || 'current-user',
      agenda: customizations.agenda || template.agenda,
      objectives: customizations.objectives || template.objectives,
      notes: customizations.notes || '',
      tasks: template.suggestedTasks.map(task => ({
        title: task.title,
        description: task.description,
        priority: task.priority,
        isCompleted: false
      })),
      followUps: [],
      status: 'planned',
      isArchived: false,
      relationshipsEstablished: 0,
      relationshipsStrengthened: 0,
      ...customizations
    };
  }

  /**
   * 创建自定义模板
   */
  createCustomTemplate(
    name: string,
    event: BusinessEvent
  ): EventTemplate {
    const template: EventTemplate = {
      id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      description: event.description || '',
      eventType: event.eventType,
      category: event.category,
      tags: event.tags,
      scale: event.scale,
      isVirtual: event.isVirtual,
      defaultDuration: event.endTime ? 
        Math.round((new Date(event.endTime).getTime() - new Date(event.startTime).getTime()) / (1000 * 60)) : 
        120,
      agenda: event.agenda,
      objectives: event.objectives || [],
      suggestedTasks: event.tasks.map(task => ({
        title: task.title,
        description: task.description || '',
        priority: task.priority
      })),
      followUpTemplate: [],
      icon: this.getEventTypeIcon(event.eventType),
      color: this.getEventTypeColor(event.eventType),
      isCustom: true,
      createdAt: new Date(),
      usageCount: 0
    };

    this.customTemplates.push(template);
    return template;
  }

  /**
   * 推荐模板
   */
  recommendTemplates(
    userHistory: BusinessEvent[],
    context?: {
      industry?: string;
      eventType?: BusinessEventType;
      scale?: BusinessEventScale;
    }
  ): EventTemplate[] {
    const allTemplates = this.getAllTemplates();
    
    // 基于使用历史评分
    const scoredTemplates = allTemplates.map(template => {
      let score = 0;

      // 基于历史使用的活动类型
      const typeUsage = userHistory.filter(e => e.eventType === template.eventType).length;
      score += typeUsage * 0.3;

      // 基于历史使用的规模
      const scaleUsage = userHistory.filter(e => e.scale === template.scale).length;
      score += scaleUsage * 0.2;

      // 基于历史使用的类别
      const categoryUsage = userHistory.filter(e => e.category === template.category).length;
      score += categoryUsage * 0.2;

      // 基于模板使用频率
      score += template.usageCount * 0.1;

      // 基于上下文匹配
      if (context) {
        if (context.eventType === template.eventType) score += 0.5;
        if (context.scale === template.scale) score += 0.3;
        if (context.industry && template.category.includes(context.industry)) score += 0.4;
      }

      return { template, score };
    });

    return scoredTemplates
      .sort((a, b) => b.score - a.score)
      .slice(0, 6)
      .map(item => item.template);
  }

  /**
   * 获取模板详情
   */
  getTemplateById(templateId: string): EventTemplate | undefined {
    return this.getAllTemplates().find(template => template.id === templateId);
  }

  /**
   * 删除自定义模板
   */
  deleteCustomTemplate(templateId: string): boolean {
    const index = this.customTemplates.findIndex(template => template.id === templateId);
    if (index >= 0) {
      this.customTemplates.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 更新模板
   */
  updateTemplate(templateId: string, updates: Partial<EventTemplate>): boolean {
    const template = this.getTemplateById(templateId);
    if (template && template.isCustom) {
      Object.assign(template, updates);
      return true;
    }
    return false;
  }

  /**
   * 增加模板使用次数
   */
  private incrementTemplateUsage(templateId: string): void {
    const template = this.getTemplateById(templateId);
    if (template) {
      template.usageCount++;
    }
  }

  /**
   * 获取活动类型图标
   */
  private getEventTypeIcon(eventType: BusinessEventType): string {
    const icons: Record<BusinessEventType, string> = {
      business_meeting: '💼',
      industry_conference: '🏢',
      exhibition: '🎪',
      seminar: '📚',
      networking: '🤝',
      client_visit: '🏃',
      training: '🎓',
      lecture: '👨‍🏫',
      product_launch: '🚀',
      business_dinner: '🍽️',
      workshop: '🔧',
      other: '📅'
    };
    return icons[eventType] || '📅';
  }

  /**
   * 获取活动类型颜色
   */
  private getEventTypeColor(eventType: BusinessEventType): string {
    const colors: Record<BusinessEventType, string> = {
      business_meeting: '#3B82F6',
      industry_conference: '#8B5CF6',
      exhibition: '#F59E0B',
      seminar: '#10B981',
      networking: '#EF4444',
      client_visit: '#06B6D4',
      training: '#84CC16',
      lecture: '#6366F1',
      product_launch: '#EC4899',
      business_dinner: '#F97316',
      workshop: '#14B8A6',
      other: '#6B7280'
    };
    return colors[eventType] || '#6B7280';
  }

  /**
   * 初始化默认模板
   */
  private initializeDefaultTemplates(): void {
    this.templates = [
      {
        id: 'template_business_meeting',
        name: '商务会议',
        description: '标准的商务会议，讨论项目进展和决策',
        eventType: 'business_meeting',
        category: '商务',
        tags: ['会议', '商务', '决策'],
        scale: 'small',
        isVirtual: false,
        defaultDuration: 60,
        agenda: '1. 开场介绍\n2. 议题讨论\n3. 决策确认\n4. 后续行动',
        objectives: ['明确项目目标', '制定行动计划', '分配责任'],
        suggestedTasks: [
          { title: '准备会议议程', description: '提前准备详细议程', priority: 'high' },
          { title: '预订会议室', description: '确保会议场地可用', priority: 'medium' },
          { title: '发送会议邀请', description: '提前通知所有参与者', priority: 'high' }
        ],
        followUpTemplate: [
          { type: 'thank_you', timing: 'within_24h', content: '感谢参加今天的会议' },
          { type: 'information_sharing', timing: 'within_24h', content: '分享会议纪要和行动项' }
        ],
        icon: '💼',
        color: '#3B82F6',
        isCustom: false,
        createdAt: new Date(),
        usageCount: 0
      },
      {
        id: 'template_networking_event',
        name: '网络交流活动',
        description: '建立新联系和维护现有关系的社交活动',
        eventType: 'networking',
        category: '社交',
        tags: ['网络', '社交', '关系建立'],
        scale: 'medium',
        isVirtual: false,
        defaultDuration: 120,
        agenda: '1. 欢迎致辞\n2. 自由交流\n3. 主题分享\n4. 联系方式交换',
        objectives: ['建立新的商务联系', '维护现有关系', '分享行业见解'],
        suggestedTasks: [
          { title: '准备自我介绍', description: '准备简洁有力的自我介绍', priority: 'high' },
          { title: '准备名片', description: '确保有足够的名片', priority: 'medium' },
          { title: '研究参与者', description: '了解主要参与者背景', priority: 'medium' }
        ],
        followUpTemplate: [
          { type: 'thank_you', timing: 'within_24h', content: '很高兴认识您' },
          { type: 'meeting_request', timing: 'within_week', content: '希望能进一步交流' }
        ],
        icon: '🤝',
        color: '#EF4444',
        isCustom: false,
        createdAt: new Date(),
        usageCount: 0
      },
      {
        id: 'template_client_visit',
        name: '客户拜访',
        description: '拜访重要客户，维护客户关系',
        eventType: 'client_visit',
        category: '客户关系',
        tags: ['客户', '拜访', '关系维护'],
        scale: 'small',
        isVirtual: false,
        defaultDuration: 90,
        agenda: '1. 问候寒暄\n2. 业务回顾\n3. 需求了解\n4. 解决方案讨论\n5. 后续计划',
        objectives: ['了解客户需求', '维护客户关系', '探讨合作机会'],
        suggestedTasks: [
          { title: '准备客户资料', description: '回顾客户历史和偏好', priority: 'high' },
          { title: '准备礼品', description: '准备适当的商务礼品', priority: 'low' },
          { title: '确认行程', description: '确认交通和时间安排', priority: 'medium' }
        ],
        followUpTemplate: [
          { type: 'thank_you', timing: 'immediate', content: '感谢您抽出宝贵时间' },
          { type: 'business_proposal', timing: 'within_week', content: '根据讨论内容准备提案' }
        ],
        icon: '🏃',
        color: '#06B6D4',
        isCustom: false,
        createdAt: new Date(),
        usageCount: 0
      },
      {
        id: 'template_product_launch',
        name: '产品发布会',
        description: '新产品或服务的正式发布活动',
        eventType: 'product_launch',
        category: '产品',
        tags: ['产品', '发布', '营销'],
        scale: 'large',
        isVirtual: false,
        defaultDuration: 180,
        agenda: '1. 开场致辞\n2. 产品介绍\n3. 演示展示\n4. 媒体问答\n5. 网络交流',
        objectives: ['展示新产品特性', '吸引媒体关注', '获得客户反馈'],
        suggestedTasks: [
          { title: '准备产品演示', description: '准备完整的产品演示', priority: 'high' },
          { title: '邀请媒体', description: '邀请相关媒体参加', priority: 'high' },
          { title: '准备宣传材料', description: '准备产品手册和宣传品', priority: 'medium' }
        ],
        followUpTemplate: [
          { type: 'information_sharing', timing: 'immediate', content: '分享产品资料' },
          { type: 'business_proposal', timing: 'within_week', content: '提供详细报价' }
        ],
        icon: '🚀',
        color: '#EC4899',
        isCustom: false,
        createdAt: new Date(),
        usageCount: 0
      },
      {
        id: 'template_training_workshop',
        name: '培训工作坊',
        description: '技能培训和知识分享的互动式工作坊',
        eventType: 'workshop',
        category: '培训',
        tags: ['培训', '技能', '学习'],
        scale: 'medium',
        isVirtual: true,
        defaultDuration: 240,
        agenda: '1. 培训目标介绍\n2. 理论讲解\n3. 实践练习\n4. 案例分析\n5. 总结反馈',
        objectives: ['传授专业技能', '提升团队能力', '促进知识分享'],
        suggestedTasks: [
          { title: '准备培训材料', description: '准备课件和练习材料', priority: 'high' },
          { title: '测试技术设备', description: '确保音视频设备正常', priority: 'medium' },
          { title: '准备练习案例', description: '准备实际案例供练习', priority: 'medium' }
        ],
        followUpTemplate: [
          { type: 'information_sharing', timing: 'within_24h', content: '分享培训资料' },
          { type: 'thank_you', timing: 'within_24h', content: '感谢参与培训' }
        ],
        icon: '🔧',
        color: '#14B8A6',
        isCustom: false,
        createdAt: new Date(),
        usageCount: 0
      }
    ];
  }
}

// 导出默认实例
export const eventTemplateService = new EventTemplateService();
