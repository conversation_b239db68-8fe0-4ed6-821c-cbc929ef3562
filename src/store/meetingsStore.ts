import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Meeting, MeetingTask, MeetingStatus, MeetingParticipant } from '../types';

interface MeetingsState {
  meetings: Meeting[];
  selectedMeeting: Meeting | null;
  isLoading: boolean;
  error: string | null;
  
  // 过滤和搜索
  searchQuery: string;
  statusFilter: MeetingStatus | 'all';
  dateFilter: 'all' | 'today' | 'week' | 'month';
}

interface MeetingsActions {
  // CRUD操作
  addMeeting: (meeting: Omit<Meeting, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateMeeting: (id: string, updates: Partial<Meeting>) => void;
  deleteMeeting: (id: string) => void;
  
  // 会议任务管理
  addMeetingTask: (meetingId: string, task: Omit<MeetingTask, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateMeetingTask: (meetingId: string, taskId: string, updates: Partial<MeetingTask>) => void;
  deleteMeetingTask: (meetingId: string, taskId: string) => void;
  toggleTaskCompletion: (meetingId: string, taskId: string) => void;
  
  // 参与者管理
  addParticipant: (meetingId: string, contactId: string) => void;
  removeParticipant: (meetingId: string, contactId: string) => void;
  updateParticipantStatus: (meetingId: string, contactId: string, status: MeetingParticipant['status']) => void;
  
  // 会议状态管理
  startMeeting: (meetingId: string) => void;
  completeMeeting: (meetingId: string) => void;
  cancelMeeting: (meetingId: string) => void;
  
  // 搜索和过滤
  setSearchQuery: (query: string) => void;
  setStatusFilter: (status: MeetingsState['statusFilter']) => void;
  setDateFilter: (filter: MeetingsState['dateFilter']) => void;
  
  // 选择和导航
  setSelectedMeeting: (meeting: Meeting | null) => void;
  
  // 工具方法
  getMeetingById: (id: string) => Meeting | undefined;
  getMeetingsByDate: (date: Date) => Meeting[];
  getUpcomingMeetings: () => Meeting[];
  getPastMeetings: () => Meeting[];
  
  // 状态管理
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

type MeetingsStore = MeetingsState & MeetingsActions;

export const useMeetingsStore = create<MeetingsStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      meetings: [],
      selectedMeeting: null,
      isLoading: false,
      error: null,
      searchQuery: '',
      statusFilter: 'all',
      dateFilter: 'all',

      // CRUD操作
      addMeeting: (meetingData) => {
        const newMeeting: Meeting = {
          ...meetingData,
          id: Date.now().toString(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          meetings: [...state.meetings, newMeeting],
        }));
      },

      updateMeeting: (id, updates) => {
        set((state) => ({
          meetings: state.meetings.map((meeting) =>
            meeting.id === id
              ? { ...meeting, ...updates, updatedAt: new Date() }
              : meeting
          ),
        }));
      },

      deleteMeeting: (id) => {
        set((state) => ({
          meetings: state.meetings.filter((meeting) => meeting.id !== id),
          selectedMeeting: state.selectedMeeting?.id === id ? null : state.selectedMeeting,
        }));
      },

      // 会议任务管理
      addMeetingTask: (meetingId, taskData) => {
        const newTask: MeetingTask = {
          ...taskData,
          id: Date.now().toString(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          meetings: state.meetings.map((meeting) =>
            meeting.id === meetingId
              ? { ...meeting, tasks: [...meeting.tasks, newTask], updatedAt: new Date() }
              : meeting
          ),
        }));
      },

      updateMeetingTask: (meetingId, taskId, updates) => {
        set((state) => ({
          meetings: state.meetings.map((meeting) =>
            meeting.id === meetingId
              ? {
                  ...meeting,
                  tasks: meeting.tasks.map((task) =>
                    task.id === taskId
                      ? { ...task, ...updates, updatedAt: new Date() }
                      : task
                  ),
                  updatedAt: new Date(),
                }
              : meeting
          ),
        }));
      },

      deleteMeetingTask: (meetingId, taskId) => {
        set((state) => ({
          meetings: state.meetings.map((meeting) =>
            meeting.id === meetingId
              ? {
                  ...meeting,
                  tasks: meeting.tasks.filter((task) => task.id !== taskId),
                  updatedAt: new Date(),
                }
              : meeting
          ),
        }));
      },

      toggleTaskCompletion: (meetingId, taskId) => {
        set((state) => ({
          meetings: state.meetings.map((meeting) =>
            meeting.id === meetingId
              ? {
                  ...meeting,
                  tasks: meeting.tasks.map((task) =>
                    task.id === taskId
                      ? { ...task, isCompleted: !task.isCompleted, updatedAt: new Date() }
                      : task
                  ),
                  updatedAt: new Date(),
                }
              : meeting
          ),
        }));
      },

      // 参与者管理
      addParticipant: (meetingId, contactId) => {
        set((state) => ({
          meetings: state.meetings.map((meeting) =>
            meeting.id === meetingId && !meeting.participantIds.includes(contactId)
              ? {
                  ...meeting,
                  participantIds: [...meeting.participantIds, contactId],
                  updatedAt: new Date(),
                }
              : meeting
          ),
        }));
      },

      removeParticipant: (meetingId, contactId) => {
        set((state) => ({
          meetings: state.meetings.map((meeting) =>
            meeting.id === meetingId
              ? {
                  ...meeting,
                  participantIds: meeting.participantIds.filter((id) => id !== contactId),
                  updatedAt: new Date(),
                }
              : meeting
          ),
        }));
      },

      updateParticipantStatus: (meetingId, contactId, status) => {
        // 这里可以扩展为更复杂的参与者状态管理
        // 目前简化为只管理参与者ID列表
        console.log('Update participant status:', { meetingId, contactId, status });
      },

      // 会议状态管理
      startMeeting: (meetingId) => {
        get().updateMeeting(meetingId, { status: 'in-progress' });
      },

      completeMeeting: (meetingId) => {
        get().updateMeeting(meetingId, { status: 'completed' });
      },

      cancelMeeting: (meetingId) => {
        get().updateMeeting(meetingId, { status: 'cancelled' });
      },

      // 搜索和过滤
      setSearchQuery: (query) => set({ searchQuery: query }),
      setStatusFilter: (status) => set({ statusFilter: status }),
      setDateFilter: (filter) => set({ dateFilter: filter }),

      // 选择和导航
      setSelectedMeeting: (meeting) => set({ selectedMeeting: meeting }),

      // 工具方法
      getMeetingById: (id) => {
        return get().meetings.find((meeting) => meeting.id === id);
      },

      getMeetingsByDate: (date) => {
        const targetDate = date.toDateString();
        return get().meetings.filter((meeting) => 
          meeting.date.toDateString() === targetDate
        );
      },

      getUpcomingMeetings: () => {
        const now = new Date();
        return get().meetings
          .filter((meeting) => meeting.date > now && meeting.status === 'scheduled')
          .sort((a, b) => a.date.getTime() - b.date.getTime());
      },

      getPastMeetings: () => {
        const now = new Date();
        return get().meetings
          .filter((meeting) => meeting.date < now || meeting.status === 'completed')
          .sort((a, b) => b.date.getTime() - a.date.getTime());
      },

      // 状态管理
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),
    }),
    {
      name: 'meetings-store',
      version: 1,
    }
  )
);

// 选择器
export const useMeetingsSelectors = {
  useMeetings: () => useMeetingsStore((state) => state.meetings),
  useSelectedMeeting: () => useMeetingsStore((state) => state.selectedMeeting),
  useUpcomingMeetings: () => useMeetingsStore((state) => state.getUpcomingMeetings()),
  usePastMeetings: () => useMeetingsStore((state) => state.getPastMeetings()),
  useIsLoading: () => useMeetingsStore((state) => state.isLoading),
  useError: () => useMeetingsStore((state) => state.error),
  useSearchQuery: () => useMeetingsStore((state) => state.searchQuery),
  useStatusFilter: () => useMeetingsStore((state) => state.statusFilter),
  useDateFilter: () => useMeetingsStore((state) => state.dateFilter),
};
