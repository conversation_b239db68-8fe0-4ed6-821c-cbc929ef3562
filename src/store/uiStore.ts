import { create } from 'zustand';
import { UIStore } from './types';

/**
 * UI Store
 * 管理UI相关状态，如模态框、导航、搜索、表单等
 */
export const useUIStore = create<UIStore>((set, get) => ({
  // Initial state
  isConfirmationModalVisible: false,
  confirmationModalConfig: null,
  activeTab: 'contacts',
  searchQuery: '',
  searchFilters: {
    tags: [],
    isFavorite: undefined,
  },
  isFormDirty: false,
  formErrors: {},
  
  // Actions
  showConfirmationModal: (config) => {
    set((state) => ({
      ...state,
      isConfirmationModalVisible: true,
      confirmationModalConfig: config,
    }));
  },
  
  hideConfirmationModal: () => {
    set((state) => ({
      ...state,
      isConfirmationModalVisible: false,
      confirmationModalConfig: null,
    }));
  },
  
  setActiveTab: (tab) => {
    set((state) => ({
      ...state,
      activeTab: tab,
    }));
  },
  
  setSearchQuery: (query) => {
    set((state) => ({
      ...state,
      searchQuery: query,
    }));
  },
  
  setSearchFilters: (filters) => {
    set((state) => ({
      ...state,
      searchFilters: {
        ...state.searchFilters,
        ...filters,
      },
    }));
  },
  
  setFormDirty: (isDirty) => {
    set((state) => ({
      ...state,
      isFormDirty: isDirty,
    }));
  },
  
  setFormErrors: (errors) => {
    set((state) => ({
      ...state,
      formErrors: errors,
    }));
  },
  
  clearFormErrors: () => {
    set((state) => ({
      ...state,
      formErrors: {},
    }));
  },
}));

// Selectors for better performance
export const useUISelectors = {
  // Modal state
  useConfirmationModal: () => useUIStore((state) => ({
    isVisible: state.isConfirmationModalVisible,
    config: state.confirmationModalConfig,
  })),
  
  // Navigation state
  useActiveTab: () => useUIStore((state) => state.activeTab),
  
  // Search state
  useSearchState: () => useUIStore((state) => ({
    query: state.searchQuery,
    filters: state.searchFilters,
  })),
  
  // Form state
  useFormState: () => useUIStore((state) => ({
    isDirty: state.isFormDirty,
    errors: state.formErrors,
  })),
};

// Actions selectors
export const useUIActions = () => useUIStore((state) => ({
  showConfirmationModal: state.showConfirmationModal,
  hideConfirmationModal: state.hideConfirmationModal,
  setActiveTab: state.setActiveTab,
  setSearchQuery: state.setSearchQuery,
  setSearchFilters: state.setSearchFilters,
  setFormDirty: state.setFormDirty,
  setFormErrors: state.setFormErrors,
  clearFormErrors: state.clearFormErrors,
}));
