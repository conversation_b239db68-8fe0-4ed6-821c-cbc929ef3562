import React from 'react';
import {
  VStack,
  HStack,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  Button,
  ButtonText,
  Badge,
  BadgeText,
  ScrollView,
  Pressable,
  Icon
} from '@gluestack-ui/themed';
import { Search, Filter, X, Star, Users } from 'lucide-react-native';
import { useContactsStore } from '../../store';

interface ContactsFilterProps {
  onFilterChange?: () => void;
}

/**
 * 联系人搜索和过滤组件
 * 提供搜索、标签过滤、收藏过滤等功能
 */
const ContactsFilter: React.FC<ContactsFilterProps> = ({ onFilterChange }) => {
  const { 
    searchQuery, 
    setSearchQuery, 
    selectedTags, 
    setSelectedTags, 
    availableTags,
    contacts
  } = useContactsStore();

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    onFilterChange?.();
  };

  const handleTagToggle = (tag: string) => {
    const newSelectedTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    
    setSelectedTags(newSelectedTags);
    onFilterChange?.();
  };

  const clearAllFilters = () => {
    setSearchQuery('');
    setSelectedTags([]);
    onFilterChange?.();
  };

  const hasActiveFilters = searchQuery.length > 0 || selectedTags.length > 0;
  const favoriteCount = contacts.filter(c => c.isFavorite).length;

  return (
    <VStack space="md" p="$4" bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark900' } }}>
      {/* Search Input */}
      <Input>
        <InputSlot pl="$3">
          <InputIcon as={Search} />
        </InputSlot>
        <InputField 
          placeholder="Search contacts..." 
          value={searchQuery}
          onChangeText={handleSearchChange}
        />
        {searchQuery.length > 0 && (
          <InputSlot pr="$3">
            <Pressable onPress={() => handleSearchChange('')}>
              <InputIcon as={X} />
            </Pressable>
          </InputSlot>
        )}
      </Input>

      {/* Quick Filters */}
      <HStack space="sm" alignItems="center">
        <Pressable
          onPress={() => handleTagToggle('favorites')}
          bg={selectedTags.includes('favorites') ? '$primary100' : '$backgroundLight100'}
          px="$3"
          py="$2"
          borderRadius="$full"
          sx={{
            _dark: {
              bg: selectedTags.includes('favorites') ? '$primary900' : '$backgroundDark800'
            }
          }}
        >
          <HStack space="xs" alignItems="center">
            <Icon 
              as={Star} 
              size="sm" 
              color={selectedTags.includes('favorites') ? '$primary600' : '$textLight600'}
            />
            <ButtonText 
              size="sm"
              color={selectedTags.includes('favorites') ? '$primary600' : '$textLight600'}
            >
              Favorites ({favoriteCount})
            </ButtonText>
          </HStack>
        </Pressable>

        <Pressable
          onPress={() => handleTagToggle('recent')}
          bg={selectedTags.includes('recent') ? '$primary100' : '$backgroundLight100'}
          px="$3"
          py="$2"
          borderRadius="$full"
          sx={{
            _dark: {
              bg: selectedTags.includes('recent') ? '$primary900' : '$backgroundDark800'
            }
          }}
        >
          <HStack space="xs" alignItems="center">
            <Icon 
              as={Users} 
              size="sm" 
              color={selectedTags.includes('recent') ? '$primary600' : '$textLight600'}
            />
            <ButtonText 
              size="sm"
              color={selectedTags.includes('recent') ? '$primary600' : '$textLight600'}
            >
              Recent
            </ButtonText>
          </HStack>
        </Pressable>
      </HStack>

      {/* Available Tags */}
      {availableTags.length > 0 && (
        <VStack space="sm">
          <HStack justifyContent="space-between" alignItems="center">
            <ButtonText size="sm" color="$textLight600">
              Filter by tags:
            </ButtonText>
            {hasActiveFilters && (
              <Button size="xs" variant="link" onPress={clearAllFilters}>
                <ButtonText>Clear all</ButtonText>
              </Button>
            )}
          </HStack>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <HStack space="xs">
              {availableTags.map((tag) => (
                <Pressable
                  key={tag}
                  onPress={() => handleTagToggle(tag)}
                >
                  <Badge
                    size="md"
                    variant={selectedTags.includes(tag) ? "solid" : "outline"}
                    action={selectedTags.includes(tag) ? "primary" : "muted"}
                  >
                    <BadgeText>{tag}</BadgeText>
                  </Badge>
                </Pressable>
              ))}
            </HStack>
          </ScrollView>
        </VStack>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <HStack space="xs" alignItems="center" flexWrap="wrap">
          <ButtonText size="xs" color="$textLight500">
            Active filters:
          </ButtonText>
          {searchQuery && (
            <Badge size="sm" variant="outline" action="info">
              <BadgeText>Search: "{searchQuery}"</BadgeText>
            </Badge>
          )}
          {selectedTags.map((tag) => (
            <Badge key={tag} size="sm" variant="solid" action="primary">
              <BadgeText>{tag}</BadgeText>
              <Pressable onPress={() => handleTagToggle(tag)} ml="$1">
                <Icon as={X} size="xs" color="white" />
              </Pressable>
            </Badge>
          ))}
        </HStack>
      )}
    </VStack>
  );
};

export default ContactsFilter;
