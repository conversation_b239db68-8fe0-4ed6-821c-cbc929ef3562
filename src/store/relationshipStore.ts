/**
 * 关系管理状态存储
 * 管理联系人之间的关系数据和图谱分析
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  ContactRelationship, 
  ContactRelationshipType,
  NetworkGraph,
  Contact
} from '../types';
import { relationshipGraphService, RelationshipGraphAnalysis } from '../services/relationshipGraphService';

interface RelationshipState {
  // 关系数据
  relationships: ContactRelationship[];
  
  // 图谱缓存
  networkGraphs: Record<string, NetworkGraph>; // contactId -> NetworkGraph
  graphAnalyses: Record<string, RelationshipGraphAnalysis>; // contactId -> Analysis
  
  // UI状态
  isLoading: boolean;
  error: string | null;
  selectedRelationship: ContactRelationship | null;
  
  // 过滤和搜索
  relationshipFilter: {
    types: ContactRelationshipType[];
    isActive?: boolean;
    minStrength?: number;
    maxStrength?: number;
  };
}

interface RelationshipActions {
  // 关系CRUD操作
  addRelationship: (relationship: Omit<ContactRelationship, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateRelationship: (id: string, updates: Partial<ContactRelationship>) => Promise<void>;
  deleteRelationship: (id: string) => Promise<void>;
  toggleRelationshipStatus: (id: string) => Promise<void>;
  
  // 批量操作
  addMultipleRelationships: (relationships: Omit<ContactRelationship, 'id' | 'createdAt' | 'updatedAt'>[]) => Promise<void>;
  deleteMultipleRelationships: (ids: string[]) => Promise<void>;
  
  // 图谱操作
  buildNetworkGraph: (centerContactId: string, contacts: Contact[], maxDepth?: number) => Promise<NetworkGraph>;
  analyzeNetworkGraph: (centerContactId: string, contacts: Contact[]) => Promise<RelationshipGraphAnalysis>;
  clearGraphCache: (contactId?: string) => void;
  
  // 关系发现
  findMutualConnections: (contactId: string) => string[];
  findShortestPath: (fromContactId: string, toContactId: string, contacts: Contact[]) => string[];
  recommendConnections: (contactId: string, contacts: Contact[]) => Array<{ contactId: string; score: number; reason: string }>;
  
  // 关系分析
  getRelationshipsByContact: (contactId: string) => ContactRelationship[];
  getRelationshipsByType: (type: ContactRelationshipType) => ContactRelationship[];
  getStrongRelationships: (minStrength?: number) => ContactRelationship[];
  
  // 过滤和搜索
  setRelationshipFilter: (filter: Partial<RelationshipState['relationshipFilter']>) => void;
  clearRelationshipFilter: () => void;
  
  // 选择和UI
  setSelectedRelationship: (relationship: ContactRelationship | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 数据管理
  importRelationships: (relationships: ContactRelationship[]) => Promise<void>;
  exportRelationships: () => Promise<ContactRelationship[]>;
  clearAllRelationships: () => void;
  initializeSampleData: () => Promise<void>;
}

type RelationshipStore = RelationshipState & RelationshipActions;

// 辅助函数
const generateId = () => `rel_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const filterRelationships = (
  relationships: ContactRelationship[],
  filter: RelationshipState['relationshipFilter']
): ContactRelationship[] => {
  return relationships.filter(rel => {
    // 类型过滤
    if (filter.types.length > 0 && !filter.types.includes(rel.relationshipType)) {
      return false;
    }
    
    // 活跃状态过滤
    if (filter.isActive !== undefined && rel.isActive !== filter.isActive) {
      return false;
    }
    
    // 强度过滤
    if (filter.minStrength !== undefined && rel.strength < filter.minStrength) {
      return false;
    }
    
    if (filter.maxStrength !== undefined && rel.strength > filter.maxStrength) {
      return false;
    }
    
    return true;
  });
};

export const useRelationshipStore = create<RelationshipStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      relationships: [],
      networkGraphs: {},
      graphAnalyses: {},
      isLoading: false,
      error: null,
      selectedRelationship: null,
      relationshipFilter: {
        types: [],
      },

      // 关系CRUD操作
      addRelationship: async (relationshipData) => {
        const relationship: ContactRelationship = {
          ...relationshipData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          relationships: [...state.relationships, relationship],
          // 清除相关的图谱缓存
          networkGraphs: {},
          graphAnalyses: {},
        }));
      },

      updateRelationship: async (id, updates) => {
        set((state) => ({
          relationships: state.relationships.map(rel =>
            rel.id === id
              ? { ...rel, ...updates, updatedAt: new Date() }
              : rel
          ),
          // 清除相关的图谱缓存
          networkGraphs: {},
          graphAnalyses: {},
        }));
      },

      deleteRelationship: async (id) => {
        set((state) => ({
          relationships: state.relationships.filter(rel => rel.id !== id),
          selectedRelationship: state.selectedRelationship?.id === id ? null : state.selectedRelationship,
          // 清除相关的图谱缓存
          networkGraphs: {},
          graphAnalyses: {},
        }));
      },

      toggleRelationshipStatus: async (id) => {
        set((state) => ({
          relationships: state.relationships.map(rel =>
            rel.id === id
              ? { ...rel, isActive: !rel.isActive, updatedAt: new Date() }
              : rel
          ),
          // 清除相关的图谱缓存
          networkGraphs: {},
          graphAnalyses: {},
        }));
      },

      // 批量操作
      addMultipleRelationships: async (relationshipsData) => {
        const relationships = relationshipsData.map(data => ({
          ...data,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        }));

        set((state) => ({
          relationships: [...state.relationships, ...relationships],
          networkGraphs: {},
          graphAnalyses: {},
        }));
      },

      deleteMultipleRelationships: async (ids) => {
        set((state) => ({
          relationships: state.relationships.filter(rel => !ids.includes(rel.id)),
          networkGraphs: {},
          graphAnalyses: {},
        }));
      },

      // 图谱操作
      buildNetworkGraph: async (centerContactId, contacts, maxDepth = 3) => {
        const { relationships, networkGraphs } = get();
        const cacheKey = `${centerContactId}_${maxDepth}`;
        
        // 检查缓存
        if (networkGraphs[cacheKey]) {
          return networkGraphs[cacheKey];
        }

        try {
          set({ isLoading: true, error: null });
          
          const graph = relationshipGraphService.buildNetworkGraph(
            centerContactId,
            contacts,
            relationships,
            maxDepth
          );

          set((state) => ({
            networkGraphs: { ...state.networkGraphs, [cacheKey]: graph },
            isLoading: false,
          }));

          return graph;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '构建网络图谱失败';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      analyzeNetworkGraph: async (centerContactId, contacts) => {
        const { relationships, graphAnalyses } = get();
        
        // 检查缓存
        if (graphAnalyses[centerContactId]) {
          return graphAnalyses[centerContactId];
        }

        try {
          set({ isLoading: true, error: null });
          
          const graph = relationshipGraphService.buildNetworkGraph(
            centerContactId,
            contacts,
            relationships,
            3
          );
          
          const analysis = relationshipGraphService.analyzeNetworkGraph(graph, relationships);

          set((state) => ({
            graphAnalyses: { ...state.graphAnalyses, [centerContactId]: analysis },
            isLoading: false,
          }));

          return analysis;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '分析网络图谱失败';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      clearGraphCache: (contactId) => {
        if (contactId) {
          set((state) => {
            const newNetworkGraphs = { ...state.networkGraphs };
            const newGraphAnalyses = { ...state.graphAnalyses };
            
            // 清除特定联系人的缓存
            Object.keys(newNetworkGraphs).forEach(key => {
              if (key.startsWith(contactId)) {
                delete newNetworkGraphs[key];
              }
            });
            
            delete newGraphAnalyses[contactId];
            
            return {
              networkGraphs: newNetworkGraphs,
              graphAnalyses: newGraphAnalyses,
            };
          });
        } else {
          // 清除所有缓存
          set({
            networkGraphs: {},
            graphAnalyses: {},
          });
        }
      },

      // 关系发现
      findMutualConnections: (contactId) => {
        const { relationships } = get();
        return relationshipGraphService.findMutualConnections(contactId, relationships);
      },

      findShortestPath: (fromContactId, toContactId, contacts) => {
        const { relationships } = get();
        const pathAnalysis = relationshipGraphService.analyzeRelationshipPath(
          fromContactId,
          toContactId,
          contacts,
          relationships
        );
        return pathAnalysis.shortestPath;
      },

      recommendConnections: (contactId, contacts) => {
        const { relationships } = get();
        return relationshipGraphService.recommendConnections(contactId, contacts, relationships);
      },

      // 关系分析
      getRelationshipsByContact: (contactId) => {
        const { relationships } = get();
        return relationships.filter(rel => 
          rel.isActive && (rel.fromContactId === contactId || rel.toContactId === contactId)
        );
      },

      getRelationshipsByType: (type) => {
        const { relationships } = get();
        return relationships.filter(rel => rel.isActive && rel.relationshipType === type);
      },

      getStrongRelationships: (minStrength = 70) => {
        const { relationships } = get();
        return relationships.filter(rel => rel.isActive && rel.strength >= minStrength);
      },

      // 过滤和搜索
      setRelationshipFilter: (newFilter) => {
        set((state) => ({
          relationshipFilter: { ...state.relationshipFilter, ...newFilter },
        }));
      },

      clearRelationshipFilter: () => {
        set({
          relationshipFilter: { types: [] },
        });
      },

      // 选择和UI
      setSelectedRelationship: (relationship) => set({ selectedRelationship: relationship }),
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),

      // 数据管理
      importRelationships: async (relationships) => {
        set((state) => ({
          relationships: [...state.relationships, ...relationships],
          networkGraphs: {},
          graphAnalyses: {},
        }));
      },

      exportRelationships: async () => {
        const { relationships } = get();
        return relationships;
      },

      clearAllRelationships: () => {
        set({
          relationships: [],
          networkGraphs: {},
          graphAnalyses: {},
          selectedRelationship: null,
          error: null,
        });
      },

      initializeSampleData: async () => {
        const { relationships } = get();
        if (relationships.length === 0) {
          // 动态导入示例数据
          try {
            const { sampleRelationships } = await import('../data/sampleRelationships');
            set({
              relationships: sampleRelationships,
              networkGraphs: {},
              graphAnalyses: {},
            });
          } catch (error) {
            console.warn('无法加载示例关系数据:', error);
          }
        }
      },
    }),
    {
      name: 'relationship-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        relationships: state.relationships,
      }),
    }
  )
);

// 导出选择器
export const useRelationshipSelectors = {
  useFilteredRelationships: () => {
    const { relationships, relationshipFilter } = useRelationshipStore();
    return filterRelationships(relationships, relationshipFilter);
  },
  
  useContactRelationships: (contactId: string) => {
    const getRelationshipsByContact = useRelationshipStore(state => state.getRelationshipsByContact);
    return getRelationshipsByContact(contactId);
  },
  
  useNetworkGraph: (centerContactId: string, contacts: Contact[], maxDepth?: number) => {
    const buildNetworkGraph = useRelationshipStore(state => state.buildNetworkGraph);
    const networkGraphs = useRelationshipStore(state => state.networkGraphs);
    const cacheKey = `${centerContactId}_${maxDepth || 3}`;
    
    return {
      graph: networkGraphs[cacheKey],
      buildGraph: () => buildNetworkGraph(centerContactId, contacts, maxDepth),
    };
  },
  
  useGraphAnalysis: (centerContactId: string, contacts: Contact[]) => {
    const analyzeNetworkGraph = useRelationshipStore(state => state.analyzeNetworkGraph);
    const graphAnalyses = useRelationshipStore(state => state.graphAnalyses);
    
    return {
      analysis: graphAnalyses[centerContactId],
      analyzeGraph: () => analyzeNetworkGraph(centerContactId, contacts),
    };
  },
};
