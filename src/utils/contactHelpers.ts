import { 
  Contact, 
  ContactMethod, 
  Address, 
  SocialLink, 
  ImportantDate, 
  CustomField,
  RelationshipType,
  ContactPriority 
} from '../types';

/**
 * 联系人字段管理工具函数
 */

// 生成唯一ID
export const generateId = () => Date.now().toString() + Math.random().toString(36).substr(2, 9);

// 创建默认的联系人对象
export const createDefaultContact = (input: {
  name: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  company?: string;
  position?: string;
  notes?: string;
  tags?: string[];
  avatar?: string;
  isFavorite?: boolean;
  groupIds?: string[];
  relationshipType?: RelationshipType;
  priority?: ContactPriority;
}): Omit<Contact, 'id'> => {
  const now = new Date();
  
  return {
    // 基本信息
    name: input.name,
    firstName: input.firstName,
    lastName: input.lastName,
    avatar: input.avatar || '',
    
    // 工作信息
    company: input.company,
    position: input.position,
    
    // 联系方式 - 新结构
    phones: input.phone ? [{
      id: generateId(),
      type: 'phone' as const,
      label: 'mobile' as const,
      value: input.phone,
      isPrimary: true,
    }] : [],
    
    emails: input.email ? [{
      id: generateId(),
      type: 'email' as const,
      label: 'work' as const,
      value: input.email,
      isPrimary: true,
    }] : [],
    
    addresses: [],
    
    // 兼容性字段
    email: input.email,
    phone: input.phone,
    
    // 社交媒体和其他
    socialLinks: [],
    importantDates: [],
    customFields: [],
    
    // 分类和关系
    relationshipType: input.relationshipType || 'acquaintance',
    priority: input.priority || 'medium',
    groupIds: input.groupIds || [],
    tags: input.tags || [],
    
    // 状态
    notes: input.notes,
    isFavorite: input.isFavorite || false,
    isArchived: false,
    isBlocked: false,
    
    // 元数据
    source: 'manual',
    createdAt: now,
    updatedAt: now,
    
    // 统计
    interactionCount: 0,
    connectionStrength: 50, // 默认中等连接强度
  };
};

// 联系方式管理
export const addContactMethod = (
  contact: Contact,
  method: Omit<ContactMethod, 'id'>
): Contact => {
  const newMethod: ContactMethod = {
    ...method,
    id: generateId(),
  };

  const fieldName = method.type === 'phone' ? 'phones' : 'emails';
  const updatedMethods = [...contact[fieldName], newMethod];

  // 如果是第一个或设置为主要，更新其他为非主要
  if (newMethod.isPrimary || updatedMethods.length === 1) {
    updatedMethods.forEach((m, index) => {
      if (m.id !== newMethod.id) {
        m.isPrimary = false;
      }
    });
    newMethod.isPrimary = true;
  }

  return {
    ...contact,
    [fieldName]: updatedMethods,
    updatedAt: new Date(),
  };
};

export const updateContactMethod = (
  contact: Contact,
  methodId: string,
  updates: Partial<ContactMethod>
): Contact => {
  const updateField = (methods: ContactMethod[]) => {
    return methods.map(method => {
      if (method.id === methodId) {
        const updated = { ...method, ...updates };
        
        // 如果设置为主要，其他设为非主要
        if (updates.isPrimary) {
          methods.forEach(m => {
            if (m.id !== methodId) m.isPrimary = false;
          });
        }
        
        return updated;
      }
      return method;
    });
  };

  return {
    ...contact,
    phones: updateField(contact.phones),
    emails: updateField(contact.emails),
    updatedAt: new Date(),
  };
};

export const removeContactMethod = (
  contact: Contact,
  methodId: string
): Contact => {
  const removeFromField = (methods: ContactMethod[]) => {
    const filtered = methods.filter(m => m.id !== methodId);
    
    // 如果删除的是主要联系方式，设置第一个为主要
    if (filtered.length > 0 && !filtered.some(m => m.isPrimary)) {
      filtered[0].isPrimary = true;
    }
    
    return filtered;
  };

  return {
    ...contact,
    phones: removeFromField(contact.phones),
    emails: removeFromField(contact.emails),
    updatedAt: new Date(),
  };
};

// 地址管理
export const addAddress = (
  contact: Contact,
  address: Omit<Address, 'id'>
): Contact => {
  const newAddress: Address = {
    ...address,
    id: generateId(),
  };

  const updatedAddresses = [...contact.addresses, newAddress];

  // 如果是第一个或设置为主要，更新其他为非主要
  if (newAddress.isPrimary || updatedAddresses.length === 1) {
    updatedAddresses.forEach(addr => {
      if (addr.id !== newAddress.id) {
        addr.isPrimary = false;
      }
    });
    newAddress.isPrimary = true;
  }

  return {
    ...contact,
    addresses: updatedAddresses,
    updatedAt: new Date(),
  };
};

// 社交链接管理
export const addSocialLink = (
  contact: Contact,
  link: Omit<SocialLink, 'id'>
): Contact => {
  const newLink: SocialLink = {
    ...link,
    id: generateId(),
  };

  return {
    ...contact,
    socialLinks: [...contact.socialLinks, newLink],
    updatedAt: new Date(),
  };
};

// 重要日期管理
export const addImportantDate = (
  contact: Contact,
  date: Omit<ImportantDate, 'id'>
): Contact => {
  const newDate: ImportantDate = {
    ...date,
    id: generateId(),
  };

  return {
    ...contact,
    importantDates: [...contact.importantDates, newDate],
    updatedAt: new Date(),
  };
};

// 自定义字段管理
export const addCustomField = (
  contact: Contact,
  field: Omit<CustomField, 'id'>
): Contact => {
  const newField: CustomField = {
    ...field,
    id: generateId(),
  };

  return {
    ...contact,
    customFields: [...contact.customFields, newField],
    updatedAt: new Date(),
  };
};

// 获取主要联系方式
export const getPrimaryPhone = (contact: Contact): string | undefined => {
  const primary = contact.phones.find(p => p.isPrimary);
  return primary?.value || contact.phones[0]?.value || contact.phone;
};

export const getPrimaryEmail = (contact: Contact): string | undefined => {
  const primary = contact.emails.find(e => e.isPrimary);
  return primary?.value || contact.emails[0]?.value || contact.email;
};

export const getPrimaryAddress = (contact: Contact): Address | undefined => {
  return contact.addresses.find(a => a.isPrimary) || contact.addresses[0];
};

// 格式化地址
export const formatAddress = (address: Address): string => {
  const parts = [
    address.street,
    address.city,
    address.state,
    address.zipCode,
    address.country
  ].filter(Boolean);
  
  return parts.join(', ');
};

// 联系人搜索匹配
export const matchesSearchQuery = (contact: Contact, query: string): boolean => {
  if (!query) return true;
  
  const searchText = query.toLowerCase();
  
  return (
    contact.name.toLowerCase().includes(searchText) ||
    contact.company?.toLowerCase().includes(searchText) ||
    contact.position?.toLowerCase().includes(searchText) ||
    contact.notes?.toLowerCase().includes(searchText) ||
    contact.tags.some(tag => tag.toLowerCase().includes(searchText)) ||
    contact.phones.some(p => p.value.includes(searchText)) ||
    contact.emails.some(e => e.value.toLowerCase().includes(searchText))
  );
};
