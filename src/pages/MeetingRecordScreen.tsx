import React, { useState, useRef } from 'react';
import {
  ScrollView,
  Dimensions,
  FlatList,
  Image,
  Platform,
  StyleSheet,
  View,
  Text as RNText,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Switch
} from 'react-native';
import {
  Box,
  Text,
  Heading,
  VStack,
  HStack,
  Button,
  Pressable,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  Input,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';
import { useNavigation } from '@react-navigation/native';
import { useAlert } from "../context/AlertContext"; // 导入 useAlert
import { Calendar, CheckCircle, XCircle, HelpCircle, MapPin, Clock, Edit, Trash2, PlusCircle, MinusCircle, Info, ArrowLeft, Users, ListChecks, FileText, Search } from 'lucide-react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { Contact } from '../types';

const { width } = Dimensions.get('window');

interface Meeting {
  id: string;
  title: string;
  date: string;
  participants: string[];
  location: string;
  notes: string;
  tasks: string[];
}

// Mock data for contacts
const MOCK_CONTACTS: Contact[] = [
  { id: '1', name: 'Emma <PERSON>', avatar: 'https://api.a0.dev/assets/image?text=EJ&aspect=1:1', role: 'Product Manager', company: 'TechCorp' },
  { id: '2', name: 'Michael Chen', avatar: 'https://api.a0.dev/assets/image?text=MC&aspect=1:1', role: 'Senior Developer', company: 'DevSolutions' },
  { id: '3', name: 'Sarah Williams', avatar: 'https://api.a0.dev/assets/image?text=SW&aspect=1:1', role: 'Marketing Director', company: 'BrandEx' },
  { id: '4', name: 'David Rodriguez', avatar: 'https://api.a0.dev/assets/image?text=DR&aspect=1:1', role: 'CEO', company: 'StartupInc' },
  { id: '5', name: 'Lisa Wong', avatar: 'https://api.a0.dev/assets/image?text=LW&aspect=1:1', role: 'Investor', company: 'Capital Ventures' },
];

// Mock data for past meetings
const MOCK_PAST_MEETINGS = [
  { 
    id: '1', 
    title: 'Project Kickoff', 
    date: '2025-05-30T10:00:00', 
    participants: ['1', '3', '4'],
    location: 'Conference Room A',
    notes: 'Discussed project timeline and initial requirements. David wants to accelerate the launch date.',
    tasks: ['Create project roadmap', 'Schedule follow-up with technical team']
  },
  { 
    id: '2', 
    title: 'Investment Discussion', 
    date: '2025-06-01T14:00:00', 
    participants: ['5'],
    location: 'Virtual Meeting',
    notes: 'Lisa presented investment options and discussed terms.',
    tasks: ['Prepare financial projections', 'Draft proposal for next round']
  },
  { 
    id: '3', 
    title: 'Technical Review', 
    date: '2025-06-03T11:30:00', 
    participants: ['2'],
    location: 'Development Office',
    notes: 'Michael presented current technical challenges and proposed solutions.',
    tasks: ['Research alternative frameworks', 'Schedule code review session']
  },
];

const MeetingRecordScreen = () => {
  const { showAlert } = useAlert(); // 使用 useAlert
  const navigation = useNavigation();
  const toast = useToast();
  const [activeTab, setActiveTab] = useState('new'); // 'new' or 'history'
  
  // Form states for new meeting
  const [title, setTitle] = useState('');
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [location, setLocation] = useState('');
  const [notes, setNotes] = useState('');
  const [selectedParticipants, setSelectedParticipants] = useState<string[]>([]);
  const [tasks, setTasks] = useState<string[]>(['']);
  const [isVirtual, setIsVirtual] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Refs
  const notesInputRef = useRef(null);

  const resetForm = () => {
    setTitle('');
    setDate(new Date());
    setLocation('');
    setNotes('');
    setSelectedParticipants([]);
    setTasks(['']);
    setIsVirtual(false);
    setSearchQuery('');
  };
  
  // Handlers
  const handleDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setDate(selectedDate);
    }
  };
  
  const toggleParticipant = (id: string) => {
    if (selectedParticipants.includes(id)) {
      setSelectedParticipants(selectedParticipants.filter(pId => pId !== id));
    } else {
      setSelectedParticipants([...selectedParticipants, id]);
    }
  };
  
  const handleAddTask = () => {
    setTasks([...tasks, '']);
  };
  
  const updateTask = (text: string, index: number) => {
    const newTasks = [...tasks];
    newTasks[index] = text;
    setTasks(newTasks);
  };
  
  const removeTask = (index: number) => {
    if (tasks.length > 1) {
      const newTasks = [...tasks];
      newTasks.splice(index, 1);
      setTasks(newTasks);
    }
  };
  
  const handleCancel = () => {
    resetForm();
    // 可选择导航回上一页或切换 tab
    // navigation.goBack();
    // setActiveTab('history');
    showAlert({
      title: 'Cancelled',
      message: 'New meeting creation has been cancelled.',
      buttons: [{ text: 'OK', onPress: () => {} }]
    });
  };

  const handleSaveMeeting = () => {
    // Basic validation
    if (!title.trim()) {
      toast.show({
        placement: 'top',
        render: ({ id }: { id: string }) => (
          <Toast nativeID={`toast-${id}`} action="error" variant="accent">
            <ToastTitle>Validation Error</ToastTitle>
            <ToastDescription>Please enter a meeting title.</ToastDescription>
          </Toast>
        ),
      });
      return;
    }
    if (selectedParticipants.length === 0) {
      toast.show({
        placement: 'top',
        render: ({ id }: { id: string }) => (
          <Toast nativeID={`toast-${id}`} action="error" variant="accent">
            <ToastTitle>Validation Error</ToastTitle>
            <ToastDescription>Please select at least one participant.</ToastDescription>
          </Toast>
        ),
      });
      return;
    }

    // If all validations pass, proceed to save
    console.log('Meeting Saved:', { title, date, location, notes, selectedParticipants, tasks, isVirtual });
    toast.show({
      placement: 'top',
      render: ({ id }: { id: string }) => (
        <Toast nativeID={`toast-${id}`} action="success" variant="accent" onCloseComplete={() => console.log('Toast closed')}>
          <ToastTitle>Success</ToastTitle>
          <ToastDescription>Meeting saved successfully!</ToastDescription>
        </Toast>
      ),
    });
    resetForm(); 
    setActiveTab('history');
  };

  const filteredContacts = MOCK_CONTACTS.filter(contact => 
    contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (contact.company && contact.company.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderNewMeetingTab = () => (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <ScrollView style={styles.formContainer}>
        {/* Title Input */}
        <View style={styles.inputGroup}>
          <RNText style={styles.label}>Meeting Title *</RNText>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="Enter meeting title"
            placeholderTextColor="#AAA"
          />
        </View>
        
        {/* Date & Time Picker */}
        <View style={styles.inputGroup}>
          <RNText style={styles.label}>Date & Time *</RNText>
          <TouchableOpacity 
            style={styles.datePickerContainer}
            onPress={() => setShowDatePicker(true)}
          >
            <RNText style={styles.dateText}>
              {date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric', 
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </RNText>
            <Calendar size={20} color="#007AFF" />
          </TouchableOpacity>
          {showDatePicker && (
            <DateTimePicker
              value={date}
              mode="datetime"
              display="default"
              onChange={handleDateChange}
            />
          )}
        </View>
        
        {/* Location Input */}
        <View style={styles.inputGroup}>
          <View style={styles.labelRow}>
            <RNText style={styles.label}>Location</RNText>
            <View style={styles.switchContainer}>
              <RNText style={styles.switchLabel}>Virtual</RNText>
              <Switch
                value={isVirtual}
                onValueChange={setIsVirtual}
                trackColor={{ false: '#D1D1D6', true: '#ADD8E6' }}
                thumbColor={isVirtual ? '#007AFF' : '#F4F4F4'}
              />
            </View>
          </View>
          <TextInput
            style={styles.input}
            value={location}
            onChangeText={setLocation}
            placeholder={isVirtual ? "Enter meeting link or platform" : "Enter meeting location"}
            placeholderTextColor="#AAA"
          />
        </View>
        
        {/* Participants Section */}
        <View style={styles.inputGroup}>
          <RNText style={styles.label}>Participants *</RNText>
          <TextInput
            style={[styles.input, { marginBottom: 10 }]}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search contacts"
            placeholderTextColor="#AAA"
          />
          
          <View style={styles.participantContainer}>
            {filteredContacts.map(contact => (
              <TouchableOpacity
                key={contact.id}
                style={[
                  styles.participantItem,
                  selectedParticipants.includes(contact.id) && styles.selectedParticipant
                ]}
                onPress={() => toggleParticipant(contact.id)}
              >
                <Image 
                  source={{ uri: contact.avatar }} 
                  style={styles.participantAvatar} 
                />
                <View style={styles.participantInfo}>
                  <RNText style={styles.participantName}>{contact.name}</RNText>
                  <RNText style={styles.participantRole}>{contact.role}, {contact.company}</RNText>
                </View>
                {selectedParticipants.includes(contact.id) && (
                  <Search size={20} color="#AAA" style={styles.searchIcon} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Notes Section */}
        <View style={styles.inputGroup}>
          <RNText style={styles.label}>Meeting Notes</RNText>
          <TextInput
            ref={notesInputRef}
            style={[styles.input, styles.notesInput]}
            value={notes}
            onChangeText={setNotes}
            placeholder="Enter meeting notes, key points, action items..."
            placeholderTextColor="#AAA"
            multiline
            textAlignVertical="top"
          />
        </View>
        
        {/* Follow-up Tasks */}
        <View style={styles.inputGroup}>
          <RNText style={styles.label}>Follow-up Tasks</RNText>
          
          {tasks.map((task, index) => (
            <View key={index} style={styles.taskItem}>
              <TextInput
                style={styles.taskInput}
                value={task}
                onChangeText={(text) => updateTask(text, index)}
                placeholder="Enter a task"
                placeholderTextColor="#AAA"
              />
              <TouchableOpacity 
                style={styles.removeTaskButton}
                onPress={() => removeTask(index)}
              >
                <XCircle size={22} color="#FF3B30" />
              </TouchableOpacity>
            </View>
          ))}
          
          <TouchableOpacity
            style={styles.addTaskButton}
            onPress={handleAddTask}
          >
            <Calendar size={20} color="#555" style={styles.icon} />
            <RNText style={styles.addTaskText}>Add Task</RNText>
          </TouchableOpacity>
        </View>
        
        <View style={styles.formActionsContainer}>
          <TouchableOpacity style={[styles.button, styles.saveButton]} onPress={handleSaveMeeting}>
            <RNText style={[styles.buttonText, styles.saveButtonText]}>Save Meeting</RNText>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={handleCancel}>
            <RNText style={[styles.buttonText, styles.cancelButtonText]}>Cancel</RNText>
          </TouchableOpacity>
        </View>
        
        {/* Spacer for keyboard */}
        <View style={{ height: 50 }} />
      </ScrollView>
    </KeyboardAvoidingView>
  );

  const renderMeetingItem = (meeting: Meeting) => {
    const participants = meeting.participants.map(id =>
      MOCK_CONTACTS.find(c => c.id === id)
    ).filter((c): c is Contact => Boolean(c)); // Ensures participants are Contact objects

    return (
      <TouchableOpacity
        style={styles.meetingHistoryItem}
        onPress={() => {
          // In a real app, this would navigate to meeting details
        }}
      >
        <View style={styles.meetingHeader}>
          <RNText style={styles.meetingTitle}>{meeting.title}</RNText>
          <RNText style={styles.meetingDate}>{formatDate(meeting.date)}</RNText>
        </View>
        
        <View style={styles.meetingLocation}>
          <MapPin size={16} color="#666" style={{ marginRight: 4 }} />
          <RNText style={styles.meetingLocationText}>{meeting.location}</RNText>
        </View>
        
        <View style={styles.meetingParticipants}>
          <RNText style={styles.participantsLabel}>Participants:</RNText>
          <View style={styles.avatarRow}>
            {participants.slice(0, 3).map((contact, index) => (
              <Image 
                key={contact.id} 
                source={{ uri: contact.avatar }} 
                style={[styles.historyAvatar, { marginLeft: index > 0 ? -10 : 0 }]} 
              />
            ))}
            {participants.length > 3 && (
              <View style={[styles.historyAvatar, styles.extraParticipants]}>
                <RNText style={styles.extraParticipantsText}>+{participants.length - 3}</RNText>
              </View>
            )}
          </View>
        </View>
        
        {meeting.tasks && meeting.tasks.length > 0 && (
          <View style={styles.meetingTasks}>
            <RNText style={styles.tasksLabel}>Follow-ups:</RNText>
            {meeting.tasks.slice(0, 2).map((task, index) => (
              <View key={index} style={styles.taskRow}>
                <FileText size={20} color="#555" style={styles.icon} />
                <RNText style={styles.taskText} numberOfLines={1}>{task}</RNText>
              </View>
            ))}
            {meeting.tasks.length > 2 && (
              <RNText style={styles.moreTasks}>+{meeting.tasks.length - 2} more tasks</RNText>
            )}
          </View>
        )}
        
        <View style={styles.meetingActions}>
          <TouchableOpacity style={styles.actionButton} onPress={() => showAlert({ title: 'Share Meeting', message: 'Share feature is not yet implemented.', buttons: [{ text: 'OK', onPress: () => {} }] }) }>
            <Ionicons name="share-social-outline" size={22} color="#007AFF" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={() => console.log('Edit meeting', meeting.id)}>
            <Edit size={22} color="#555" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={() => console.log('Delete meeting', meeting.id)}>
            <Trash2 size={22} color="#D32F2F" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };
  
  const renderHistoryTab = () => (
    <FlatList
      data={MOCK_PAST_MEETINGS}
      renderItem={({ item }) => renderMeetingItem(item)}
      keyExtractor={item => item.id}
      contentContainerStyle={styles.historyList}
    />
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color="#007AFF" />
        </TouchableOpacity>
        
        <RNText style={styles.headerTitle}>
          {activeTab === 'new' ? 'Record Meeting' : 'Meeting History'}
        </RNText>
        
        <View style={styles.rightPlaceholder} />
      </View>
      
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'new' && styles.activeTabButton]}
          onPress={() => setActiveTab('new')}
        >
          <Ionicons 
            name="add-circle-outline" 
            size={20} 
            color={activeTab === 'new' ? "#007AFF" : "#666"} 
            style={styles.tabIcon}
          />
          <RNText 
            style={[
              styles.tabText,
              activeTab === 'new' && styles.activeTabText
            ]}
          >
            New Record
          </RNText>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'history' && styles.activeTabButton]}
          onPress={() => setActiveTab('history')}
        >
          <Clock 
            size={20} 
            color={activeTab === 'history' ? "#007AFF" : "#666"} 
            style={styles.tabIcon}
          />
          <RNText 
            style={[
              styles.tabText,
              activeTab === 'history' && styles.activeTabText
            ]}
          >
            History
          </RNText>
        </TouchableOpacity>
      </View>
      
      <View style={styles.contentContainer}>
        {activeTab === 'new' ? renderNewMeetingTab() : renderHistoryTab()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 15,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
  },
  contentContainer: {
    flex: 1,
  },
  formContainer: {
    padding: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#444',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E1E1E1',
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
    marginBottom: 18,
  },
  notesInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  datePickerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E1E1E1',
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginBottom: 18,
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 18,
  },
  participantContainer: {
    marginBottom: 18,
  },
  searchInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E1E1E1',
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
    marginBottom: 10,
  },
  participantList: {
    maxHeight: 200,
  },
  participantItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  participantAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 10,
  },
  participantInfo: {
    flex: 1,
  },
  participantName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  participantRole: {
    fontSize: 14,
    color: '#666',
  },
  checkIcon: {
    marginLeft: 10,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskInput: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E1E1E1',
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  removeTaskButton: {
    padding: 8,
    marginLeft: 8,
  },
  addTaskButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  addTaskText: {
    fontSize: 16,
    color: '#007AFF',
    marginLeft: 5,
  },
  formActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 30,
    marginBottom: 20,
  },
  button: {
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 5,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  saveButton: {
    backgroundColor: '#007AFF',
  },
  saveButtonText: {
    color: '#FFFFFF',
  },
  cancelButton: {
    backgroundColor: '#EFEFF4',
  },
  cancelButtonText: {
    color: '#007AFF',
  },
  historyList: {
    padding: 16,
  },
  meetingHistoryItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 1px 3px 0px rgba(0, 0, 0, 0.1)',
    } : {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: Platform.OS === 'android' ? 2 : 0,
    }),
  },
  meetingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  meetingTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  meetingDate: {
    fontSize: 14,
    color: '#666',
  },
  meetingLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  meetingLocationText: {
    fontSize: 14,
    color: '#666',
  },
  meetingParticipants: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  participantsLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
    marginRight: 8,
  },
  avatarRow: {
    flexDirection: 'row',
  },
  historyAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  extraParticipants: {
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
    width: 30,
    height: 30,
    borderRadius: 15,
    marginLeft: -10,
  },
  extraParticipantsText: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
  },
  meetingTasks: {
    marginTop: 5,
    marginBottom: 15,
  },
  tasksLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
    marginBottom: 5,
  },
  taskRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  taskText: {
    fontSize: 14,
    color: '#444',
    flex: 1,
  },
  moreTasks: {
    fontSize: 13,
    color: '#007AFF',
    marginTop: 2,
    marginLeft: 21,
  },
  meetingActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    paddingTop: 10,
    marginTop: 5,
  },
  actionButton: {
    padding: 8,
    marginLeft: 10,
  },
    inputGroup: {
      marginBottom: 20,
    },
    labelRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    switchLabel: {
      fontSize: 16,
      color: '#444',
      marginRight: 8,
    },
    selectedParticipant: {
      backgroundColor: '#E6F2FF', // 浅蓝色背景表示选中
    },
    backButton: {
      padding: 5, // 为返回按钮提供一些内边距，方便点击
    },
    rightPlaceholder: {
      width: 30, // 大致与一个图标按钮的宽度相当，用于平衡头部布局
    },
    tabIcon: {
      marginBottom: 2, // 标签中图标和文字间的小间距
    },
    icon: { // 通用图标样式，例如右边距
      marginRight: 5,
    },
    searchIcon: { // 用于参与者列表等场景的图标样式
      marginLeft: 10, // 与原 checkIcon 类似
    },
});

export default MeetingRecordScreen;