---
trigger: always_on
---

# APP 设计规范与标准 

本文档总结了 APP UI 重构所遵循的设计规范和标准，旨在为开发过程提供统一的参考。

## 1. 关键尺寸和间距

### 1.1 屏幕适配
- **目标设备**: 主要适配 iPhone 14 系列 (屏幕比例 19.5:9)。
- **响应式设计**: 需兼顾其他常见屏幕尺寸 (如 iPhone SE、中型安卓机型)，确保组件在小屏幕上布局合理，不超出边界。

### 1.2 关键间距
- **卡片间距**: 16px (卡片与卡片之间，卡片与屏幕边缘的水平间距)
- **按钮内边距**:
    - 上下: 12px
    - 左右: 24px
- **图标与文本间距**: 8px
- **页面顶部标题与内容区**: 24px

### 1.3 组件尺寸
- **圆形按钮 (例如 "Add All Device" 按钮)**: 直径 56px
- **温度滑块 (圆形)**: 直径 200px
- **开关按钮**:
    - 宽度: 40px
    - 高度: 24px
- **卡片**:
    - 高度: 约 80px (具体根据内容调整)
    - 宽度: 屏幕等宽，减去左右各 16px 的边距

## 2. 颜色值 (Color Palette)

### 2.1 主色 (Primary)
- **蓝色 (按钮、高亮、主要交互元素)**: `#007AFF`

### 2.2 背景色 (Background)
- **浅灰背景 (页面背景)**: `#F5F5F5`
- **白色背景 (卡片、模态框等)**: `#FFFFFF`

### 2.3 文本颜色 (Text)
- **主标题/重要文本 (深灰)**: `#333333`
- **次级文本/常规正文 (中灰)**: `#666666`
- **占位提示文本/辅助信息 (浅灰)**: `#999999`

### 2.4 辅助颜色 (Auxiliary)
- **蓝色高亮状态 (主色悬停或强调)**: `#0056D2`
- **错误状态 (红色)**: `#FF3B30`
- **成功状态 (绿色)**: `#34C759`

### 2.5 边框颜色 (Border)
- **默认边框/分割线**: `#E0E0E0`

## 3. 字体 (Typography)

### 3.1 字体家族
- **iOS**: SF Pro Display
- **Android**: (若 SF Pro Display 不可用，考虑使用系统默认 Sans-Serif 或选择一个相似的开源字体如 Inter/Roboto 作为回退方案)

### 3.2 字体大小与字重
- **页面/模块大标题**: 18px, Bold
- **正文/主要内容**: 16px, Regular
- **次级文本/辅助说明**: 14px, Regular
- **占位符/更次要信息**: 12px, Light

### 3.3 字体颜色
- **主标题/重要文本**: `#333333`
- **次级文本/常规正文**: `#666666`

## 4. 组件状态

### 4.1 按钮 (Button)
- **默认态**:
    - 背景色: `#007AFF`
    - 文本颜色: `#FFFFFF`
- **悬停/按下态 (Hover/Pressed)**:
    - 背景色: `#0056D2`
    - 文本颜色: `#FFFFFF`
- **禁用态 (Disabled)**:
    - 背景色: `#E0E0E0`
    - 文本颜色: `#999999`

### 4.2 输入框 (Input Field)
- **默认态**:
    - 边框颜色: `#E0E0E0`
    - 文本颜色: `#333333`
- **聚焦态 (Focus)**:
    - 边框颜色: `#007AFF`
- **错误态 (Error)**:
    - 边框颜色: `#FF3B30`
    - 错误提示文本颜色: `#FF3B30`

### 4.3 开关按钮 (Switch)
- **开启状态**:
    - 背景色: `#007AFF`
    - 圆点颜色: `#FFFFFF`
- **关闭状态**:
    - 背景色: `#E0E0E0` (滑轨背景)
    - 圆点颜色: `#FFFFFF`

### 4.4 滑块 (Slider - 如温度调节器)
- **默认态**:
    - 滑块轨道颜色: `#E0E0E0`
    - 滑块圆点/激活部分颜色: `#007AFF`
- **禁用态**:
    - 滑块轨道颜色: `#CCCCCC`
    - 滑块圆点/激活部分颜色: `#999999`

## 5. 图标 (Icons)
- **图标库**: `lucide-react-native`
- **间距**: 图标与相邻文本间距为 8px。

## 6. 复杂交互或动画说明

### 6.1 温度滑块
- **交互**:
    - 用户滑动时，滑块圆点沿轨道移动。
    - 滑块中心的温度值根据滑块位置动态更新。
- **动画**:
    - 圆点移动时有 0.3s 的平滑过渡动画。
    - 温度值变化时有数字滚动或平滑过渡效果。

### 6.2 设备添加
- **交互**:
    - 点击“Add All Device”后，显示加载动画 (如旋转的圆形图标)。
    - 加载完成后，跳转至主页面或显示成功提示。
- **动画**:
    - 加载图标使用旋转动画，建议持续时间约 1s 或直到操作完成。

### 6.3 定时器设置 (Schedule)
- **交互**:
    - 点击“Schedule”时间段后，弹出平台标准的时间选择器 (Time Picker)。
    - 用户选择时间后，点击“确认”保存，更新界面显示的时间。
- **动画**:
    - 时间选择器的弹出和收回动画遵循平台默认行为，或使用 0.2s 的平滑/弹性动画（如果自定义）。

## 7. Gluestack UI 主题配置注意事项
- **Tokens**: 将上述颜色、间距、字体大小等定义为 Gluestack UI 的主题 tokens。
- **组件变体**: 利用 Gluestack UI 的变体 (variants) 和属性 (props) 来实现不同状态的样式。
- **响应式**: 使用 Gluestack UI 提供的响应式工具 (如 `sx` prop, 响应式工具类) 来处理不同屏幕尺寸的适配。

---
这份文档将作为我们 UI 重构的主要参考。
