import { useState } from 'react';

export interface SettingsState {
  // Notifications
  notificationsEnabled: boolean;
  emailNotifications: boolean;
  reminderNotifications: boolean;
  networkUpdates: boolean;
  
  // App Settings
  dataSync: boolean;
  darkMode: boolean;
  aiSuggestions: boolean;
  autoTagging: boolean;
  
  // Security
  biometricLogin: boolean;
  locationTracking: boolean;
}

const defaultSettings: SettingsState = {
  notificationsEnabled: true,
  emailNotifications: true,
  reminderNotifications: true,
  networkUpdates: true,
  dataSync: true,
  darkMode: false,
  aiSuggestions: true,
  autoTagging: true,
  biometricLogin: false,
  locationTracking: false,
};

/**
 * 设置管理Hook
 * 提供设置状态管理和重置功能
 */
export const useSettings = () => {
  const [settings, setSettings] = useState<SettingsState>(defaultSettings);

  const updateSetting = <K extends keyof SettingsState>(
    key: K,
    value: SettingsState[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
  };

  const resetToDefaults = () => {
    setSettings(defaultSettings);
  };

  return {
    settings,
    updateSetting,
    resetSettings,
    resetToDefaults
  };
};
