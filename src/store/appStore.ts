import { create } from 'zustand';
import { AppStore } from './types';

/**
 * App Store
 * 管理应用全局状态，如加载状态、错误状态、网络状态等
 */
export const useAppStore = create<AppStore>((set, get) => ({
  // Initial state
  isLoading: false,
  isContactsLoading: false,
  isSettingsLoading: false,
  error: null,
  contactsError: null,
  settingsError: null,
  isOnline: true,
  isAuthenticated: false,
  version: '1.0.0',
  buildNumber: '1',
  
  // Actions
  setLoading: (loading) => {
    set((state) => ({
      ...state,
      isLoading: loading,
    }));
  },
  
  setError: (error) => {
    set((state) => ({
      ...state,
      error,
    }));
  },
  
  setOnlineStatus: (isOnline) => {
    set((state) => ({
      ...state,
      isOnline,
    }));
  },
  
  setAuthenticated: (isAuthenticated) => {
    set((state) => ({
      ...state,
      isAuthenticated,
    }));
  },
  
  clearErrors: () => {
    set((state) => ({
      ...state,
      error: null,
      contactsError: null,
      settingsError: null,
    }));
  },
}));

// Selectors for better performance
export const useAppSelectors = {
  // Loading states
  useLoadingStates: () => useAppStore((state) => ({
    isLoading: state.isLoading,
    isContactsLoading: state.isContactsLoading,
    isSettingsLoading: state.isSettingsLoading,
  })),
  
  // Error states
  useErrorStates: () => useAppStore((state) => ({
    error: state.error,
    contactsError: state.contactsError,
    settingsError: state.settingsError,
  })),
  
  // Network and auth
  useConnectionState: () => useAppStore((state) => ({
    isOnline: state.isOnline,
    isAuthenticated: state.isAuthenticated,
  })),
  
  // App info
  useAppInfo: () => useAppStore((state) => ({
    version: state.version,
    buildNumber: state.buildNumber,
  })),
};

// Actions selectors
export const useAppActions = () => useAppStore((state) => ({
  setLoading: state.setLoading,
  setError: state.setError,
  setOnlineStatus: state.setOnlineStatus,
  setAuthenticated: state.setAuthenticated,
  clearErrors: state.clearErrors,
}));
