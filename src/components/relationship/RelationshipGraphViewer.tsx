/**
 * 关系图谱可视化组件
 * 显示联系人之间的关系网络，支持多层级关系和路径分析
 */

import React, { useState, useMemo, useEffect } from 'react';
import { Dimensions } from 'react-native';
import Svg, { Line, Circle, Text as SvgText, G } from 'react-native-svg';
import {
  Box,
  Text,
  VStack,
  HStack,
  Heading,
  Pressable,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  Badge,
  BadgeText,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  ScrollView,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';
import {
  X,
  Users,
  ArrowRight,
  Share2,
  Eye,
  MessageCircle,
  Plus,
  Filter,
  Search,
  Target,
} from 'lucide-react-native';

import { StandardButton, StandardIcon, CircularButton } from '../ui';
import { 
  NetworkGraph, 
  NetworkGraphNode, 
  NetworkGraphEdge, 
  Contact,
  ContactRelationship,
  ContactRelationshipType 
} from '../../types';
import { relationshipGraphService, PathAnalysis } from '../../services/relationshipGraphService';

interface RelationshipGraphViewerProps {
  centerContactId: string;
  contacts: Contact[];
  relationships: ContactRelationship[];
  maxDepth?: number;
  onContactPress?: (contactId: string) => void;
  onAddRelationship?: (fromId: string, toId: string) => void;
}

export const RelationshipGraphViewer: React.FC<RelationshipGraphViewerProps> = ({
  centerContactId,
  contacts,
  relationships,
  maxDepth = 3,
  onContactPress,
  onAddRelationship,
}) => {
  const toast = useToast();
  const { width, height } = Dimensions.get('window');
  
  // 状态管理
  const [selectedNode, setSelectedNode] = useState<NetworkGraphNode | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<NetworkGraphEdge | null>(null);
  const [showNodeModal, setShowNodeModal] = useState(false);
  const [showPathModal, setShowPathModal] = useState(false);
  const [pathAnalysis, setPathAnalysis] = useState<PathAnalysis | null>(null);
  const [highlightedPath, setHighlightedPath] = useState<string[]>([]);
  const [filterType, setFilterType] = useState<ContactRelationshipType | 'all'>('all');

  // 构建网络图谱
  const networkGraph = useMemo(() => {
    try {
      return relationshipGraphService.buildNetworkGraph(
        centerContactId,
        contacts,
        relationships,
        maxDepth
      );
    } catch (error) {
      console.error('构建网络图谱失败:', error);
      return { nodes: [], edges: [], centerNodeId: centerContactId, maxDepth };
    }
  }, [centerContactId, contacts, relationships, maxDepth]);

  // 过滤后的图谱
  const filteredGraph = useMemo(() => {
    if (filterType === 'all') return networkGraph;
    
    const filteredEdges = networkGraph.edges.filter(edge => edge.relationshipType === filterType);
    const connectedNodeIds = new Set<string>();
    
    filteredEdges.forEach(edge => {
      connectedNodeIds.add(edge.fromId);
      connectedNodeIds.add(edge.toId);
    });
    
    // 始终包含中心节点
    connectedNodeIds.add(centerContactId);
    
    const filteredNodes = networkGraph.nodes.filter(node => connectedNodeIds.has(node.id));
    
    return {
      ...networkGraph,
      nodes: filteredNodes,
      edges: filteredEdges,
    };
  }, [networkGraph, filterType, centerContactId]);

  // 计算节点位置（圆形布局）
  const nodesWithPositions = useMemo(() => {
    const centerX = width / 2;
    const centerY = height / 2.5;
    const baseRadius = 80;
    
    return filteredGraph.nodes.map((node, index) => {
      if (node.id === centerContactId) {
        return { ...node, x: centerX, y: centerY };
      }
      
      // 按层级分布
      const level = node.level || 1;
      const radius = baseRadius * level;
      const nodesAtLevel = filteredGraph.nodes.filter(n => n.level === level).length;
      const angleStep = (2 * Math.PI) / Math.max(nodesAtLevel, 1);
      const nodeIndexAtLevel = filteredGraph.nodes
        .filter(n => n.level === level)
        .findIndex(n => n.id === node.id);
      
      const angle = nodeIndexAtLevel * angleStep;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      
      return { ...node, x, y };
    });
  }, [filteredGraph, centerContactId, width, height]);

  // 处理节点点击
  const handleNodePress = (node: NetworkGraphNode) => {
    setSelectedNode(node);
    setShowNodeModal(true);
  };

  // 处理路径分析
  const handlePathAnalysis = (targetNodeId: string) => {
    if (targetNodeId === centerContactId) return;
    
    const analysis = relationshipGraphService.analyzeRelationshipPath(
      centerContactId,
      targetNodeId,
      contacts,
      relationships
    );
    
    setPathAnalysis(analysis);
    setShowPathModal(true);
    
    // 高亮最短路径
    if (analysis.shortestPath.length > 0) {
      setHighlightedPath(analysis.shortestPath);
    }
  };

  // 获取关系类型颜色
  const getRelationshipColor = (type: ContactRelationshipType) => {
    const colors: Record<ContactRelationshipType, string> = {
      introduced_by: '$success500',
      introduced_to: '$success600',
      mutual_friend: '$primary500',
      colleague: '$blue500',
      family_friend: '$purple500',
      business_partner: '$orange500',
      mentor_mentee: '$indigo500',
      client_vendor: '$teal500',
      other: '$gray500',
    };
    return colors[type] || '$gray500';
  };

  // 获取关系类型标签
  const getRelationshipLabel = (type: ContactRelationshipType) => {
    const labels: Record<ContactRelationshipType, string> = {
      introduced_by: '介绍',
      introduced_to: '被介绍',
      mutual_friend: '共同朋友',
      colleague: '同事',
      family_friend: '家庭朋友',
      business_partner: '商业伙伴',
      mentor_mentee: '师生',
      client_vendor: '客户关系',
      other: '其他',
    };
    return labels[type] || type;
  };

  // 获取节点颜色
  const getNodeColor = (node: NetworkGraphNode) => {
    if (node.id === centerContactId) return '$primary600';
    if (highlightedPath.includes(node.id)) return '$success500';
    
    switch (node.level) {
      case 1: return '$blue500';
      case 2: return '$purple500';
      case 3: return '$orange500';
      default: return '$gray500';
    }
  };

  // 获取姓名首字母
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // 渲染SVG连接线
  const renderSVGConnections = () => {
    return filteredGraph.edges.map((edge, index) => {
      const fromNode = nodesWithPositions.find(n => n.id === edge.fromId);
      const toNode = nodesWithPositions.find(n => n.id === edge.toId);

      if (!fromNode || !toNode || !fromNode.x || !fromNode.y || !toNode.x || !toNode.y) {
        return null;
      }

      const isHighlighted = highlightedPath.includes(edge.fromId) && highlightedPath.includes(edge.toId);
      const strokeWidth = isHighlighted ? 3 : Math.max(1, edge.strength / 30);
      const opacity = isHighlighted ? 1 : 0.6;

      // 获取颜色的实际值
      const getColorValue = (colorToken: string) => {
        switch (colorToken) {
          case '$success500': return '#22c55e';
          case '$primary500': return '#3b82f6';
          case '$blue500': return '#3b82f6';
          case '$purple500': return '#a855f7';
          case '$orange500': return '#f97316';
          case '$indigo500': return '#6366f1';
          case '$teal500': return '#14b8a6';
          case '$gray500': return '#6b7280';
          default: return '#6b7280';
        }
      };

      const strokeColor = getColorValue(isHighlighted ? '$success500' : getRelationshipColor(edge.relationshipType));

      return (
        <Line
          key={`edge-${edge.id}-${index}`}
          x1={fromNode.x}
          y1={fromNode.y}
          x2={toNode.x}
          y2={toNode.y}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
          opacity={opacity}
          strokeDasharray={edge.relationshipType === 'other' ? '5,5' : undefined}
        />
      );
    });
  };

  // 渲染节点
  const renderNodes = () => {
    return nodesWithPositions.map((node) => {
      if (!node.x || !node.y) return null;
      
      const nodeSize = node.id === centerContactId ? 60 : 50;
      const isHighlighted = highlightedPath.includes(node.id);
      const nodeColor = getNodeColor(node);

      return (
        <Pressable
          key={node.id}
          position="absolute"
          left={node.x - nodeSize / 2}
          top={node.y - nodeSize / 2}
          onPress={() => handleNodePress(node)}
          onLongPress={() => handlePathAnalysis(node.id)}
        >
          <Box
            width={nodeSize}
            height={nodeSize}
            borderRadius={nodeSize / 2}
            bg={nodeColor}
            borderWidth={isHighlighted ? 3 : 2}
            borderColor={isHighlighted ? '$success600' : 'white'}
            justifyContent="center"
            alignItems="center"
            sx={{
              shadowColor: '$neutral900',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
              elevation: 4,
            }}
          >
            {node.avatar ? (
              <Avatar size={node.id === centerContactId ? 'lg' : 'md'} bg={nodeColor}>
                <AvatarImage source={{ uri: node.avatar }} alt={node.name} />
                <AvatarFallbackText>{getInitials(node.name)}</AvatarFallbackText>
              </Avatar>
            ) : (
              <Text
                fontWeight="$bold"
                color="white"
                size={node.id === centerContactId ? 'lg' : 'md'}
              >
                {getInitials(node.name)}
              </Text>
            )}
          </Box>

          {/* 节点标签 */}
          <Box
            position="absolute"
            top={nodeSize + 5}
            left={-20}
            width={nodeSize + 40}
            alignItems="center"
          >
            <Text
              size="xs"
              fontWeight="$medium"
              color="$textLight700"
              textAlign="center"
              numberOfLines={1}
              sx={{ _dark: { color: '$textDark300' } }}
            >
              {node.name}
            </Text>
            
            {/* 层级指示 */}
            {node.level > 0 && (
              <Badge size="xs" variant="outline" mt="$1">
                <BadgeText>{node.level}度</BadgeText>
              </Badge>
            )}
          </Box>
        </Pressable>
      );
    });
  };

  return (
    <Box flex={1} bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark950' } }}>
      {/* 工具栏 */}
      <Box p="$4" bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark900' } }}>
        <VStack space="md">
          <HStack justifyContent="space-between" alignItems="center">
            <Heading size="sm">关系图谱</Heading>
            <HStack space="sm">
              <CircularButton
                icon={Filter}
                size="sm"
                variant="outline"
                onPress={() => {
                  // TODO: 显示过滤选项
                }}
              />
              <CircularButton
                icon={Search}
                size="sm"
                variant="outline"
                onPress={() => {
                  // TODO: 显示搜索功能
                }}
              />
            </HStack>
          </HStack>

          {/* 统计信息 */}
          <HStack space="lg" justifyContent="space-around">
            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$primary600">
                {filteredGraph.nodes.length}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                联系人
              </Text>
            </VStack>
            
            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$success600">
                {filteredGraph.edges.length}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                关系
              </Text>
            </VStack>
            
            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$warning600">
                {Math.max(...filteredGraph.nodes.map(n => n.level || 0))}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                最大层级
              </Text>
            </VStack>
          </HStack>
        </VStack>
      </Box>

      {/* 图谱可视化区域 */}
      <Box flex={1} position="relative" overflow="hidden">
        {filteredGraph.nodes.length > 0 ? (
          <>
            {/* SVG图层 - 渲染连接线 */}
            <Svg
              width={width}
              height={height * 0.7}
              style={{ position: 'absolute', top: 0, left: 0 }}
            >
              {renderSVGConnections()}
            </Svg>

            {/* React Native组件图层 - 渲染节点 */}
            {renderNodes()}
          </>
        ) : (
          <Box flex={1} justifyContent="center" alignItems="center" p="$8">
            <VStack space="lg" alignItems="center">
              <StandardIcon as={Users} size="4xl" color="$textLight400" />
              <VStack space="sm" alignItems="center">
                <Text 
                  textAlign="center" 
                  fontWeight="$medium" 
                  color="$textLight500" 
                  sx={{ _dark: { color: '$textDark500' } }}
                >
                  暂无关系数据
                </Text>
                <Text 
                  textAlign="center" 
                  color="$textLight400" 
                  sx={{ _dark: { color: '$textDark600' } }}
                >
                  开始添加联系人之间的关系
                </Text>
              </VStack>
              <StandardButton
                variant="outline"
                leftIcon={Plus}
                onPress={() => onAddRelationship?.(centerContactId, '')}
              >
                添加关系
              </StandardButton>
            </VStack>
          </Box>
        )}
      </Box>

      {/* 操作提示 */}
      <Box 
        position="absolute" 
        bottom="$4" 
        left="$4" 
        right="$4"
        bg="$backgroundLight0"
        p="$3"
        borderRadius="$lg"
        sx={{
          _dark: { bg: '$backgroundDark900' },
          shadowColor: '$neutral900',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 4,
        }}
      >
        <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }} textAlign="center">
          点击节点查看详情 • 长按分析关系路径
        </Text>
      </Box>

      {/* 节点详情模态框 */}
      {selectedNode && (
        <Modal isOpen={showNodeModal} onClose={() => setShowNodeModal(false)}>
          <ModalBackdrop />
          <ModalContent maxWidth="$96">
            <ModalHeader>
              <VStack space="md" alignItems="center">
                <Avatar size="xl" bg={getNodeColor(selectedNode)}>
                  <AvatarFallbackText>{getInitials(selectedNode.name)}</AvatarFallbackText>
                  {selectedNode.avatar && (
                    <AvatarImage source={{ uri: selectedNode.avatar }} alt={selectedNode.name} />
                  )}
                </Avatar>
                <VStack space="xs" alignItems="center">
                  <Heading size="lg">{selectedNode.name}</Heading>
                  {selectedNode.company && (
                    <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                      {selectedNode.position ? `${selectedNode.position} at ${selectedNode.company}` : selectedNode.company}
                    </Text>
                  )}
                  <Badge size="sm" variant="outline">
                    <BadgeText>{selectedNode.level}度联系人</BadgeText>
                  </Badge>
                </VStack>
              </VStack>
              <ModalCloseButton>
                <StandardIcon as={X} />
              </ModalCloseButton>
            </ModalHeader>

            <ModalBody>
              <VStack space="md">
                {/* 连接信息 */}
                <VStack space="sm">
                  <Text fontWeight="$bold">连接信息</Text>
                  <HStack justifyContent="space-between">
                    <Text>连接强度:</Text>
                    <Text fontWeight="$medium">{selectedNode.connectionStrength || 0}%</Text>
                  </HStack>
                  <HStack justifyContent="space-between">
                    <Text>关系层级:</Text>
                    <Text fontWeight="$medium">{selectedNode.level}度</Text>
                  </HStack>
                </VStack>
              </VStack>
            </ModalBody>

            <ModalFooter>
              <HStack space="md" flex={1}>
                <StandardButton
                  variant="outline"
                  onPress={() => handlePathAnalysis(selectedNode.id)}
                  flex={1}
                  leftIcon={Target}
                >
                  分析路径
                </StandardButton>
                <StandardButton
                  variant="solid"
                  action="primary"
                  onPress={() => {
                    setShowNodeModal(false);
                    onContactPress?.(selectedNode.id);
                  }}
                  flex={1}
                  leftIcon={Eye}
                >
                  查看详情
                </StandardButton>
              </HStack>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {/* 路径分析模态框 */}
      {pathAnalysis && (
        <Modal isOpen={showPathModal} onClose={() => setShowPathModal(false)}>
          <ModalBackdrop />
          <ModalContent maxWidth="$96">
            <ModalHeader>
              <Heading size="lg">关系路径分析</Heading>
              <ModalCloseButton>
                <StandardIcon as={X} />
              </ModalCloseButton>
            </ModalHeader>

            <ModalBody>
              <VStack space="lg">
                {/* 最短路径 */}
                {pathAnalysis.shortestPath.length > 0 && (
                  <VStack space="sm">
                    <Text fontWeight="$bold">最短路径</Text>
                    <HStack space="xs" alignItems="center" flexWrap="wrap">
                      {pathAnalysis.shortestPath.map((contactId, index) => {
                        const contact = contacts.find(c => c.id === contactId);
                        return (
                          <React.Fragment key={contactId}>
                            <Text fontWeight="$medium">{contact?.name || contactId}</Text>
                            {index < pathAnalysis.shortestPath.length - 1 && (
                              <StandardIcon as={ArrowRight} size="xs" color="$textLight500" />
                            )}
                          </React.Fragment>
                        );
                      })}
                    </HStack>
                    <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                      {pathAnalysis.shortestPath.length - 1} 度分离
                    </Text>
                  </VStack>
                )}

                {/* 共同联系人 */}
                {pathAnalysis.commonConnections.length > 0 && (
                  <VStack space="sm">
                    <Text fontWeight="$bold">共同联系人</Text>
                    <VStack space="xs">
                      {pathAnalysis.commonConnections.slice(0, 5).map(contactId => {
                        const contact = contacts.find(c => c.id === contactId);
                        return (
                          <Text key={contactId} color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                            • {contact?.name || contactId}
                          </Text>
                        );
                      })}
                    </VStack>
                  </VStack>
                )}
              </VStack>
            </ModalBody>

            <ModalFooter>
              <StandardButton
                variant="solid"
                action="primary"
                onPress={() => setShowPathModal(false)}
                flex={1}
              >
                关闭
              </StandardButton>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}
    </Box>
  );
};

export default RelationshipGraphViewer;
