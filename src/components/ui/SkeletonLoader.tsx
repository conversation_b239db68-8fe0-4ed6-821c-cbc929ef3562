/**
 * 骨架屏加载组件
 * 提供各种预设的骨架屏样式
 */

import React, { useEffect, useRef } from 'react';
import { Animated, Easing } from 'react-native';
import {
  Box,
  VStack,
  HStack,
} from '@gluestack-ui/themed';

interface SkeletonProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number | string;
  animated?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 16,
  borderRadius = '$sm',
  animated = true,
}) => {
  const opacity = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    if (!animated) return;

    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 0.7,
          duration: 800,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0.3,
          duration: 800,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [animated, opacity]);

  return (
    <Animated.View style={{ opacity: animated ? opacity : 0.3 }}>
      <Box
        width={width}
        height={height}
        borderRadius={borderRadius}
        bg="$backgroundLight200"
        sx={{ _dark: { bg: '$backgroundDark700' } }}
      />
    </Animated.View>
  );
};

// 联系人卡片骨架屏
export const ContactCardSkeleton: React.FC<{ animated?: boolean }> = ({ animated = true }) => (
  <Box
    p="$4"
    bg="$backgroundLight0"
    borderRadius="$lg"
    borderWidth="$1"
    borderColor="$borderLight200"
    sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
  >
    <HStack space="md" alignItems="center">
      {/* 头像 */}
      <Skeleton width={48} height={48} borderRadius="$full" animated={animated} />
      
      {/* 信息 */}
      <VStack flex={1} space="sm">
        <Skeleton width="70%" height={16} animated={animated} />
        <Skeleton width="50%" height={12} animated={animated} />
        <Skeleton width="40%" height={12} animated={animated} />
      </VStack>
      
      {/* 操作按钮 */}
      <VStack space="xs">
        <Skeleton width={24} height={24} borderRadius="$sm" animated={animated} />
        <Skeleton width={24} height={24} borderRadius="$sm" animated={animated} />
      </VStack>
    </HStack>
  </Box>
);

// 联系人列表骨架屏
export const ContactListSkeleton: React.FC<{ count?: number; animated?: boolean }> = ({ 
  count = 5, 
  animated = true 
}) => (
  <VStack space="md" p="$4">
    {Array.from({ length: count }).map((_, index) => (
      <ContactCardSkeleton key={index} animated={animated} />
    ))}
  </VStack>
);

// 活动卡片骨架屏
export const EventCardSkeleton: React.FC<{ animated?: boolean }> = ({ animated = true }) => (
  <Box
    p="$4"
    bg="$backgroundLight0"
    borderRadius="$lg"
    borderWidth="$1"
    borderColor="$borderLight200"
    sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
  >
    <VStack space="md">
      {/* 标题和状态 */}
      <HStack justifyContent="space-between" alignItems="flex-start">
        <VStack flex={1} space="xs">
          <Skeleton width="80%" height={18} animated={animated} />
          <Skeleton width="60%" height={14} animated={animated} />
        </VStack>
        <Skeleton width={60} height={24} borderRadius="$full" animated={animated} />
      </HStack>
      
      {/* 详情信息 */}
      <VStack space="xs">
        <HStack space="sm" alignItems="center">
          <Skeleton width={16} height={16} animated={animated} />
          <Skeleton width="40%" height={12} animated={animated} />
        </HStack>
        <HStack space="sm" alignItems="center">
          <Skeleton width={16} height={16} animated={animated} />
          <Skeleton width="50%" height={12} animated={animated} />
        </HStack>
      </VStack>
    </VStack>
  </Box>
);

// 关系图谱骨架屏
export const RelationshipGraphSkeleton: React.FC<{ animated?: boolean }> = ({ animated = true }) => (
  <Box
    flex={1}
    justifyContent="center"
    alignItems="center"
    p="$8"
    bg="$backgroundLight50"
    sx={{ _dark: { bg: '$backgroundDark950' } }}
  >
    <VStack space="lg" alignItems="center">
      {/* 中心节点 */}
      <Skeleton width={80} height={80} borderRadius="$full" animated={animated} />
      
      {/* 周围节点 */}
      <HStack space="xl" justifyContent="center">
        <VStack space="md" alignItems="center">
          <Skeleton width={60} height={60} borderRadius="$full" animated={animated} />
          <Skeleton width={40} height={12} animated={animated} />
        </VStack>
        <VStack space="md" alignItems="center">
          <Skeleton width={60} height={60} borderRadius="$full" animated={animated} />
          <Skeleton width={50} height={12} animated={animated} />
        </VStack>
        <VStack space="md" alignItems="center">
          <Skeleton width={60} height={60} borderRadius="$full" animated={animated} />
          <Skeleton width={45} height={12} animated={animated} />
        </VStack>
      </HStack>
      
      {/* 连接线 */}
      <VStack space="xs" alignItems="center">
        <Skeleton width={200} height={2} animated={animated} />
        <Skeleton width={150} height={2} animated={animated} />
        <Skeleton width={180} height={2} animated={animated} />
      </VStack>
    </VStack>
  </Box>
);

// 搜索结果骨架屏
export const SearchResultSkeleton: React.FC<{ animated?: boolean }> = ({ animated = true }) => (
  <VStack space="md" p="$4">
    {/* 搜索统计 */}
    <HStack justifyContent="space-between" alignItems="center">
      <Skeleton width="40%" height={16} animated={animated} />
      <Skeleton width="20%" height={14} animated={animated} />
    </HStack>
    
    {/* 结果列表 */}
    {Array.from({ length: 3 }).map((_, index) => (
      <Box
        key={index}
        p="$3"
        bg="$backgroundLight0"
        borderRadius="$md"
        borderWidth="$1"
        borderColor="$borderLight200"
        sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
      >
        <HStack space="md" alignItems="center">
          <Skeleton width={40} height={40} borderRadius="$md" animated={animated} />
          <VStack flex={1} space="xs">
            <Skeleton width="70%" height={14} animated={animated} />
            <Skeleton width="50%" height={12} animated={animated} />
          </VStack>
          <Skeleton width={20} height={20} animated={animated} />
        </HStack>
      </Box>
    ))}
  </VStack>
);

// 统计卡片骨架屏
export const StatsCardSkeleton: React.FC<{ animated?: boolean }> = ({ animated = true }) => (
  <Box
    p="$4"
    bg="$backgroundLight0"
    borderRadius="$lg"
    borderWidth="$1"
    borderColor="$borderLight200"
    sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
  >
    <VStack space="md">
      <HStack justifyContent="space-between" alignItems="center">
        <Skeleton width="60%" height={16} animated={animated} />
        <Skeleton width={24} height={24} animated={animated} />
      </HStack>
      
      <Skeleton width="40%" height={32} animated={animated} />
      
      <HStack space="md">
        <VStack flex={1} space="xs">
          <Skeleton width="100%" height={12} animated={animated} />
          <Skeleton width="80%" height={10} animated={animated} />
        </VStack>
        <VStack flex={1} space="xs">
          <Skeleton width="100%" height={12} animated={animated} />
          <Skeleton width="70%" height={10} animated={animated} />
        </VStack>
      </HStack>
    </VStack>
  </Box>
);

// 推荐卡片骨架屏
export const RecommendationCardSkeleton: React.FC<{ animated?: boolean }> = ({ animated = true }) => (
  <Box
    p="$4"
    bg="$backgroundLight0"
    borderRadius="$lg"
    borderWidth="$1"
    borderColor="$borderLight200"
    sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
  >
    <VStack space="md">
      {/* 标题和类型 */}
      <HStack justifyContent="space-between" alignItems="flex-start">
        <VStack flex={1} space="xs">
          <Skeleton width="80%" height={16} animated={animated} />
          <Skeleton width="60%" height={12} animated={animated} />
        </VStack>
        <Skeleton width={50} height={20} borderRadius="$full" animated={animated} />
      </HStack>
      
      {/* 描述 */}
      <VStack space="xs">
        <Skeleton width="100%" height={12} animated={animated} />
        <Skeleton width="90%" height={12} animated={animated} />
        <Skeleton width="70%" height={12} animated={animated} />
      </VStack>
      
      {/* 操作按钮 */}
      <HStack space="md" justifyContent="flex-end">
        <Skeleton width={60} height={32} borderRadius="$md" animated={animated} />
        <Skeleton width={80} height={32} borderRadius="$md" animated={animated} />
      </HStack>
    </VStack>
  </Box>
);

// 导出所有组件
export { Skeleton };

// 为了避免Babel作用域跟踪问题，我们不使用默认导出
// 而是将Skeleton作为主要导出
