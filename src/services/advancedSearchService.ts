/**
 * 高级搜索和过滤服务
 * 提供强大的搜索、过滤和排序功能
 */

import { Contact, BusinessEvent, ContactRelationship, Communication } from '../types';

// 搜索类型
export type SearchEntityType = 'contact' | 'event' | 'relationship' | 'communication' | 'all';

// 搜索条件接口
export interface SearchCriteria {
  query?: string;
  entityType: SearchEntityType;
  filters: SearchFilters;
  sortBy: SortOption;
  sortOrder: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// 搜索过滤器
export interface SearchFilters {
  // 联系人过滤器
  contactFilters?: {
    industries?: string[];
    companies?: string[];
    priorities?: string[];
    tags?: string[];
    isFavorite?: boolean;
    hasAvatar?: boolean;
    createdDateRange?: DateRange;
    lastContactDateRange?: DateRange;
  };
  
  // 活动过滤器
  eventFilters?: {
    eventTypes?: string[];
    categories?: string[];
    scales?: string[];
    statuses?: string[];
    isVirtual?: boolean;
    dateRange?: DateRange;
    participantCount?: NumberRange;
    effectivenessScore?: NumberRange;
  };
  
  // 关系过滤器
  relationshipFilters?: {
    types?: string[];
    strengths?: NumberRange;
    contexts?: string[];
    introducedBy?: string[];
    dateRange?: DateRange;
  };
  
  // 沟通过滤器
  communicationFilters?: {
    types?: string[];
    directions?: string[];
    dateRange?: DateRange;
    hasAttachments?: boolean;
  };
}

// 排序选项
export interface SortOption {
  field: string;
  entityType: SearchEntityType;
}

// 日期范围
export interface DateRange {
  start: Date;
  end: Date;
}

// 数值范围
export interface NumberRange {
  min: number;
  max: number;
}

// 搜索结果
export interface SearchResult {
  contacts: Contact[];
  events: BusinessEvent[];
  relationships: ContactRelationship[];
  communications: Communication[];
  totalCount: number;
  hasMore: boolean;
}

// 搜索建议
export interface SearchSuggestion {
  type: 'query' | 'filter' | 'entity';
  value: string;
  label: string;
  count?: number;
}

/**
 * 高级搜索服务类
 */
export class AdvancedSearchService {
  private searchHistory: string[] = [];
  private popularFilters: Map<string, number> = new Map();

  /**
   * 执行搜索
   */
  async search(
    criteria: SearchCriteria,
    data: {
      contacts: Contact[];
      events: BusinessEvent[];
      relationships: ContactRelationship[];
      communications: Communication[];
    }
  ): Promise<SearchResult> {
    const { query, entityType, filters, sortBy, sortOrder, limit, offset } = criteria;

    // 记录搜索历史
    if (query && query.trim()) {
      this.addToSearchHistory(query.trim());
    }

    let results: SearchResult = {
      contacts: [],
      events: [],
      relationships: [],
      communications: [],
      totalCount: 0,
      hasMore: false
    };

    // 根据实体类型执行搜索
    if (entityType === 'all' || entityType === 'contact') {
      results.contacts = this.searchContacts(query, filters.contactFilters, data.contacts);
    }

    if (entityType === 'all' || entityType === 'event') {
      results.events = this.searchEvents(query, filters.eventFilters, data.events);
    }

    if (entityType === 'all' || entityType === 'relationship') {
      results.relationships = this.searchRelationships(query, filters.relationshipFilters, data.relationships);
    }

    if (entityType === 'all' || entityType === 'communication') {
      results.communications = this.searchCommunications(query, filters.communicationFilters, data.communications);
    }

    // 排序结果
    results = this.sortResults(results, sortBy, sortOrder);

    // 计算总数
    results.totalCount = results.contacts.length + results.events.length + 
                        results.relationships.length + results.communications.length;

    // 应用分页
    if (limit && offset !== undefined) {
      results = this.paginateResults(results, limit, offset);
    }

    return results;
  }

  /**
   * 获取搜索建议
   */
  async getSearchSuggestions(
    query: string,
    entityType: SearchEntityType,
    data: {
      contacts: Contact[];
      events: BusinessEvent[];
      relationships: ContactRelationship[];
      communications: Communication[];
    }
  ): Promise<SearchSuggestion[]> {
    const suggestions: SearchSuggestion[] = [];
    const lowerQuery = query.toLowerCase();

    // 查询建议（基于搜索历史）
    const queryMatches = this.searchHistory
      .filter(h => h.toLowerCase().includes(lowerQuery))
      .slice(0, 5)
      .map(h => ({
        type: 'query' as const,
        value: h,
        label: h
      }));
    suggestions.push(...queryMatches);

    // 实体建议
    if (entityType === 'all' || entityType === 'contact') {
      const contactSuggestions = this.getContactSuggestions(query, data.contacts);
      suggestions.push(...contactSuggestions);
    }

    if (entityType === 'all' || entityType === 'event') {
      const eventSuggestions = this.getEventSuggestions(query, data.events);
      suggestions.push(...eventSuggestions);
    }

    // 过滤器建议
    const filterSuggestions = this.getFilterSuggestions(query, entityType, data);
    suggestions.push(...filterSuggestions);

    return suggestions.slice(0, 10); // 限制建议数量
  }

  /**
   * 获取热门过滤器
   */
  getPopularFilters(): Array<{ filter: string; count: number }> {
    return Array.from(this.popularFilters.entries())
      .map(([filter, count]) => ({ filter, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * 清除搜索历史
   */
  clearSearchHistory(): void {
    this.searchHistory = [];
  }

  /**
   * 搜索联系人
   */
  private searchContacts(query?: string, filters?: SearchFilters['contactFilters'], contacts: Contact[] = []): Contact[] {
    let results = [...contacts];

    // 文本搜索
    if (query && query.trim()) {
      const lowerQuery = query.toLowerCase();
      results = results.filter(contact => 
        contact.name.toLowerCase().includes(lowerQuery) ||
        contact.company?.toLowerCase().includes(lowerQuery) ||
        contact.title?.toLowerCase().includes(lowerQuery) ||
        contact.email?.toLowerCase().includes(lowerQuery) ||
        contact.phone?.toLowerCase().includes(lowerQuery) ||
        contact.industry?.toLowerCase().includes(lowerQuery) ||
        contact.notes?.toLowerCase().includes(lowerQuery) ||
        contact.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
      );
    }

    // 应用过滤器
    if (filters) {
      if (filters.industries && filters.industries.length > 0) {
        results = results.filter(contact => 
          contact.industry && filters.industries!.includes(contact.industry)
        );
      }

      if (filters.companies && filters.companies.length > 0) {
        results = results.filter(contact => 
          contact.company && filters.companies!.includes(contact.company)
        );
      }

      if (filters.priorities && filters.priorities.length > 0) {
        results = results.filter(contact => 
          contact.priority && filters.priorities!.includes(contact.priority)
        );
      }

      if (filters.tags && filters.tags.length > 0) {
        results = results.filter(contact => 
          contact.tags && contact.tags.some(tag => filters.tags!.includes(tag))
        );
      }

      if (filters.isFavorite !== undefined) {
        results = results.filter(contact => contact.isFavorite === filters.isFavorite);
      }

      if (filters.hasAvatar !== undefined) {
        results = results.filter(contact => !!contact.avatar === filters.hasAvatar);
      }

      if (filters.createdDateRange) {
        results = results.filter(contact => {
          const createdDate = new Date(contact.createdAt);
          return createdDate >= filters.createdDateRange!.start && 
                 createdDate <= filters.createdDateRange!.end;
        });
      }

      if (filters.lastContactDateRange) {
        results = results.filter(contact => {
          const lastContactDate = new Date(contact.updatedAt);
          return lastContactDate >= filters.lastContactDateRange!.start && 
                 lastContactDate <= filters.lastContactDateRange!.end;
        });
      }
    }

    return results;
  }

  /**
   * 搜索活动
   */
  private searchEvents(query?: string, filters?: SearchFilters['eventFilters'], events: BusinessEvent[] = []): BusinessEvent[] {
    let results = [...events];

    // 文本搜索
    if (query && query.trim()) {
      const lowerQuery = query.toLowerCase();
      results = results.filter(event => 
        event.title.toLowerCase().includes(lowerQuery) ||
        event.description?.toLowerCase().includes(lowerQuery) ||
        event.location?.toLowerCase().includes(lowerQuery) ||
        event.category.toLowerCase().includes(lowerQuery) ||
        event.notes?.toLowerCase().includes(lowerQuery) ||
        event.agenda?.toLowerCase().includes(lowerQuery) ||
        event.tags?.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
        event.participants.some(p => p.name.toLowerCase().includes(lowerQuery))
      );
    }

    // 应用过滤器
    if (filters) {
      if (filters.eventTypes && filters.eventTypes.length > 0) {
        results = results.filter(event => filters.eventTypes!.includes(event.eventType));
      }

      if (filters.categories && filters.categories.length > 0) {
        results = results.filter(event => filters.categories!.includes(event.category));
      }

      if (filters.scales && filters.scales.length > 0) {
        results = results.filter(event => filters.scales!.includes(event.scale));
      }

      if (filters.statuses && filters.statuses.length > 0) {
        results = results.filter(event => filters.statuses!.includes(event.status));
      }

      if (filters.isVirtual !== undefined) {
        results = results.filter(event => event.isVirtual === filters.isVirtual);
      }

      if (filters.dateRange) {
        results = results.filter(event => {
          const eventDate = new Date(event.date);
          return eventDate >= filters.dateRange!.start && 
                 eventDate <= filters.dateRange!.end;
        });
      }

      if (filters.participantCount) {
        results = results.filter(event => {
          const count = event.participants.length;
          return count >= filters.participantCount!.min && 
                 count <= filters.participantCount!.max;
        });
      }

      if (filters.effectivenessScore) {
        results = results.filter(event => {
          const score = event.effectivenessScore || 0;
          return score >= filters.effectivenessScore!.min && 
                 score <= filters.effectivenessScore!.max;
        });
      }
    }

    return results;
  }

  /**
   * 搜索关系
   */
  private searchRelationships(query?: string, filters?: SearchFilters['relationshipFilters'], relationships: ContactRelationship[] = []): ContactRelationship[] {
    let results = [...relationships];

    // 文本搜索
    if (query && query.trim()) {
      const lowerQuery = query.toLowerCase();
      results = results.filter(rel => 
        rel.type.toLowerCase().includes(lowerQuery) ||
        rel.context?.toLowerCase().includes(lowerQuery) ||
        rel.notes?.toLowerCase().includes(lowerQuery)
      );
    }

    // 应用过滤器
    if (filters) {
      if (filters.types && filters.types.length > 0) {
        results = results.filter(rel => filters.types!.includes(rel.type));
      }

      if (filters.strengths) {
        results = results.filter(rel => {
          const strength = rel.strength;
          return strength >= filters.strengths!.min && 
                 strength <= filters.strengths!.max;
        });
      }

      if (filters.contexts && filters.contexts.length > 0) {
        results = results.filter(rel => 
          rel.context && filters.contexts!.includes(rel.context)
        );
      }

      if (filters.introducedBy && filters.introducedBy.length > 0) {
        results = results.filter(rel => 
          rel.introducedBy && filters.introducedBy!.includes(rel.introducedBy)
        );
      }

      if (filters.dateRange) {
        results = results.filter(rel => {
          const createdDate = new Date(rel.createdAt);
          return createdDate >= filters.dateRange!.start && 
                 createdDate <= filters.dateRange!.end;
        });
      }
    }

    return results;
  }

  /**
   * 搜索沟通记录
   */
  private searchCommunications(query?: string, filters?: SearchFilters['communicationFilters'], communications: Communication[] = []): Communication[] {
    let results = [...communications];

    // 文本搜索
    if (query && query.trim()) {
      const lowerQuery = query.toLowerCase();
      results = results.filter(comm => 
        comm.content.toLowerCase().includes(lowerQuery) ||
        comm.summary?.toLowerCase().includes(lowerQuery) ||
        comm.notes?.toLowerCase().includes(lowerQuery)
      );
    }

    // 应用过滤器
    if (filters) {
      if (filters.types && filters.types.length > 0) {
        results = results.filter(comm => filters.types!.includes(comm.type));
      }

      if (filters.directions && filters.directions.length > 0) {
        results = results.filter(comm => filters.directions!.includes(comm.direction));
      }

      if (filters.dateRange) {
        results = results.filter(comm => {
          const commDate = new Date(comm.date);
          return commDate >= filters.dateRange!.start && 
                 commDate <= filters.dateRange!.end;
        });
      }

      if (filters.hasAttachments !== undefined) {
        results = results.filter(comm => 
          (comm.attachments && comm.attachments.length > 0) === filters.hasAttachments
        );
      }
    }

    return results;
  }

  /**
   * 排序结果
   */
  private sortResults(results: SearchResult, sortBy: SortOption, sortOrder: 'asc' | 'desc'): SearchResult {
    const multiplier = sortOrder === 'asc' ? 1 : -1;

    // 排序联系人
    if (results.contacts.length > 0) {
      results.contacts.sort((a, b) => {
        let comparison = 0;
        switch (sortBy.field) {
          case 'name':
            comparison = a.name.localeCompare(b.name);
            break;
          case 'company':
            comparison = (a.company || '').localeCompare(b.company || '');
            break;
          case 'createdAt':
            comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
            break;
          case 'updatedAt':
            comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime();
            break;
          case 'priority':
            const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
            comparison = (priorityOrder[a.priority as keyof typeof priorityOrder] || 0) - 
                        (priorityOrder[b.priority as keyof typeof priorityOrder] || 0);
            break;
          default:
            comparison = 0;
        }
        return comparison * multiplier;
      });
    }

    // 排序活动
    if (results.events.length > 0) {
      results.events.sort((a, b) => {
        let comparison = 0;
        switch (sortBy.field) {
          case 'title':
            comparison = a.title.localeCompare(b.title);
            break;
          case 'date':
            comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
            break;
          case 'createdAt':
            comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
            break;
          case 'effectivenessScore':
            comparison = (a.effectivenessScore || 0) - (b.effectivenessScore || 0);
            break;
          case 'participantCount':
            comparison = a.participants.length - b.participants.length;
            break;
          default:
            comparison = 0;
        }
        return comparison * multiplier;
      });
    }

    return results;
  }

  /**
   * 分页结果
   */
  private paginateResults(results: SearchResult, limit: number, offset: number): SearchResult {
    const totalItems = results.totalCount;
    const hasMore = offset + limit < totalItems;

    // 简化分页逻辑：只对联系人进行分页
    if (results.contacts.length > 0) {
      results.contacts = results.contacts.slice(offset, offset + limit);
    }

    results.hasMore = hasMore;
    return results;
  }

  /**
   * 添加到搜索历史
   */
  private addToSearchHistory(query: string): void {
    // 移除重复项
    this.searchHistory = this.searchHistory.filter(h => h !== query);
    // 添加到开头
    this.searchHistory.unshift(query);
    // 限制历史记录数量
    this.searchHistory = this.searchHistory.slice(0, 20);
  }

  /**
   * 获取联系人建议
   */
  private getContactSuggestions(query: string, contacts: Contact[]): SearchSuggestion[] {
    const lowerQuery = query.toLowerCase();
    const suggestions: SearchSuggestion[] = [];

    // 名称匹配
    const nameMatches = contacts
      .filter(c => c.name.toLowerCase().includes(lowerQuery))
      .slice(0, 3)
      .map(c => ({
        type: 'entity' as const,
        value: c.name,
        label: `${c.name} - ${c.company || '联系人'}`
      }));
    suggestions.push(...nameMatches);

    // 公司匹配
    const companyMatches = contacts
      .filter(c => c.company && c.company.toLowerCase().includes(lowerQuery))
      .slice(0, 2)
      .map(c => ({
        type: 'entity' as const,
        value: c.company!,
        label: `${c.company} - 公司`
      }));
    suggestions.push(...companyMatches);

    return suggestions;
  }

  /**
   * 获取活动建议
   */
  private getEventSuggestions(query: string, events: BusinessEvent[]): SearchSuggestion[] {
    const lowerQuery = query.toLowerCase();
    const suggestions: SearchSuggestion[] = [];

    // 标题匹配
    const titleMatches = events
      .filter(e => e.title.toLowerCase().includes(lowerQuery))
      .slice(0, 3)
      .map(e => ({
        type: 'entity' as const,
        value: e.title,
        label: `${e.title} - 活动`
      }));
    suggestions.push(...titleMatches);

    return suggestions;
  }

  /**
   * 获取过滤器建议
   */
  private getFilterSuggestions(query: string, entityType: SearchEntityType, data: any): SearchSuggestion[] {
    const suggestions: SearchSuggestion[] = [];
    const lowerQuery = query.toLowerCase();

    // 行业过滤器建议
    if (entityType === 'all' || entityType === 'contact') {
      const industries = Array.from(new Set(
        data.contacts
          .map((c: Contact) => c.industry)
          .filter((industry: string) => industry && industry.toLowerCase().includes(lowerQuery))
      )).slice(0, 3);

      industries.forEach((industry: string) => {
        suggestions.push({
          type: 'filter',
          value: `industry:${industry}`,
          label: `行业: ${industry}`
        });
      });
    }

    return suggestions;
  }
}

// 导出默认实例
export const advancedSearchService = new AdvancedSearchService();
