import React from 'react';
import {
  VStack,
  Text
} from '@gluestack-ui/themed';
import { Platform } from 'react-native';

interface VersionInfoProps {
  version: string;
  copyright: string;
}

/**
 * 版本信息组件
 * 显示应用版本和版权信息
 */
const VersionInfo: React.FC<VersionInfoProps> = ({
  version,
  copyright
}) => {
  return (
    <VStack
      alignItems="center"
      p="$5"
      pb={Platform.OS === 'ios' ? '$10' : '$5'}
      space="xs"
    >
      <Text 
        fontSize="$sm" 
        color="$textLight500"
        sx={{ _dark: { color: '$textDark400' } }}
      >
        {version}
      </Text>
      
      <Text 
        fontSize="$xs" 
        color="$textLight500"
        sx={{ _dark: { color: '$textDark400' } }}
      >
        {copyright}
      </Text>
    </VStack>
  );
};

export default VersionInfo;
