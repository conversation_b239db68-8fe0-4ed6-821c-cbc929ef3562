/**
 * 关系强度趋势图表组件
 * 显示关系强度随时间的变化趋势
 */

import React, { useMemo } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Heading,
} from '@gluestack-ui/themed';
import { Dimensions } from 'react-native';

import { CommunicationRecord } from '../../types';

interface RelationshipTrendChartProps {
  communications: CommunicationRecord[];
  contactName: string;
  timeRange?: 'week' | 'month' | 'quarter' | 'year';
  height?: number;
}

interface DataPoint {
  date: Date;
  value: number;
  label: string;
}

export const RelationshipTrendChart: React.FC<RelationshipTrendChartProps> = ({
  communications,
  contactName,
  timeRange = 'quarter',
  height = 200,
}) => {
  const { width } = Dimensions.get('window');
  const chartWidth = width - 64; // 减去padding

  // 计算趋势数据点
  const trendData = useMemo((): DataPoint[] => {
    const now = new Date();
    const timeRanges = {
      week: 7,
      month: 30,
      quarter: 90,
      year: 365,
    };
    
    const days = timeRanges[timeRange];
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
    
    // 按时间段分组通信记录
    const intervals = timeRange === 'week' ? 7 : timeRange === 'month' ? 6 : timeRange === 'quarter' ? 12 : 12;
    const intervalDays = Math.floor(days / intervals);
    
    const dataPoints: DataPoint[] = [];
    
    for (let i = 0; i < intervals; i++) {
      const intervalStart = new Date(startDate.getTime() + i * intervalDays * 24 * 60 * 60 * 1000);
      const intervalEnd = new Date(intervalStart.getTime() + intervalDays * 24 * 60 * 60 * 1000);
      
      // 获取该时间段内的通信记录
      const intervalComms = communications.filter(c => 
        c.startTime >= intervalStart && c.startTime < intervalEnd
      );
      
      // 计算该时间段的关系强度
      let strengthValue = 0;
      if (intervalComms.length > 0) {
        // 基于通信频率、质量和类型计算强度
        const frequency = intervalComms.length;
        const avgEffectiveness = intervalComms
          .filter(c => c.effectiveness !== undefined)
          .reduce((sum, c) => sum + (c.effectiveness || 0), 0) / Math.max(intervalComms.length, 1);
        
        // 不同类型的通信有不同权重
        const typeWeights = {
          meeting: 3,
          video_call: 2.5,
          call: 2,
          email: 1.5,
          message: 1,
          other: 1,
        };
        
        const weightedFrequency = intervalComms.reduce((sum, c) => 
          sum + (typeWeights[c.type] || 1), 0
        );
        
        // 计算强度值 (0-100)
        strengthValue = Math.min(100, (weightedFrequency * 10) + (avgEffectiveness * 5));
      }
      
      dataPoints.push({
        date: intervalStart,
        value: strengthValue,
        label: formatDateLabel(intervalStart, timeRange),
      });
    }
    
    return dataPoints;
  }, [communications, timeRange]);

  // 格式化日期标签
  const formatDateLabel = (date: Date, range: string): string => {
    switch (range) {
      case 'week':
        return date.toLocaleDateString('zh-CN', { weekday: 'short' });
      case 'month':
        return date.toLocaleDateString('zh-CN', { day: 'numeric' });
      case 'quarter':
        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
      case 'year':
        return date.toLocaleDateString('zh-CN', { month: 'short' });
      default:
        return date.toLocaleDateString('zh-CN');
    }
  };

  // 计算图表路径
  const chartPath = useMemo(() => {
    if (trendData.length < 2) return '';
    
    const maxValue = Math.max(...trendData.map(d => d.value), 1);
    const xStep = chartWidth / (trendData.length - 1);
    
    let path = '';
    
    trendData.forEach((point, index) => {
      const x = index * xStep;
      const y = height - (point.value / maxValue) * height;
      
      if (index === 0) {
        path += `M ${x} ${y}`;
      } else {
        path += ` L ${x} ${y}`;
      }
    });
    
    return path;
  }, [trendData, chartWidth, height]);

  // 计算趋势方向
  const trendDirection = useMemo(() => {
    if (trendData.length < 2) return 'stable';
    
    const firstHalf = trendData.slice(0, Math.floor(trendData.length / 2));
    const secondHalf = trendData.slice(Math.floor(trendData.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, d) => sum + d.value, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, d) => sum + d.value, 0) / secondHalf.length;
    
    const change = ((secondAvg - firstAvg) / Math.max(firstAvg, 1)) * 100;
    
    if (change > 10) return 'improving';
    if (change < -10) return 'declining';
    return 'stable';
  }, [trendData]);

  // 获取趋势颜色
  const getTrendColor = () => {
    switch (trendDirection) {
      case 'improving': return '$success500';
      case 'declining': return '$error500';
      case 'stable': return '$primary500';
      default: return '$textLight500';
    }
  };

  // 获取趋势描述
  const getTrendDescription = () => {
    switch (trendDirection) {
      case 'improving': return '关系强度呈上升趋势';
      case 'declining': return '关系强度呈下降趋势';
      case 'stable': return '关系强度保持稳定';
      default: return '趋势不明确';
    }
  };

  return (
    <Box p="$4" bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark900' } }}>
      <VStack space="md">
        {/* 标题 */}
        <VStack space="xs">
          <Heading size="sm">
            与{contactName}的关系趋势
          </Heading>
          <Text size="sm" color={getTrendColor()} fontWeight="$medium">
            {getTrendDescription()}
          </Text>
        </VStack>

        {/* 图表容器 */}
        <Box position="relative" height={height + 40}>
          {trendData.length >= 2 ? (
            <>
              {/* SVG图表 */}
              <Box position="absolute" top={0} left={0} right={0}>
                {/* 这里应该使用SVG库来渲染图表，但为了简化，我们使用简单的视觉表示 */}
                <VStack space="xs">
                  {/* 数据点 */}
                  <HStack space="xs" justifyContent="space-between" alignItems="flex-end" height={height}>
                    {trendData.map((point, index) => {
                      const maxValue = Math.max(...trendData.map(d => d.value), 1);
                      const barHeight = (point.value / maxValue) * (height - 20);
                      
                      return (
                        <VStack key={index} alignItems="center" flex={1}>
                          <Box
                            width="$6"
                            height={barHeight}
                            bg={getTrendColor()}
                            borderRadius="$sm"
                            opacity={0.8}
                          />
                          <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }} mt="$1">
                            {Math.round(point.value)}
                          </Text>
                        </VStack>
                      );
                    })}
                  </HStack>
                  
                  {/* X轴标签 */}
                  <HStack justifyContent="space-between" mt="$2">
                    {trendData.map((point, index) => (
                      <Text 
                        key={index} 
                        size="xs" 
                        color="$textLight400" 
                        sx={{ _dark: { color: '$textDark600' } }}
                        flex={1}
                        textAlign="center"
                      >
                        {point.label}
                      </Text>
                    ))}
                  </HStack>
                </VStack>
              </Box>
            </>
          ) : (
            <Box 
              flex={1} 
              justifyContent="center" 
              alignItems="center"
              bg="$backgroundLight100"
              sx={{ _dark: { bg: '$backgroundDark800' } }}
              borderRadius="$md"
            >
              <VStack space="sm" alignItems="center">
                <Text color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                  暂无足够数据
                </Text>
                <Text size="sm" color="$textLight400" sx={{ _dark: { color: '$textDark600' } }} textAlign="center">
                  需要更多沟通记录来生成趋势图
                </Text>
              </VStack>
            </Box>
          )}
        </Box>

        {/* 统计信息 */}
        {trendData.length >= 2 && (
          <HStack space="lg" justifyContent="space-around">
            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$primary600">
                {Math.round(trendData[trendData.length - 1]?.value || 0)}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                当前强度
              </Text>
            </VStack>
            
            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$success600">
                {Math.round(Math.max(...trendData.map(d => d.value)))}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                最高强度
              </Text>
            </VStack>
            
            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$warning600">
                {Math.round(trendData.reduce((sum, d) => sum + d.value, 0) / trendData.length)}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                平均强度
              </Text>
            </VStack>
          </HStack>
        )}
      </VStack>
    </Box>
  );
};

export default RelationshipTrendChart;
