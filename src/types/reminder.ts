/**
 * 提醒系统类型定义
 */

export type ReminderType = 
  | 'birthday'           // 生日提醒
  | 'follow_up'          // 定期联系提醒
  | 'meeting'            // 会议提醒
  | 'anniversary'        // 纪念日提醒
  | 'task_deadline'      // 任务截止提醒
  | 'relationship_maintenance' // 关系维护提醒
  | 'custom';            // 自定义提醒

export type ReminderStatus = 
  | 'pending'    // 待触发
  | 'triggered'  // 已触发
  | 'completed'  // 已完成
  | 'dismissed'  // 已忽略
  | 'snoozed';   // 已延期

export type ReminderPriority = 'low' | 'medium' | 'high' | 'urgent';

export type ReminderFrequency = 
  | 'once'       // 一次性
  | 'daily'      // 每日
  | 'weekly'     // 每周
  | 'monthly'    // 每月
  | 'quarterly'  // 每季度
  | 'yearly'     // 每年
  | 'custom';    // 自定义间隔

export interface ReminderAction {
  id: string;
  type: 'call' | 'email' | 'message' | 'meeting' | 'note' | 'custom';
  label: string;
  data?: Record<string, any>; // 额外数据，如电话号码、邮箱等
}

export interface ReminderRule {
  id: string;
  name: string;
  description?: string;
  type: ReminderType;
  frequency: ReminderFrequency;
  customInterval?: number; // 自定义间隔天数
  priority: ReminderPriority;
  isActive: boolean;
  
  // 触发条件
  conditions: {
    contactTags?: string[];           // 联系人标签条件
    relationshipTypes?: string[];     // 关系类型条件
    lastContactDays?: number;         // 最后联系天数
    importanceLevel?: number;         // 重要程度（1-5）
    excludeWeekends?: boolean;        // 排除周末
    excludeHolidays?: boolean;        // 排除节假日
  };
  
  // 提醒设置
  settings: {
    advanceNotice?: number;           // 提前通知天数
    reminderTime?: string;            // 提醒时间 (HH:mm)
    enablePushNotification?: boolean; // 启用推送通知
    enableEmailNotification?: boolean; // 启用邮件通知
    autoSnoozeMinutes?: number;       // 自动延期分钟数
  };
  
  createdAt: Date;
  updatedAt: Date;
}

export interface Reminder {
  id: string;
  ruleId?: string;              // 关联的提醒规则ID
  type: ReminderType;
  title: string;
  description?: string;
  
  // 关联信息
  contactId?: string;           // 关联的联系人ID
  meetingId?: string;           // 关联的会议ID
  taskId?: string;              // 关联的任务ID
  
  // 时间信息
  triggerDate: Date;            // 触发日期
  originalDate?: Date;          // 原始日期（用于延期）
  completedAt?: Date;           // 完成时间
  dismissedAt?: Date;           // 忽略时间
  snoozeUntil?: Date;           // 延期到
  
  // 状态和优先级
  status: ReminderStatus;
  priority: ReminderPriority;
  
  // 操作建议
  suggestedActions?: ReminderAction[];
  
  // 元数据
  metadata?: {
    autoGenerated?: boolean;    // 是否自动生成
    lastContactDate?: Date;     // 最后联系日期
    contactFrequency?: number;  // 联系频率（天）
    relationshipStrength?: number; // 关系强度
    [key: string]: any;
  };
  
  createdAt: Date;
  updatedAt: Date;
}

export interface ReminderStats {
  total: number;
  pending: number;
  triggered: number;
  completed: number;
  dismissed: number;
  overdue: number;
  
  byType: Record<ReminderType, number>;
  byPriority: Record<ReminderPriority, number>;
  
  upcomingWeek: number;
  upcomingMonth: number;
}

export interface ReminderNotification {
  id: string;
  reminderId: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  scheduledFor: Date;
  sentAt?: Date;
  isRead: boolean;
  actions?: {
    label: string;
    action: string;
    data?: Record<string, any>;
  }[];
}

// 预设的提醒规则模板
export interface ReminderTemplate {
  id: string;
  name: string;
  description: string;
  type: ReminderType;
  defaultSettings: Partial<ReminderRule>;
  isBuiltIn: boolean;
}

// 提醒系统配置
export interface ReminderConfig {
  isEnabled: boolean;
  defaultReminderTime: string;        // 默认提醒时间
  enablePushNotifications: boolean;
  enableEmailNotifications: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string;              // 勿扰开始时间
    endTime: string;                // 勿扰结束时间
  };
  weekendMode: 'normal' | 'quiet' | 'disabled';
  maxDailyReminders: number;
  autoSnoozeEnabled: boolean;
  autoSnoozeMinutes: number;
}

// 智能提醒建议
export interface SmartReminderSuggestion {
  id: string;
  contactId: string;
  type: ReminderType;
  reason: string;                     // 建议原因
  confidence: number;                 // 置信度 0-1
  suggestedDate: Date;
  suggestedFrequency?: ReminderFrequency;
  metadata: {
    lastContactDays: number;
    relationshipStrength: number;
    contactImportance: number;
    analysisData: Record<string, any>;
  };
  isAccepted?: boolean;
  createdAt: Date;
}
