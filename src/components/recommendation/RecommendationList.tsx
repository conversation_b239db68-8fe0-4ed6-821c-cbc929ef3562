/**
 * 推荐列表组件
 * 显示推荐项目列表，支持过滤和排序
 */

import React, { useState } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  FlatList,
  Pressable,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  Badge,
  BadgeText,
  Spinner,
} from '@gluestack-ui/themed';
import {
  Search,
  Filter,
  ChevronDown,
  RefreshCw,
  Settings,
  TrendingUp,
} from 'lucide-react-native';

import { StandardIcon, StandardButton } from '../ui';
import { RecommendationCard } from './RecommendationCard';
import { RecommendationItem, RecommendationType } from '../../services/aiRecommendationService';
import { useRecommendationStore, useRecommendationSelectors } from '../../store/recommendationStore';

interface RecommendationListProps {
  onRecommendationAction?: (recommendationId: string, action: string) => void;
  onRefresh?: () => void;
  compact?: boolean;
  maxItems?: number;
  showFilters?: boolean;
  showHeader?: boolean;
}

export const RecommendationList: React.FC<RecommendationListProps> = ({
  onRecommendationAction,
  onRefresh,
  compact = false,
  maxItems,
  showFilters = true,
  showHeader = true
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilterOptions, setShowFilterOptions] = useState(false);

  // Store hooks
  const {
    dismissRecommendation,
    provideFeedback,
    setTypeFilter,
    setPriorityFilter,
    clearFilters,
    markRecommendationAsActioned
  } = useRecommendationStore();

  const activeRecommendations = useRecommendationSelectors.useActiveRecommendations();
  const isLoading = useRecommendationSelectors.useIsLoading();
  const error = useRecommendationSelectors.useError();
  const filters = useRecommendationSelectors.useFilters();
  const stats = useRecommendationSelectors.useRecommendationStats();

  // Filter recommendations by search query
  const filteredRecommendations = React.useMemo(() => {
    let filtered = activeRecommendations;

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(rec =>
        rec.title.toLowerCase().includes(query) ||
        rec.description.toLowerCase().includes(query)
      );
    }

    // Apply maxItems limit
    if (maxItems && maxItems > 0) {
      filtered = filtered.slice(0, maxItems);
    }

    return filtered;
  }, [activeRecommendations, searchQuery, maxItems]);

  const handleRecommendationAction = (recommendationId: string, action: string) => {
    if (action === 'primary' || action === 'later') {
      markRecommendationAsActioned(recommendationId);
    }
    
    if (onRecommendationAction) {
      onRecommendationAction(recommendationId, action);
    }
  };

  const handleDismiss = (recommendationId: string) => {
    dismissRecommendation(recommendationId);
  };

  const handleFeedback = (recommendationId: string, feedback: 'positive' | 'negative' | 'neutral') => {
    provideFeedback(recommendationId, feedback);
  };

  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  const typeOptions = [
    { value: 'all', label: '全部类型' },
    { value: 'contact_connection', label: '联系人推荐' },
    { value: 'event_suggestion', label: '活动推荐' },
    { value: 'relationship_maintenance', label: '关系维护' },
    { value: 'business_opportunity', label: '商务机会' },
    { value: 'network_expansion', label: '网络扩展' },
    { value: 'follow_up_reminder', label: '跟进提醒' },
  ];

  const priorityOptions = [
    { value: 'all', label: '全部优先级' },
    { value: 'urgent', label: '紧急' },
    { value: 'high', label: '高' },
    { value: 'medium', label: '中' },
    { value: 'low', label: '低' },
  ];

  const renderRecommendationItem = ({ item }: { item: RecommendationItem }) => (
    <RecommendationCard
      recommendation={item}
      onAction={handleRecommendationAction}
      onDismiss={handleDismiss}
      onFeedback={handleFeedback}
      compact={compact}
    />
  );

  const renderEmptyState = () => (
    <Box flex={1} justifyContent="center" alignItems="center" p="$8">
      <VStack space="md" alignItems="center">
        <StandardIcon as={TrendingUp} size="4xl" color="$textLight400" />
        <Text textAlign="center" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
          {searchQuery ? '没有找到匹配的推荐' : '暂无推荐'}
        </Text>
        <Text textAlign="center" color="$textLight400" sx={{ _dark: { color: '$textDark600' } }}>
          {searchQuery ? '尝试调整搜索条件' : '系统会根据您的活动自动生成推荐'}
        </Text>
        {!searchQuery && (
          <StandardButton
            variant="outline"
            onPress={handleRefresh}
            mt="$4"
            leftIcon={RefreshCw}
          >
            刷新推荐
          </StandardButton>
        )}
      </VStack>
    </Box>
  );

  const renderHeader = () => (
    <VStack space="md" mb="$4">
      {/* Title and Stats */}
      <HStack justifyContent="space-between" alignItems="center">
        <VStack>
          <Text fontWeight="$bold" size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
            智能推荐
          </Text>
          <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
            {stats.total} 条推荐，{filteredRecommendations.length} 条活跃
          </Text>
        </VStack>
        
        <HStack space="sm">
          <Pressable onPress={handleRefresh} p="$2">
            <StandardIcon 
              as={RefreshCw} 
              size="md" 
              color="$textLight600"
              sx={{
                transform: isLoading ? [{ rotate: '360deg' }] : []
              }}
            />
          </Pressable>
          
          {showFilters && (
            <Pressable 
              onPress={() => setShowFilterOptions(!showFilterOptions)} 
              p="$2"
            >
              <StandardIcon as={Filter} size="md" color="$textLight600" />
            </Pressable>
          )}
        </HStack>
      </HStack>

      {/* Priority Badges */}
      <HStack space="sm" flexWrap="wrap">
        {Object.entries(stats.byPriority).map(([priority, count]) => (
          <Badge key={priority} size="sm" variant="outline" action="muted">
            <BadgeText>
              {priority === 'urgent' ? '紧急' : 
               priority === 'high' ? '高' :
               priority === 'medium' ? '中' : '低'}: {count}
            </BadgeText>
          </Badge>
        ))}
      </HStack>

      {/* Search */}
      <Input variant="outline" size="md">
        <InputSlot pl="$3">
          <InputIcon as={Search} />
        </InputSlot>
        <InputField
          placeholder="搜索推荐..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </Input>

      {/* Filter Options */}
      {showFilters && showFilterOptions && (
        <VStack space="sm" p="$4" bg="$backgroundLight100" borderRadius="$md" sx={{ _dark: { bg: '$backgroundDark800' } }}>
          <HStack space="md">
            <VStack flex={1} space="xs">
              <Text size="sm" fontWeight="$medium" color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                类型
              </Text>
              <Select
                selectedValue={filters.typeFilter}
                onValueChange={(value) => setTypeFilter(value as RecommendationType | 'all')}
              >
                <SelectTrigger variant="outline" size="sm">
                  <SelectInput placeholder="选择类型" />
                  <SelectIcon as={ChevronDown} />
                </SelectTrigger>
                <SelectPortal>
                  <SelectBackdrop />
                  <SelectContent>
                    <SelectDragIndicatorWrapper>
                      <SelectDragIndicator />
                    </SelectDragIndicatorWrapper>
                    {typeOptions.map((option) => (
                      <SelectItem key={option.value} label={option.label} value={option.value} />
                    ))}
                  </SelectContent>
                </SelectPortal>
              </Select>
            </VStack>

            <VStack flex={1} space="xs">
              <Text size="sm" fontWeight="$medium" color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                优先级
              </Text>
              <Select
                selectedValue={filters.priorityFilter}
                onValueChange={(value) => setPriorityFilter(value as any)}
              >
                <SelectTrigger variant="outline" size="sm">
                  <SelectInput placeholder="选择优先级" />
                  <SelectIcon as={ChevronDown} />
                </SelectTrigger>
                <SelectPortal>
                  <SelectBackdrop />
                  <SelectContent>
                    <SelectDragIndicatorWrapper>
                      <SelectDragIndicator />
                    </SelectDragIndicatorWrapper>
                    {priorityOptions.map((option) => (
                      <SelectItem key={option.value} label={option.label} value={option.value} />
                    ))}
                  </SelectContent>
                </SelectPortal>
              </Select>
            </VStack>
          </HStack>

          <HStack space="sm" justifyContent="flex-end">
            <StandardButton
              size="sm"
              variant="outline"
              onPress={clearFilters}
            >
              清除过滤
            </StandardButton>
          </HStack>
        </VStack>
      )}
    </VStack>
  );

  if (error) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" p="$8">
        <VStack space="md" alignItems="center">
          <Text color="$error600" textAlign="center">
            {error}
          </Text>
          <StandardButton
            variant="outline"
            onPress={handleRefresh}
            leftIcon={RefreshCw}
          >
            重试
          </StandardButton>
        </VStack>
      </Box>
    );
  }

  return (
    <Box flex={1}>
      {showHeader && renderHeader()}
      
      {isLoading && filteredRecommendations.length === 0 ? (
        <Box flex={1} justifyContent="center" alignItems="center">
          <VStack space="md" alignItems="center">
            <Spinner size="large" color="$primary500" />
            <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
              正在生成推荐...
            </Text>
          </VStack>
        </Box>
      ) : (
        <FlatList
          data={filteredRecommendations}
          keyExtractor={(item) => item.id}
          renderItem={renderRecommendationItem}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            flexGrow: 1,
            paddingBottom: 20
          }}
        />
      )}
    </Box>
  );
};

export default RecommendationList;
