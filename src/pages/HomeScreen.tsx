import React, { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Box,
  Text,
  Heading,
  VStack,
  HStack,
  Button,
  ButtonText,
  ButtonIcon,
  Pressable,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  Image,
  ScrollView,
  Link,
  LinkText,
  Icon,
  StatusBar
} from '@gluestack-ui/themed';
import { Bell, Plus, Users, Calendar, Map, Settings, User, Briefcase, Clock, ChevronRight } from 'lucide-react-native';
import { Platform } from 'react-native';
import { RootStackParamList } from '@/types/navigation';

const mockStats = [
  { label: '总联系人', value: 156, colorToken: '$blue500' },
  { label: '本周新增', value: 8, colorToken: '$green500' },
  { label: '待跟进', value: 12, colorToken: '$yellow500' },
  { label: '本月会议', value: 23, colorToken: '$red500' },
];

const mockRecentContacts = [
  { id: '1', name: '张三', company: '阿里巴巴', avatarUrl: undefined, lastContact: '2天前' },
  { id: '2', name: '李四', company: '腾讯', avatarUrl: undefined, lastContact: '1周前' },
  { id: '3', name: '王五', company: '字节跳动', avatarUrl: undefined, lastContact: '3天前' },
  { id: '4', name: '赵六', company: '美团', avatarUrl: undefined, lastContact: '5天前' },
];

const mockReminders = [
  { id: 1, title: '与张三讨论合作项目', time: '今天 14:00', type: 'meeting', colorToken: '$blue500' },
  { id: 2, title: '回访李四关于产品反馈', time: '明天 10:00', type: 'followup', colorToken: '$yellow500' },
  { id: 3, title: '准备下周的项目演示', time: '本周五 16:00', type: 'task', colorToken: '$green500' },
];

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return '早上好';
    if (hour < 18) return '下午好';
    return '晚上好';
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long',
    });
  };

  const quickActions = [
    { id: 1, title: '添加联系人', icon: Plus, colorToken: '$blue500', onPress: () => navigation.navigate('ContactCreate', {}) },
    { id: 2, title: '记录会议', icon: Calendar, colorToken: '$green500', onPress: () => navigation.navigate('MeetingRecord', {}) },
    { id: 3, title: '查看图谱', icon: Map, colorToken: '$yellow500', onPress: () => navigation.navigate('NetworkVisual') },
    { id: 4, title: '设置', icon: Settings, colorToken: '$trueGray500', onPress: () => navigation.navigate('Settings') },
  ];

  return (
    <Box flex={1} bg="$backgroundLight50">
      <StatusBar 
        barStyle={Platform.OS === 'ios' ? 'dark-content' : 'light-content'} 
        backgroundColor="$backgroundLight50" 
      />
      
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 16 }} 
        pt={24} 
        px={16} 
      >
        <VStack space="lg">
          {/* 头部问候区域 */}
          <HStack justifyContent="space-between" alignItems="center">
            <VStack>
              <Heading size="lg" color="$textDark900">
                {getGreeting()}
              </Heading>
              <Text size="sm" color="$textDark700">
                {formatDate(currentTime)}
              </Text>
            </VStack>
            <Pressable onPress={() => navigation.navigate('Settings')} bg="$backgroundLight0" p={12} rounded="$full">
              <Icon as={Bell} size={24} color="$primary500" />
            </Pressable>
          </HStack>

          {/* 统计数据卡片 */}
          <HStack space="sm" justifyContent="space-between">
            {mockStats.map((stat, index) => (
              <Pressable 
                key={index} 
                flex={1} 
                bg="$backgroundLight0" 
                rounded="$md" 
                p={16} 
                alignItems="center"
                sx={{
                  _dark: {
                    bg: '$backgroundDark900'
                  }
                }}
              >
                <Heading size="2xl" color={stat.colorToken}>
                  {stat.value}
                </Heading>
                <Text size="xs" color="$textDark700" textAlign="center" mt={4}>
                  {stat.label}
                </Text>
              </Pressable>
            ))}
          </HStack>

          {/* 快速操作按钮 */}
          <HStack justifyContent="space-around">
            {quickActions.map((action) => (
              <Pressable key={action.id} alignItems="center" onPress={action.onPress}>
                <Box 
                  w={56} h={56} rounded="$full" 
                  alignItems="center" justifyContent="center" 
                  mb={8} 
                  bg={action.colorToken} 
                  opacity={0.2}
                >
                  <Icon as={action.icon} size={24} color={action.colorToken} />
                </Box>
                <Text size="xs" color="$textDark700" textAlign="center">
                  {action.title}
                </Text>
              </Pressable>
            ))}
          </HStack>

          {/* 最近联系人 */}
          <VStack space="md">
            <HStack justifyContent="space-between" alignItems="center">
              <Heading size="md" color="$textDark900">最近联系人</Heading>
              <Link onPress={() => { /* Navigate to all contacts */ }}>
                <LinkText size="sm" color="$primary500">查看全部</LinkText>
              </Link>
            </HStack>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ gap: 16 }}>
              {mockRecentContacts.map((contact) => (
                <Pressable 
                  key={contact.id} 
                  bg="$backgroundLight0" 
                  rounded="$md" 
                  p={16} 
                  w={140} 
                  alignItems="center"
                  onPress={() => navigation.navigate('ContactDetail', { contactId: contact.id })}
                  sx={{
                    _dark: {
                      bg: '$backgroundDark900'
                    }
                  }}
                >
                  <Avatar size="md" mb={8}>
                    {contact.avatarUrl ? (
                      <AvatarImage source={{ uri: contact.avatarUrl }} alt={contact.name} />
                    ) : (
                      <AvatarFallbackText>{contact.name.charAt(0)}</AvatarFallbackText>
                    )}
                  </Avatar>
                  <Text fontWeight="$bold" color="$textDark900" size="sm">
                    {contact.name}
                  </Text>
                  <Text size="xs" color="$textDark700">
                    {contact.company}
                  </Text>
                  <Text size="xs" color="$textDark500" mt={4}>
                    {contact.lastContact}
                  </Text>
                </Pressable>
              ))}
            </ScrollView>
          </VStack>

          {/* 提醒事项 */}
          <VStack space="md">
            <HStack justifyContent="space-between" alignItems="center">
              <Heading size="md" color="$textDark900">提醒事项</Heading>
              <Link onPress={() => { /* Navigate to all reminders */ }}>
                <LinkText size="sm" color="$primary500">查看全部</LinkText>
              </Link>
            </HStack>
            <VStack space="sm">
              {mockReminders.map((reminder) => (
                <Pressable 
                  key={reminder.id} 
                  bg="$backgroundLight0" 
                  rounded="$md" 
                  p={16}
                  sx={{
                    _dark: {
                      bg: '$backgroundDark900'
                    }
                  }}
                >
                  <HStack alignItems="center" space="md">
                    <Box bg={reminder.colorToken} rounded="$full" w={8} h={8} />
                    <VStack flex={1}>
                      <Text fontWeight="$medium" color="$textDark900" size="sm">
                        {reminder.title}
                      </Text>
                      <HStack alignItems="center" mt={4} space="xs">
                        <Icon as={Clock} size="xs" color="$textDark700" />
                        <Text size="xs" color="$textDark700">
                          {reminder.time}
                        </Text>
                      </HStack>
                    </VStack>
                    <Icon as={ChevronRight} size="md" color="$textDark500" />
                  </HStack>
                </Pressable>
              ))}
            </VStack>
          </VStack>

          {/* 网络图谱预览 */}
          <VStack space="md">
            <HStack justifyContent="space-between" alignItems="center">
              <Heading size="md" color="$textDark900">关系图谱</Heading>
            </HStack>
            <Pressable 
              bg="$backgroundLight0" 
              rounded="$md" 
              p={24} 
              alignItems="center"
              onPress={() => navigation.navigate('NetworkVisual')}
              sx={{
                _dark: {
                  bg: '$backgroundDark900'
                }
              }}
            >
              <Icon as={Map} size={48} color="$primary500" />
              <Heading size="sm" color="$textDark900" mt={8}>探索您的关系网络</Heading>
              <Text size="xs" color="$textDark700" textAlign="center" mt={4}>
                可视化人脉连接，发现潜在机会。
              </Text>
            </Pressable>
          </VStack>

        </VStack>
      </ScrollView>
    </Box>
  );
};

export default HomeScreen;