/**
 * 商务活动/事件管理页面
 * 从会议记录页面演进而来，支持更丰富的商务活动管理
 */

import React, { useState, useEffect } from 'react';
import { Platform } from 'react-native';
import {
  Box,
  Text,
  Heading,
  VStack,
  HStack,
  Button,
  ButtonText,
  ButtonIcon,
  Pressable,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  Textarea,
  TextareaInput,
  Switch,
  ScrollView,
  FlatList,
  KeyboardAvoidingView,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
  Badge,
  BadgeText,
  Divider,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
} from '@gluestack-ui/themed';
import { useNavigation } from '@react-navigation/native';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Plus,
  Minus,
  ArrowLeft,
  Search,
  Edit,
  Trash2,
  CheckCircle,
  X,
  Video,
  FileText,
  Building,
  Tag,
  ChevronDown,
  Star,
  TrendingUp,
} from 'lucide-react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';

// Import global state and hooks
import { useContactsStore, useBusinessEventStore, useBusinessEventSelectors } from '../store';
import { usePerformanceMonitor, useNetworkError } from '../hooks';
import { ErrorBoundary, StandardButton, StandardIcon, CircularButton } from '../components/ui';
import { 
  BusinessEvent, 
  EventTask, 
  EventParticipant,
  Contact, 
  BusinessEventType, 
  BusinessEventScale,
  EventRelationshipContext 
} from '../types';

type BusinessEventScreenNavigationProp = any; // TODO: 添加正确的导航类型

interface EventFormData {
  title: string;
  description: string;
  date: Date;
  startTime: Date;
  endTime: Date | null;
  location: string;
  isVirtual: boolean;
  eventLink: string;
  eventType: BusinessEventType;
  category: string;
  tags: string[];
  scale: BusinessEventScale;
  agenda: string;
  objectives: string[];
  notes: string;
  participants: Omit<EventParticipant, 'contactId'>[];
  participantIds: string[];
  tasks: Omit<EventTask, 'id' | 'createdAt' | 'updatedAt'>[];
}

/**
 * 商务活动/事件管理屏幕
 * 支持创建、编辑和管理各类商务活动
 */
const BusinessEventScreen: React.FC = () => {
  const navigation = useNavigation<BusinessEventScreenNavigationProp>();
  const toast = useToast();

  // Performance monitoring
  usePerformanceMonitor('BusinessEventScreen');

  // Global state
  const { contacts } = useContactsStore();
  const {
    addEvent,
    updateEvent,
    deleteEvent,
    addEventTask,
    toggleTaskCompletion,
    setLoading
  } = useBusinessEventStore();
  const upcomingEvents = useBusinessEventSelectors.useUpcomingEvents();
  const pastEvents = useBusinessEventSelectors.usePastEvents();
  const { showError, clearError } = useNetworkError();

  // Local state
  const [activeTab, setActiveTab] = useState<'new' | 'history'>('new');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [timePickerMode, setTimePickerMode] = useState<'start' | 'end'>('start');
  const [showParticipantModal, setShowParticipantModal] = useState(false);
  const [participantSearchQuery, setParticipantSearchQuery] = useState('');
  const [newTag, setNewTag] = useState('');
  const [newObjective, setNewObjective] = useState('');

  // Form data state
  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    description: '',
    date: new Date(),
    startTime: new Date(),
    endTime: null,
    location: '',
    isVirtual: false,
    eventLink: '',
    eventType: 'business_meeting',
    category: '',
    tags: [],
    scale: 'small',
    agenda: '',
    objectives: [],
    notes: '',
    participants: [],
    participantIds: [],
    tasks: [],
  });

  // Form validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  const resetForm = () => {
    const now = new Date();
    setFormData({
      title: '',
      description: '',
      date: now,
      startTime: now,
      endTime: null,
      location: '',
      isVirtual: false,
      eventLink: '',
      eventType: 'business_meeting',
      category: '',
      tags: [],
      scale: 'small',
      agenda: '',
      objectives: [],
      notes: '',
      participants: [],
      participantIds: [],
      tasks: [],
    });
    setErrors({});
    setParticipantSearchQuery('');
    setNewTag('');
    setNewObjective('');
  };
  
  // Form update helpers
  const updateFormData = (updates: Partial<EventFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    // Clear related errors when user starts typing
    Object.keys(updates).forEach(key => {
      if (errors[key]) {
        setErrors(prev => ({ ...prev, [key]: undefined }));
      }
    });
  };

  // Date and time handlers
  const handleDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      updateFormData({ date: selectedDate });
    }
  };

  const handleTimeChange = (event: DateTimePickerEvent, selectedTime?: Date) => {
    setShowTimePicker(false);
    if (selectedTime) {
      if (timePickerMode === 'start') {
        updateFormData({ startTime: selectedTime });
      } else {
        updateFormData({ endTime: selectedTime });
      }
    }
  };

  // Tag management
  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      updateFormData({
        tags: [...formData.tags, newTag.trim()]
      });
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    updateFormData({
      tags: formData.tags.filter(tag => tag !== tagToRemove)
    });
  };

  // Objective management
  const addObjective = () => {
    if (newObjective.trim() && !formData.objectives.includes(newObjective.trim())) {
      updateFormData({
        objectives: [...formData.objectives, newObjective.trim()]
      });
      setNewObjective('');
    }
  };

  const removeObjective = (objectiveToRemove: string) => {
    updateFormData({
      objectives: formData.objectives.filter(obj => obj !== objectiveToRemove)
    });
  };

  // Participant management
  const toggleParticipant = (contactId: string) => {
    const currentParticipants = formData.participantIds;
    if (currentParticipants.includes(contactId)) {
      updateFormData({
        participantIds: currentParticipants.filter(id => id !== contactId)
      });
    } else {
      const contact = contacts.find(c => c.id === contactId);
      if (contact) {
        updateFormData({
          participantIds: [...currentParticipants, contactId]
        });
      }
    }
  };

  // Task management
  const addTask = () => {
    updateFormData({
      tasks: [...formData.tasks, { 
        title: '', 
        description: '', 
        priority: 'medium',
        isCompleted: false 
      }]
    });
  };

  const updateTask = (index: number, updates: Partial<EventTask>) => {
    const newTasks = [...formData.tasks];
    newTasks[index] = { ...newTasks[index], ...updates };
    updateFormData({ tasks: newTasks });
  };

  const removeTask = (index: number) => {
    if (formData.tasks.length > 0) {
      const newTasks = formData.tasks.filter((_, i) => i !== index);
      updateFormData({ tasks: newTasks });
    }
  };
  
  // Form validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '活动标题不能为空';
    }

    if (formData.participantIds.length === 0) {
      newErrors.participants = '请至少选择一位参与者';
    }

    if (formData.isVirtual && !formData.eventLink.trim()) {
      newErrors.eventLink = '线上活动需要提供活动链接';
    }

    if (!formData.isVirtual && !formData.location.trim()) {
      newErrors.location = '线下活动需要提供活动地点';
    }

    if (!formData.category.trim()) {
      newErrors.category = '请填写活动分类';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Save event
  const handleSaveEvent = async () => {
    if (!validateForm()) {
      toast.show({
        placement: 'top',
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>表单验证失败</ToastTitle>
            <ToastDescription>请检查并修正表单中的错误</ToastDescription>
          </Toast>
        ),
      });
      return;
    }

    setLoading(true);
    clearError();

    try {
      // 构建参与者数据
      const participants: EventParticipant[] = formData.participantIds.map(contactId => {
        const contact = contacts.find(c => c.id === contactId);
        return {
          contactId,
          name: contact?.name || '',
          status: 'invited',
          relationshipContext: 'existing' as EventRelationshipContext,
          interactionLevel: 'none',
          businessCardExchanged: false,
          followUpRequired: false,
        };
      });

      const eventData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        date: formData.date,
        startTime: formData.startTime,
        endTime: formData.endTime,
        location: formData.isVirtual ? formData.eventLink : formData.location,
        isVirtual: formData.isVirtual,
        eventLink: formData.isVirtual ? formData.eventLink : undefined,
        eventType: formData.eventType,
        category: formData.category.trim(),
        tags: formData.tags,
        scale: formData.scale,
        participants,
        organizerId: 'current-user', // TODO: 从用户状态获取
        agenda: formData.agenda.trim(),
        objectives: formData.objectives,
        notes: formData.notes.trim(),
        tasks: formData.tasks.filter(task => task.title.trim()),
        followUps: [],
        status: 'planned' as const,
        relationshipsEstablished: 0,
        relationshipsStrengthened: 0,
      };

      await addEvent(eventData);

      toast.show({
        placement: 'top',
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>活动创建成功</ToastTitle>
            <ToastDescription>商务活动已成功保存到日程中</ToastDescription>
          </Toast>
        ),
      });

      resetForm();
      setActiveTab('history');
    } catch (error) {
      console.error('Error saving event:', error);
      showError({
        message: '保存活动时出错，请重试',
        code: 'SAVE_ERROR'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    resetForm();
    navigation.goBack();
  };

  // Filtered contacts for participant selection
  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(participantSearchQuery.toLowerCase()) ||
    (contact.company && contact.company.toLowerCase().includes(participantSearchQuery.toLowerCase()))
  );

  // Get selected participants details
  const selectedParticipants = contacts.filter(contact =>
    formData.participantIds.includes(contact.id)
  );

  // Event type options
  const eventTypeOptions = [
    { value: 'business_meeting', label: '商务会议', icon: Users },
    { value: 'industry_conference', label: '行业会议', icon: Building },
    { value: 'exhibition', label: '展会', icon: Star },
    { value: 'seminar', label: '研讨会', icon: FileText },
    { value: 'networking', label: '社交活动', icon: Users },
    { value: 'client_visit', label: '客户拜访', icon: MapPin },
    { value: 'training', label: '培训', icon: TrendingUp },
    { value: 'lecture', label: '讲座', icon: FileText },
    { value: 'product_launch', label: '产品发布会', icon: Star },
    { value: 'business_dinner', label: '商务晚宴', icon: Users },
    { value: 'workshop', label: '工作坊', icon: Building },
    { value: 'other', label: '其他', icon: Plus },
  ];

  const scaleOptions = [
    { value: 'small', label: '小型 (< 10人)' },
    { value: 'medium', label: '中型 (10-50人)' },
    { value: 'large', label: '大型 (50-200人)' },
    { value: 'massive', label: '超大型 (> 200人)' },
  ];

  // Utility functions
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDateTime = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getEventTypeLabel = (type: BusinessEventType) => {
    return eventTypeOptions.find(option => option.value === type)?.label || type;
  };

  return (
    <ErrorBoundary>
      <Box flex={1} bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark950' } }}>
        {/* Header */}
        <Box
          pt="$12"
          pb="$4"
          px="$4"
          bg="$backgroundLight0"
          sx={{ _dark: { bg: '$backgroundDark900' } }}
          borderBottomWidth="$1"
          borderBottomColor="$borderLight200"
          sx={{ _dark: { borderBottomColor: '$borderDark700' } }}
        >
          <HStack justifyContent="space-between" alignItems="center">
            <HStack alignItems="center" space="md">
              <CircularButton
                icon={ArrowLeft}
                size="sm"
                variant="ghost"
                onPress={handleCancel}
              />
              <Heading size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                商务活动管理
              </Heading>
            </HStack>
          </HStack>

          {/* Tab Navigation */}
          <HStack mt="$4" space="md">
            <Pressable
              flex={1}
              onPress={() => setActiveTab('new')}
              p="$3"
              borderRadius="$md"
              bg={activeTab === 'new' ? '$primary100' : 'transparent'}
              sx={{
                _dark: {
                  bg: activeTab === 'new' ? '$primary900' : 'transparent'
                }
              }}
            >
              <Text
                textAlign="center"
                fontWeight={activeTab === 'new' ? '$bold' : '$normal'}
                color={activeTab === 'new' ? '$primary600' : '$textLight600'}
                sx={{
                  _dark: {
                    color: activeTab === 'new' ? '$primary400' : '$textDark400'
                  }
                }}
              >
                创建活动
              </Text>
            </Pressable>

            <Pressable
              flex={1}
              onPress={() => setActiveTab('history')}
              p="$3"
              borderRadius="$md"
              bg={activeTab === 'history' ? '$primary100' : 'transparent'}
              sx={{
                _dark: {
                  bg: activeTab === 'history' ? '$primary900' : 'transparent'
                }
              }}
            >
              <Text
                textAlign="center"
                fontWeight={activeTab === 'history' ? '$bold' : '$normal'}
                color={activeTab === 'history' ? '$primary600' : '$textLight600'}
                sx={{
                  _dark: {
                    color: activeTab === 'history' ? '$primary400' : '$textDark400'
                  }
                }}
              >
                活动历史
              </Text>
            </Pressable>
          </HStack>
        </Box>

        {/* Content */}
        {activeTab === 'new' ? (
          <KeyboardAvoidingView
            flex={1}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
          >
            <ScrollView flex={1} p="$4">
              <VStack space="lg">
                {/* Event Title */}
                <VStack space="sm">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    活动标题 *
                  </Text>
                  <Input
                    variant="outline"
                    size="md"
                    isInvalid={!!errors.title}
                  >
                    <InputField
                      placeholder="输入活动标题"
                      value={formData.title}
                      onChangeText={(text) => updateFormData({ title: text })}
                    />
                  </Input>
                  {errors.title && (
                    <Text size="sm" color="$error600">{errors.title}</Text>
                  )}
                </VStack>

                {/* Event Type */}
                <VStack space="sm">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    活动类型 *
                  </Text>
                  <Select
                    selectedValue={formData.eventType}
                    onValueChange={(value) => updateFormData({ eventType: value as BusinessEventType })}
                  >
                    <SelectTrigger variant="outline" size="md">
                      <SelectInput placeholder="选择活动类型" />
                      <SelectIcon as={ChevronDown} />
                    </SelectTrigger>
                    <SelectPortal>
                      <SelectBackdrop />
                      <SelectContent>
                        <SelectDragIndicatorWrapper>
                          <SelectDragIndicator />
                        </SelectDragIndicatorWrapper>
                        {eventTypeOptions.map((option) => (
                          <SelectItem key={option.value} label={option.label} value={option.value} />
                        ))}
                      </SelectContent>
                    </SelectPortal>
                  </Select>
                </VStack>

                {/* Category and Scale */}
                <HStack space="md">
                  <VStack space="sm" flex={1}>
                    <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                      行业分类 *
                    </Text>
                    <Input variant="outline" size="md" isInvalid={!!errors.category}>
                      <InputField
                        placeholder="如：科技、金融、医疗"
                        value={formData.category}
                        onChangeText={(text) => updateFormData({ category: text })}
                      />
                    </Input>
                    {errors.category && (
                      <Text size="sm" color="$error600">{errors.category}</Text>
                    )}
                  </VStack>

                  <VStack space="sm" flex={1}>
                    <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                      活动规模
                    </Text>
                    <Select
                      selectedValue={formData.scale}
                      onValueChange={(value) => updateFormData({ scale: value as BusinessEventScale })}
                    >
                      <SelectTrigger variant="outline" size="md">
                        <SelectInput placeholder="选择规模" />
                        <SelectIcon as={ChevronDown} />
                      </SelectTrigger>
                      <SelectPortal>
                        <SelectBackdrop />
                        <SelectContent>
                          <SelectDragIndicatorWrapper>
                            <SelectDragIndicator />
                          </SelectDragIndicatorWrapper>
                          {scaleOptions.map((option) => (
                            <SelectItem key={option.value} label={option.label} value={option.value} />
                          ))}
                        </SelectContent>
                      </SelectPortal>
                    </Select>
                  </VStack>
                </HStack>

                {/* Event Description */}
                <VStack space="sm">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    活动描述
                  </Text>
                  <Textarea>
                    <TextareaInput
                      placeholder="输入活动描述（可选）"
                      value={formData.description}
                      onChangeText={(text) => updateFormData({ description: text })}
                      numberOfLines={3}
                    />
                  </Textarea>
                </VStack>

                {/* Tags */}
                <VStack space="sm">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    活动标签
                  </Text>
                  <HStack space="sm" alignItems="center">
                    <Input flex={1} variant="outline" size="md">
                      <InputField
                        placeholder="添加标签"
                        value={newTag}
                        onChangeText={setNewTag}
                        onSubmitEditing={addTag}
                      />
                    </Input>
                    <StandardButton
                      size="sm"
                      variant="outline"
                      onPress={addTag}
                      leftIcon={Plus}
                    >
                      添加
                    </StandardButton>
                  </HStack>
                  {formData.tags.length > 0 && (
                    <HStack space="sm" flexWrap="wrap">
                      {formData.tags.map((tag, index) => (
                        <Badge key={index} size="md" variant="solid" action="muted" mr="$2" mb="$2">
                          <BadgeText>{tag}</BadgeText>
                          <Pressable onPress={() => removeTag(tag)} ml="$1">
                            <StandardIcon as={X} size="xs" color="$textLight600" />
                          </Pressable>
                        </Badge>
                      ))}
                    </HStack>
                  )}
                </VStack>

                {/* Date Selection */}
                <VStack space="sm">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    活动日期 *
                  </Text>
                  <Pressable onPress={() => setShowDatePicker(true)}>
                    <Box
                      p="$3"
                      borderWidth="$1"
                      borderColor="$borderLight200"
                      borderRadius="$md"
                      bg="$backgroundLight0"
                      sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
                    >
                      <HStack justifyContent="space-between" alignItems="center">
                        <Text color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                          {formatDate(formData.date)}
                        </Text>
                        <StandardIcon as={Calendar} size="md" color="$primary500" />
                      </HStack>
                    </Box>
                  </Pressable>
                  {showDatePicker && (
                    <DateTimePicker
                      value={formData.date}
                      mode="date"
                      display="default"
                      onChange={handleDateChange}
                    />
                  )}
                </VStack>

                {/* Time Selection */}
                <HStack space="md">
                  <VStack space="sm" flex={1}>
                    <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                      开始时间 *
                    </Text>
                    <Pressable onPress={() => {
                      setTimePickerMode('start');
                      setShowTimePicker(true);
                    }}>
                      <Box
                        p="$3"
                        borderWidth="$1"
                        borderColor="$borderLight200"
                        borderRadius="$md"
                        bg="$backgroundLight0"
                        sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
                      >
                        <HStack justifyContent="space-between" alignItems="center">
                          <Text color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                            {formatTime(formData.startTime)}
                          </Text>
                          <StandardIcon as={Clock} size="sm" color="$primary500" />
                        </HStack>
                      </Box>
                    </Pressable>
                  </VStack>

                  <VStack space="sm" flex={1}>
                    <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                      结束时间
                    </Text>
                    <Pressable onPress={() => {
                      setTimePickerMode('end');
                      setShowTimePicker(true);
                    }}>
                      <Box
                        p="$3"
                        borderWidth="$1"
                        borderColor="$borderLight200"
                        borderRadius="$md"
                        bg="$backgroundLight0"
                        sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
                      >
                        <HStack justifyContent="space-between" alignItems="center">
                          <Text color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                            {formData.endTime ? formatTime(formData.endTime) : '选择时间'}
                          </Text>
                          <StandardIcon as={Clock} size="sm" color="$primary500" />
                        </HStack>
                      </Box>
                    </Pressable>
                  </VStack>
                </HStack>

                {showTimePicker && (
                  <DateTimePicker
                    value={timePickerMode === 'start' ? formData.startTime : (formData.endTime || new Date())}
                    mode="time"
                    display="default"
                    onChange={handleTimeChange}
                  />
                )}

                {/* Virtual Event Toggle */}
                <VStack space="sm">
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                      线上活动
                    </Text>
                    <HStack space="sm" alignItems="center">
                      <StandardIcon
                        as={formData.isVirtual ? Video : MapPin}
                        size="sm"
                        color={formData.isVirtual ? '$primary500' : '$textLight600'}
                      />
                      <Switch
                        value={formData.isVirtual}
                        onValueChange={(value) => updateFormData({ isVirtual: value })}
                        trackColor={{ false: '$neutral300', true: '$primary200' }}
                        thumbColor={formData.isVirtual ? '$primary500' : '$neutral500'}
                      />
                    </HStack>
                  </HStack>
                </VStack>

                {/* Location/Event Link */}
                <VStack space="sm">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    {formData.isVirtual ? '活动链接 *' : '活动地点 *'}
                  </Text>
                  <Input
                    variant="outline"
                    size="md"
                    isInvalid={!!(formData.isVirtual ? errors.eventLink : errors.location)}
                  >
                    <InputSlot pl="$3">
                      <InputIcon as={formData.isVirtual ? Video : MapPin} />
                    </InputSlot>
                    <InputField
                      placeholder={formData.isVirtual ? "输入活动链接或平台" : "输入活动地点"}
                      value={formData.isVirtual ? formData.eventLink : formData.location}
                      onChangeText={(text) => updateFormData(
                        formData.isVirtual
                          ? { eventLink: text }
                          : { location: text }
                      )}
                    />
                  </Input>
                  {(formData.isVirtual ? errors.eventLink : errors.location) && (
                    <Text size="sm" color="$error600">
                      {formData.isVirtual ? errors.eventLink : errors.location}
                    </Text>
                  )}
                </VStack>

                {/* Form Actions */}
                <HStack space="md" mt="$6">
                  <StandardButton
                    variant="outline"
                    onPress={handleCancel}
                    flex={1}
                  >
                    取消
                  </StandardButton>
                  <StandardButton
                    variant="solid"
                    action="primary"
                    onPress={handleSaveEvent}
                    flex={1}
                  >
                    保存活动
                  </StandardButton>
                </HStack>

                {/* Spacer for keyboard */}
                <Box h="$16" />
              </VStack>
            </ScrollView>
          </KeyboardAvoidingView>
        ) : (
          // 活动历史列表
          <Box flex={1} p="$4">
            <VStack space="md">
              {upcomingEvents.length > 0 && (
                <VStack space="sm">
                  <Text fontWeight="$bold" size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    即将到来的活动
                  </Text>
                  {upcomingEvents.slice(0, 3).map((event) => (
                    <Box
                      key={event.id}
                      p="$4"
                      bg="$backgroundLight0"
                      borderRadius="$lg"
                      borderWidth="$1"
                      borderColor="$borderLight200"
                      sx={{
                        _dark: {
                          bg: '$backgroundDark900',
                          borderColor: '$borderDark700',
                        }
                      }}
                    >
                      <VStack space="sm">
                        <HStack justifyContent="space-between" alignItems="flex-start">
                          <VStack flex={1} space="xs">
                            <Text fontWeight="$bold" size="md" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                              {event.title}
                            </Text>
                            <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                              {getEventTypeLabel(event.eventType)} • {formatDateTime(event.date)}
                            </Text>
                          </VStack>
                          <Badge size="sm" variant="solid" action="info">
                            <BadgeText>{event.status}</BadgeText>
                          </Badge>
                        </HStack>

                        <HStack space="sm" alignItems="center">
                          <StandardIcon
                            as={event.isVirtual ? Video : MapPin}
                            size="sm"
                            color="$textLight600"
                          />
                          <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                            {event.location || '无地点信息'}
                          </Text>
                        </HStack>

                        <HStack space="sm" alignItems="center">
                          <StandardIcon as={Users} size="sm" color="$textLight600" />
                          <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                            {event.participants.length} 位参与者
                          </Text>
                        </HStack>
                      </VStack>
                    </Box>
                  ))}
                </VStack>
              )}

              {pastEvents.length > 0 && (
                <VStack space="sm">
                  <Text fontWeight="$bold" size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    历史活动
                  </Text>
                  {pastEvents.slice(0, 5).map((event) => (
                    <Box
                      key={event.id}
                      p="$4"
                      bg="$backgroundLight0"
                      borderRadius="$lg"
                      borderWidth="$1"
                      borderColor="$borderLight200"
                      sx={{
                        _dark: {
                          bg: '$backgroundDark900',
                          borderColor: '$borderDark700',
                        }
                      }}
                    >
                      <VStack space="sm">
                        <HStack justifyContent="space-between" alignItems="flex-start">
                          <VStack flex={1} space="xs">
                            <Text fontWeight="$bold" size="md" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                              {event.title}
                            </Text>
                            <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                              {getEventTypeLabel(event.eventType)} • {formatDateTime(event.date)}
                            </Text>
                          </VStack>
                          <Badge
                            size="sm"
                            variant="solid"
                            action={event.status === 'completed' ? 'success' : 'error'}
                          >
                            <BadgeText>
                              {event.status === 'completed' ? '已完成' : '已取消'}
                            </BadgeText>
                          </Badge>
                        </HStack>

                        {event.relationshipsEstablished > 0 && (
                          <HStack space="sm" alignItems="center">
                            <StandardIcon as={TrendingUp} size="sm" color="$success600" />
                            <Text size="sm" color="$success600">
                              建立了 {event.relationshipsEstablished} 个新关系
                            </Text>
                          </HStack>
                        )}
                      </VStack>
                    </Box>
                  ))}
                </VStack>
              )}

              {upcomingEvents.length === 0 && pastEvents.length === 0 && (
                <Box flex={1} justifyContent="center" alignItems="center">
                  <VStack space="md" alignItems="center">
                    <StandardIcon as={Calendar} size="4xl" color="$textLight400" />
                    <Text textAlign="center" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                      还没有活动记录
                    </Text>
                    <Text textAlign="center" color="$textLight400" sx={{ _dark: { color: '$textDark600' } }}>
                      创建第一个商务活动来开始记录
                    </Text>
                    <StandardButton
                      variant="outline"
                      onPress={() => setActiveTab('new')}
                      mt="$4"
                    >
                      创建活动
                    </StandardButton>
                  </VStack>
                </Box>
              )}
            </VStack>
          </Box>
        )}
      </Box>
    </ErrorBoundary>
  );
};

export default BusinessEventScreen;
