/**
 * AI推荐系统状态管理
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  RecommendationItem, 
  RecommendationType, 
  UserPreferences,
  recommendationEngine,
  RecommendationContext
} from '../services/aiRecommendationService';

interface RecommendationState {
  // 推荐数据
  recommendations: RecommendationItem[];
  activeRecommendations: RecommendationItem[];
  dismissedRecommendations: string[]; // 已忽略的推荐ID
  
  // 用户偏好
  userPreferences: UserPreferences | null;
  
  // UI状态
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  
  // 过滤状态
  typeFilter: RecommendationType | 'all';
  priorityFilter: 'all' | 'urgent' | 'high' | 'medium' | 'low';
  showDismissed: boolean;
}

interface RecommendationActions {
  // 推荐生成
  generateRecommendations: (context: RecommendationContext) => Promise<void>;
  refreshRecommendations: (context: RecommendationContext) => Promise<void>;
  getRecommendationsByType: (type: RecommendationType, context: RecommendationContext) => Promise<void>;
  
  // 推荐管理
  dismissRecommendation: (recommendationId: string) => void;
  undoDismissRecommendation: (recommendationId: string) => void;
  markRecommendationAsActioned: (recommendationId: string) => void;
  
  // 用户反馈
  provideFeedback: (recommendationId: string, feedback: 'positive' | 'negative' | 'neutral') => Promise<void>;
  
  // 用户偏好管理
  updateUserPreferences: (preferences: Partial<UserPreferences>) => void;
  resetUserPreferences: () => void;
  
  // 过滤和搜索
  setTypeFilter: (type: RecommendationState['typeFilter']) => void;
  setPriorityFilter: (priority: RecommendationState['priorityFilter']) => void;
  setShowDismissed: (show: boolean) => void;
  clearFilters: () => void;
  
  // 工具方法
  getRecommendationById: (id: string) => RecommendationItem | undefined;
  getRecommendationsByPriority: (priority: 'urgent' | 'high' | 'medium' | 'low') => RecommendationItem[];
  getActiveRecommendationsCount: () => number;
  getRecommendationStats: () => {
    total: number;
    byType: Record<RecommendationType, number>;
    byPriority: Record<string, number>;
    dismissed: number;
  };
  
  // 状态管理
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // 数据清理
  cleanupExpiredRecommendations: () => void;
  clearAllRecommendations: () => void;
}

type RecommendationStore = RecommendationState & RecommendationActions;

const defaultUserPreferences: UserPreferences = {
  industries: [],
  eventTypes: [],
  relationshipGoals: [],
  communicationFrequency: 'medium',
  networkingStyle: 'moderate'
};

export const useRecommendationStore = create<RecommendationStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      recommendations: [],
      activeRecommendations: [],
      dismissedRecommendations: [],
      userPreferences: defaultUserPreferences,
      isLoading: false,
      error: null,
      lastUpdated: null,
      typeFilter: 'all',
      priorityFilter: 'all',
      showDismissed: false,

      // 推荐生成
      generateRecommendations: async (context: RecommendationContext) => {
        set({ isLoading: true, error: null });
        
        try {
          const newRecommendations = await recommendationEngine.generateRecommendations(context);
          
          set((state) => {
            const updatedRecommendations = [...state.recommendations];
            
            // 添加新推荐，避免重复
            newRecommendations.forEach(newRec => {
              const existingIndex = updatedRecommendations.findIndex(rec => rec.id === newRec.id);
              if (existingIndex >= 0) {
                updatedRecommendations[existingIndex] = newRec;
              } else {
                updatedRecommendations.push(newRec);
              }
            });

            const activeRecommendations = updatedRecommendations.filter(
              rec => !state.dismissedRecommendations.includes(rec.id) &&
                     (!rec.expiresAt || new Date(rec.expiresAt) > new Date())
            );

            return {
              recommendations: updatedRecommendations,
              activeRecommendations,
              lastUpdated: new Date(),
              isLoading: false
            };
          });
        } catch (error) {
          console.error('Error generating recommendations:', error);
          set({ 
            error: '生成推荐时出错，请重试', 
            isLoading: false 
          });
        }
      },

      refreshRecommendations: async (context: RecommendationContext) => {
        // 清理过期推荐
        get().cleanupExpiredRecommendations();
        
        // 重新生成推荐
        await get().generateRecommendations(context);
      },

      getRecommendationsByType: async (type: RecommendationType, context: RecommendationContext) => {
        set({ isLoading: true, error: null });
        
        try {
          const typeRecommendations = await recommendationEngine.getRecommendationsByType(type, context);
          
          set((state) => {
            const updatedRecommendations = [...state.recommendations];
            
            typeRecommendations.forEach(newRec => {
              const existingIndex = updatedRecommendations.findIndex(rec => rec.id === newRec.id);
              if (existingIndex >= 0) {
                updatedRecommendations[existingIndex] = newRec;
              } else {
                updatedRecommendations.push(newRec);
              }
            });

            const activeRecommendations = updatedRecommendations.filter(
              rec => !state.dismissedRecommendations.includes(rec.id) &&
                     (!rec.expiresAt || new Date(rec.expiresAt) > new Date())
            );

            return {
              recommendations: updatedRecommendations,
              activeRecommendations,
              lastUpdated: new Date(),
              isLoading: false
            };
          });
        } catch (error) {
          console.error('Error getting recommendations by type:', error);
          set({ 
            error: '获取推荐时出错，请重试', 
            isLoading: false 
          });
        }
      },

      // 推荐管理
      dismissRecommendation: (recommendationId: string) => {
        set((state) => ({
          dismissedRecommendations: [...state.dismissedRecommendations, recommendationId],
          activeRecommendations: state.activeRecommendations.filter(rec => rec.id !== recommendationId)
        }));
      },

      undoDismissRecommendation: (recommendationId: string) => {
        set((state) => {
          const dismissedRecommendations = state.dismissedRecommendations.filter(id => id !== recommendationId);
          const recommendation = state.recommendations.find(rec => rec.id === recommendationId);
          
          let activeRecommendations = [...state.activeRecommendations];
          if (recommendation && (!recommendation.expiresAt || new Date(recommendation.expiresAt) > new Date())) {
            activeRecommendations.push(recommendation);
          }

          return {
            dismissedRecommendations,
            activeRecommendations
          };
        });
      },

      markRecommendationAsActioned: (recommendationId: string) => {
        // 标记为已执行，从活跃推荐中移除
        set((state) => ({
          activeRecommendations: state.activeRecommendations.filter(rec => rec.id !== recommendationId)
        }));
      },

      // 用户反馈
      provideFeedback: async (recommendationId: string, feedback: 'positive' | 'negative' | 'neutral') => {
        try {
          await recommendationEngine.updateUserFeedback(recommendationId, feedback);
          
          // 根据反馈调整推荐
          if (feedback === 'negative') {
            get().dismissRecommendation(recommendationId);
          }
        } catch (error) {
          console.error('Error providing feedback:', error);
          set({ error: '提交反馈时出错' });
        }
      },

      // 用户偏好管理
      updateUserPreferences: (preferences: Partial<UserPreferences>) => {
        set((state) => ({
          userPreferences: {
            ...state.userPreferences!,
            ...preferences
          }
        }));
      },

      resetUserPreferences: () => {
        set({ userPreferences: defaultUserPreferences });
      },

      // 过滤和搜索
      setTypeFilter: (type: RecommendationState['typeFilter']) => {
        set({ typeFilter: type });
      },

      setPriorityFilter: (priority: RecommendationState['priorityFilter']) => {
        set({ priorityFilter: priority });
      },

      setShowDismissed: (show: boolean) => {
        set({ showDismissed: show });
      },

      clearFilters: () => {
        set({
          typeFilter: 'all',
          priorityFilter: 'all',
          showDismissed: false
        });
      },

      // 工具方法
      getRecommendationById: (id: string) => {
        return get().recommendations.find(rec => rec.id === id);
      },

      getRecommendationsByPriority: (priority: 'urgent' | 'high' | 'medium' | 'low') => {
        return get().activeRecommendations.filter(rec => rec.priority === priority);
      },

      getActiveRecommendationsCount: () => {
        return get().activeRecommendations.length;
      },

      getRecommendationStats: () => {
        const { recommendations, dismissedRecommendations } = get();
        
        const stats = {
          total: recommendations.length,
          byType: {} as Record<RecommendationType, number>,
          byPriority: {} as Record<string, number>,
          dismissed: dismissedRecommendations.length
        };

        recommendations.forEach(rec => {
          // 按类型统计
          stats.byType[rec.type] = (stats.byType[rec.type] || 0) + 1;
          
          // 按优先级统计
          stats.byPriority[rec.priority] = (stats.byPriority[rec.priority] || 0) + 1;
        });

        return stats;
      },

      // 状态管理
      setLoading: (loading: boolean) => set({ isLoading: loading }),
      setError: (error: string | null) => set({ error }),
      clearError: () => set({ error: null }),

      // 数据清理
      cleanupExpiredRecommendations: () => {
        const now = new Date();
        
        set((state) => {
          const validRecommendations = state.recommendations.filter(
            rec => !rec.expiresAt || new Date(rec.expiresAt) > now
          );
          
          const activeRecommendations = validRecommendations.filter(
            rec => !state.dismissedRecommendations.includes(rec.id)
          );

          return {
            recommendations: validRecommendations,
            activeRecommendations
          };
        });
      },

      clearAllRecommendations: () => {
        set({
          recommendations: [],
          activeRecommendations: [],
          dismissedRecommendations: [],
          lastUpdated: null,
          error: null
        });
      },
    }),
    {
      name: 'recommendation-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        dismissedRecommendations: state.dismissedRecommendations,
        userPreferences: state.userPreferences,
      }),
    }
  )
);

// 选择器
export const useRecommendationSelectors = {
  useRecommendations: () => useRecommendationStore((state) => state.recommendations),
  useActiveRecommendations: () => useRecommendationStore((state) => {
    const { activeRecommendations, typeFilter, priorityFilter } = state;
    
    return activeRecommendations.filter(rec => {
      // 类型过滤
      if (typeFilter !== 'all' && rec.type !== typeFilter) {
        return false;
      }
      
      // 优先级过滤
      if (priorityFilter !== 'all' && rec.priority !== priorityFilter) {
        return false;
      }
      
      return true;
    });
  }),
  useRecommendationStats: () => useRecommendationStore((state) => state.getRecommendationStats()),
  useUserPreferences: () => useRecommendationStore((state) => state.userPreferences),
  useIsLoading: () => useRecommendationStore((state) => state.isLoading),
  useError: () => useRecommendationStore((state) => state.error),
  useFilters: () => useRecommendationStore((state) => ({
    typeFilter: state.typeFilter,
    priorityFilter: state.priorityFilter,
    showDismissed: state.showDismissed
  })),
};
