/**
 * 商务活动/事件管理状态存储
 * 从meetingsStore演进而来，支持更丰富的商务活动管理
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  BusinessEvent, 
  EventTask, 
  EventFollowUp,
  EventParticipant,
  BusinessEventStatus, 
  BusinessEventType,
  BusinessEventScale,
  EventRelationshipContext
} from '../types';

interface BusinessEventState {
  // 活动数据
  events: BusinessEvent[];
  selectedEvent: BusinessEvent | null;
  
  // UI状态
  isLoading: boolean;
  error: string | null;
  
  // 过滤和搜索
  searchQuery: string;
  statusFilter: BusinessEventStatus | 'all';
  typeFilter: BusinessEventType | 'all';
  dateFilter: 'all' | 'today' | 'week' | 'month' | 'quarter' | 'year';
  scaleFilter: BusinessEventScale | 'all';
}

interface BusinessEventActions {
  // CRUD操作
  addEvent: (event: Omit<BusinessEvent, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateEvent: (id: string, updates: Partial<BusinessEvent>) => Promise<void>;
  deleteEvent: (id: string) => Promise<void>;
  duplicateEvent: (id: string) => Promise<void>;
  
  // 活动任务管理
  addEventTask: (eventId: string, task: Omit<EventTask, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateEventTask: (eventId: string, taskId: string, updates: Partial<EventTask>) => Promise<void>;
  deleteEventTask: (eventId: string, taskId: string) => Promise<void>;
  toggleTaskCompletion: (eventId: string, taskId: string) => Promise<void>;
  
  // 参与者管理（增强版）
  addParticipant: (eventId: string, participant: Omit<EventParticipant, 'contactId'> & { contactId: string }) => Promise<void>;
  updateParticipant: (eventId: string, contactId: string, updates: Partial<EventParticipant>) => Promise<void>;
  removeParticipant: (eventId: string, contactId: string) => Promise<void>;
  markParticipantAttended: (eventId: string, contactId: string) => Promise<void>;
  
  // 跟进管理
  addFollowUp: (eventId: string, followUp: Omit<EventFollowUp, 'id' | 'createdAt'>) => Promise<void>;
  updateFollowUp: (eventId: string, followUpId: string, updates: Partial<EventFollowUp>) => Promise<void>;
  completeFollowUp: (eventId: string, followUpId: string) => Promise<void>;
  
  // 活动状态管理
  confirmEvent: (eventId: string) => Promise<void>;
  startEvent: (eventId: string) => Promise<void>;
  completeEvent: (eventId: string, effectivenessScore?: number) => Promise<void>;
  cancelEvent: (eventId: string, reason?: string) => Promise<void>;
  postponeEvent: (eventId: string, newDate: Date) => Promise<void>;
  archiveEvent: (eventId: string) => Promise<void>;
  
  // 关系建立分析
  analyzeEventRelationships: (eventId: string) => Promise<void>;
  updateRelationshipStats: (eventId: string, established: number, strengthened: number) => Promise<void>;
  
  // 搜索和过滤
  setSearchQuery: (query: string) => void;
  setStatusFilter: (status: BusinessEventState['statusFilter']) => void;
  setTypeFilter: (type: BusinessEventState['typeFilter']) => void;
  setDateFilter: (filter: BusinessEventState['dateFilter']) => void;
  setScaleFilter: (scale: BusinessEventState['scaleFilter']) => void;
  clearFilters: () => void;
  
  // 选择和导航
  setSelectedEvent: (event: BusinessEvent | null) => void;
  
  // 工具方法
  getEventById: (id: string) => BusinessEvent | undefined;
  getEventsByDate: (date: Date) => BusinessEvent[];
  getEventsByType: (type: BusinessEventType) => BusinessEvent[];
  getUpcomingEvents: () => BusinessEvent[];
  getPastEvents: () => BusinessEvent[];
  getEventsByContact: (contactId: string) => BusinessEvent[];
  
  // 统计分析
  getEventStats: () => {
    total: number;
    byType: Record<BusinessEventType, number>;
    byStatus: Record<BusinessEventStatus, number>;
    totalRelationshipsEstablished: number;
    averageEffectiveness: number;
  };
  
  // 状态管理
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // 数据管理
  importEvents: (events: BusinessEvent[]) => Promise<void>;
  exportEvents: () => Promise<BusinessEvent[]>;
  clearAllEvents: () => void;
  initializeSampleData: () => Promise<void>;
}

type BusinessEventStore = BusinessEventState & BusinessEventActions;

// 辅助函数
const generateId = () => `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const filterEvents = (
  events: BusinessEvent[],
  filters: {
    searchQuery: string;
    statusFilter: BusinessEventState['statusFilter'];
    typeFilter: BusinessEventState['typeFilter'];
    dateFilter: BusinessEventState['dateFilter'];
    scaleFilter: BusinessEventState['scaleFilter'];
  }
): BusinessEvent[] => {
  return events.filter(event => {
    // 搜索过滤
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      const matchesSearch = 
        event.title.toLowerCase().includes(query) ||
        event.description?.toLowerCase().includes(query) ||
        event.location?.toLowerCase().includes(query) ||
        event.category.toLowerCase().includes(query) ||
        event.tags.some(tag => tag.toLowerCase().includes(query));
      
      if (!matchesSearch) return false;
    }
    
    // 状态过滤
    if (filters.statusFilter !== 'all' && event.status !== filters.statusFilter) {
      return false;
    }
    
    // 类型过滤
    if (filters.typeFilter !== 'all' && event.eventType !== filters.typeFilter) {
      return false;
    }
    
    // 规模过滤
    if (filters.scaleFilter !== 'all' && event.scale !== filters.scaleFilter) {
      return false;
    }
    
    // 日期过滤
    if (filters.dateFilter !== 'all') {
      const now = new Date();
      const eventDate = new Date(event.date);
      
      switch (filters.dateFilter) {
        case 'today':
          if (eventDate.toDateString() !== now.toDateString()) return false;
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          const weekLater = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
          if (eventDate < weekAgo || eventDate > weekLater) return false;
          break;
        case 'month':
          const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
          const monthLater = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
          if (eventDate < monthAgo || eventDate > monthLater) return false;
          break;
        case 'quarter':
          const quarterAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
          const quarterLater = new Date(now.getFullYear(), now.getMonth() + 3, now.getDate());
          if (eventDate < quarterAgo || eventDate > quarterLater) return false;
          break;
        case 'year':
          if (eventDate.getFullYear() !== now.getFullYear()) return false;
          break;
      }
    }
    
    return true;
  });
};

export const useBusinessEventStore = create<BusinessEventStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      events: [],
      selectedEvent: null,
      isLoading: false,
      error: null,
      searchQuery: '',
      statusFilter: 'all',
      typeFilter: 'all',
      dateFilter: 'all',
      scaleFilter: 'all',

      // CRUD操作
      addEvent: async (eventData) => {
        const newEvent: BusinessEvent = {
          ...eventData,
          id: generateId(),
          relationshipsEstablished: 0,
          relationshipsStrengthened: 0,
          isArchived: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          events: [...state.events, newEvent],
        }));

        // 自动创建活动提醒
        try {
          const { createEventReminders } = await import('./reminderStore');
          const reminderStore = createEventReminders;
          if (reminderStore) {
            await reminderStore(newEvent.id, newEvent.title, newEvent.date);
          }
        } catch (error) {
          console.warn('Failed to create event reminders:', error);
        }
      },

      updateEvent: async (id, updates) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === id
              ? { ...event, ...updates, updatedAt: new Date() }
              : event
          ),
        }));
      },

      deleteEvent: async (id) => {
        set((state) => ({
          events: state.events.filter((event) => event.id !== id),
          selectedEvent: state.selectedEvent?.id === id ? null : state.selectedEvent,
        }));
      },

      duplicateEvent: async (id) => {
        const originalEvent = get().getEventById(id);
        if (originalEvent) {
          const duplicatedEvent = {
            ...originalEvent,
            title: `${originalEvent.title} (副本)`,
            status: 'planned' as BusinessEventStatus,
            participants: [], // 清空参与者
            tasks: [], // 清空任务
            followUps: [], // 清空跟进
            relationshipsEstablished: 0,
            relationshipsStrengthened: 0,
            effectivenessScore: undefined,
          };
          
          await get().addEvent(duplicatedEvent);
        }
      },

      // 活动任务管理
      addEventTask: async (eventId, taskData) => {
        const newTask: EventTask = {
          ...taskData,
          id: generateId(),
          isCompleted: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          events: state.events.map((event) =>
            event.id === eventId
              ? { ...event, tasks: [...event.tasks, newTask], updatedAt: new Date() }
              : event
          ),
        }));
      },

      updateEventTask: async (eventId, taskId, updates) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === eventId
              ? {
                  ...event,
                  tasks: event.tasks.map((task) =>
                    task.id === taskId
                      ? { ...task, ...updates, updatedAt: new Date() }
                      : task
                  ),
                  updatedAt: new Date(),
                }
              : event
          ),
        }));
      },

      deleteEventTask: async (eventId, taskId) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === eventId
              ? {
                  ...event,
                  tasks: event.tasks.filter((task) => task.id !== taskId),
                  updatedAt: new Date(),
                }
              : event
          ),
        }));
      },

      toggleTaskCompletion: async (eventId, taskId) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === eventId
              ? {
                  ...event,
                  tasks: event.tasks.map((task) =>
                    task.id === taskId
                      ? { 
                          ...task, 
                          isCompleted: !task.isCompleted,
                          completedAt: !task.isCompleted ? new Date() : undefined,
                          updatedAt: new Date() 
                        }
                      : task
                  ),
                  updatedAt: new Date(),
                }
              : event
          ),
        }));
      },

      // 参与者管理（增强版）
      addParticipant: async (eventId, participant) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === eventId && !event.participants.find(p => p.contactId === participant.contactId)
              ? {
                  ...event,
                  participants: [...event.participants, participant],
                  updatedAt: new Date(),
                }
              : event
          ),
        }));
      },

      updateParticipant: async (eventId, contactId, updates) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === eventId
              ? {
                  ...event,
                  participants: event.participants.map((participant) =>
                    participant.contactId === contactId
                      ? { ...participant, ...updates }
                      : participant
                  ),
                  updatedAt: new Date(),
                }
              : event
          ),
        }));
      },

      removeParticipant: async (eventId, contactId) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === eventId
              ? {
                  ...event,
                  participants: event.participants.filter((p) => p.contactId !== contactId),
                  updatedAt: new Date(),
                }
              : event
          ),
        }));
      },

      markParticipantAttended: async (eventId, contactId) => {
        await get().updateParticipant(eventId, contactId, { status: 'attended' });
      },

      // 跟进管理
      addFollowUp: async (eventId, followUpData) => {
        const newFollowUp: EventFollowUp = {
          ...followUpData,
          id: generateId(),
          isCompleted: false,
          createdAt: new Date(),
        };

        set((state) => ({
          events: state.events.map((event) =>
            event.id === eventId
              ? { ...event, followUps: [...event.followUps, newFollowUp], updatedAt: new Date() }
              : event
          ),
        }));
      },

      updateFollowUp: async (eventId, followUpId, updates) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === eventId
              ? {
                  ...event,
                  followUps: event.followUps.map((followUp) =>
                    followUp.id === followUpId
                      ? { ...followUp, ...updates }
                      : followUp
                  ),
                  updatedAt: new Date(),
                }
              : event
          ),
        }));
      },

      completeFollowUp: async (eventId, followUpId) => {
        await get().updateFollowUp(eventId, followUpId, { 
          isCompleted: true, 
          completedAt: new Date() 
        });
      },

      // 活动状态管理
      confirmEvent: async (eventId) => {
        await get().updateEvent(eventId, { status: 'confirmed' });
      },

      startEvent: async (eventId) => {
        await get().updateEvent(eventId, { status: 'in-progress' });
      },

      completeEvent: async (eventId, effectivenessScore) => {
        const event = get().getEventById(eventId);

        await get().updateEvent(eventId, {
          status: 'completed',
          effectivenessScore
        });

        // 为需要跟进的参与者创建提醒
        if (event) {
          const participantsNeedingFollowUp = event.participants
            .filter(p => p.followUpRequired)
            .map(p => p.contactId);

          if (participantsNeedingFollowUp.length > 0) {
            try {
              const { createFollowUpReminders } = await import('./reminderStore');
              const reminderStore = createFollowUpReminders;
              if (reminderStore) {
                await reminderStore(eventId, participantsNeedingFollowUp, event.title);
              }
            } catch (error) {
              console.warn('Failed to create follow-up reminders:', error);
            }
          }
        }
      },

      cancelEvent: async (eventId, reason) => {
        await get().updateEvent(eventId, { 
          status: 'cancelled',
          notes: reason ? `${get().getEventById(eventId)?.notes || ''}\n取消原因: ${reason}` : undefined
        });
      },

      postponeEvent: async (eventId, newDate) => {
        await get().updateEvent(eventId, { 
          status: 'postponed',
          date: newDate,
          startTime: newDate
        });
      },

      archiveEvent: async (eventId) => {
        await get().updateEvent(eventId, { isArchived: true });
      },

      // 关系建立分析
      analyzeEventRelationships: async (eventId) => {
        // TODO: 实现关系分析逻辑
        console.log('Analyzing relationships for event:', eventId);
      },

      updateRelationshipStats: async (eventId, established, strengthened) => {
        await get().updateEvent(eventId, {
          relationshipsEstablished: established,
          relationshipsStrengthened: strengthened,
        });
      },

      // 搜索和过滤
      setSearchQuery: (query) => set({ searchQuery: query }),
      setStatusFilter: (status) => set({ statusFilter: status }),
      setTypeFilter: (type) => set({ typeFilter: type }),
      setDateFilter: (filter) => set({ dateFilter: filter }),
      setScaleFilter: (scale) => set({ scaleFilter: scale }),
      clearFilters: () => set({
        searchQuery: '',
        statusFilter: 'all',
        typeFilter: 'all',
        dateFilter: 'all',
        scaleFilter: 'all',
      }),

      // 选择和导航
      setSelectedEvent: (event) => set({ selectedEvent: event }),

      // 工具方法
      getEventById: (id) => {
        return get().events.find((event) => event.id === id);
      },

      getEventsByDate: (date) => {
        const targetDate = date.toDateString();
        return get().events.filter((event) => 
          event.date.toDateString() === targetDate
        );
      },

      getEventsByType: (type) => {
        return get().events.filter((event) => event.eventType === type);
      },

      getUpcomingEvents: () => {
        const now = new Date();
        return get().events
          .filter((event) => 
            event.date > now && 
            ['planned', 'confirmed'].includes(event.status) &&
            !event.isArchived
          )
          .sort((a, b) => a.date.getTime() - b.date.getTime());
      },

      getPastEvents: () => {
        const now = new Date();
        return get().events
          .filter((event) => 
            event.date < now || 
            ['completed', 'cancelled'].includes(event.status)
          )
          .sort((a, b) => b.date.getTime() - a.date.getTime());
      },

      getEventsByContact: (contactId) => {
        return get().events.filter((event) =>
          event.participants.some((p) => p.contactId === contactId)
        );
      },

      // 统计分析
      getEventStats: () => {
        const events = get().events;
        const stats = {
          total: events.length,
          byType: {} as Record<BusinessEventType, number>,
          byStatus: {} as Record<BusinessEventStatus, number>,
          totalRelationshipsEstablished: 0,
          averageEffectiveness: 0,
        };

        events.forEach((event) => {
          // 按类型统计
          stats.byType[event.eventType] = (stats.byType[event.eventType] || 0) + 1;
          
          // 按状态统计
          stats.byStatus[event.status] = (stats.byStatus[event.status] || 0) + 1;
          
          // 关系建立统计
          stats.totalRelationshipsEstablished += event.relationshipsEstablished;
        });

        // 计算平均效果
        const completedEvents = events.filter(e => e.status === 'completed' && e.effectivenessScore);
        if (completedEvents.length > 0) {
          stats.averageEffectiveness = completedEvents.reduce((sum, e) => sum + (e.effectivenessScore || 0), 0) / completedEvents.length;
        }

        return stats;
      },

      // 状态管理
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),

      // 数据管理
      importEvents: async (events) => {
        set((state) => ({
          events: [...state.events, ...events],
        }));
      },

      exportEvents: async () => {
        return get().events;
      },

      clearAllEvents: () => {
        set({
          events: [],
          selectedEvent: null,
          error: null,
        });
      },

      initializeSampleData: async () => {
        const existingEvents = get().events;
        if (existingEvents.length > 0) {
          return; // 已有数据，不重复初始化
        }

        const sampleEvents: Omit<BusinessEvent, 'id' | 'createdAt' | 'updatedAt'>[] = [
          {
            title: '2024春季科技展览会',
            description: '年度最大的科技产品展示会，汇聚全球顶尖科技公司',
            date: new Date(2024, 3, 15, 9, 0), // 2024年4月15日 9:00
            startTime: new Date(2024, 3, 15, 9, 0),
            endTime: new Date(2024, 3, 15, 18, 0),
            location: '上海国际会展中心',
            isVirtual: false,
            eventType: 'exhibition',
            category: '科技',
            tags: ['科技', '展览', '产品发布', 'AI', '5G'],
            scale: 'massive',
            participants: [
              {
                contactId: 'contact_001',
                name: '张伟',
                status: 'attended',
                relationshipContext: 'first_met',
                interactionLevel: 'extensive',
                businessCardExchanged: true,
                followUpRequired: true,
              },
              {
                contactId: 'contact_002',
                name: '李明',
                status: 'attended',
                relationshipContext: 'reconnected',
                interactionLevel: 'moderate',
                businessCardExchanged: false,
                followUpRequired: false,
              }
            ],
            organizerId: 'current-user',
            agenda: '上午：主题演讲\n下午：产品展示\n晚上：网络交流',
            objectives: ['了解最新科技趋势', '寻找合作伙伴', '展示公司产品'],
            notes: '非常成功的展会，认识了很多潜在客户和合作伙伴',
            tasks: [],
            followUps: [],
            status: 'completed',
            isArchived: false,
            relationshipsEstablished: 5,
            relationshipsStrengthened: 3,
            effectivenessScore: 9,
          },
          {
            title: '人工智能研讨会',
            description: 'AI在企业应用中的最新发展和趋势讨论',
            date: new Date(2024, 2, 20, 14, 0), // 2024年3月20日 14:00
            startTime: new Date(2024, 2, 20, 14, 0),
            endTime: new Date(2024, 2, 20, 17, 0),
            location: 'https://zoom.us/j/123456789',
            isVirtual: true,
            eventLink: 'https://zoom.us/j/123456789',
            eventType: 'seminar',
            category: '人工智能',
            tags: ['AI', '机器学习', '企业应用'],
            scale: 'medium',
            participants: [
              {
                contactId: 'contact_003',
                name: '王芳',
                status: 'attended',
                relationshipContext: 'existing',
                interactionLevel: 'moderate',
                businessCardExchanged: false,
                followUpRequired: true,
              }
            ],
            organizerId: 'current-user',
            agenda: '14:00-15:00 AI发展现状\n15:00-16:00 企业应用案例\n16:00-17:00 Q&A讨论',
            objectives: ['学习AI最新技术', '了解行业应用', '建立专家网络'],
            notes: '获得了很多有价值的见解，特别是在AI伦理方面',
            tasks: [],
            followUps: [],
            status: 'completed',
            isArchived: false,
            relationshipsEstablished: 2,
            relationshipsStrengthened: 1,
            effectivenessScore: 8,
          },
          {
            title: '客户拜访 - ABC公司',
            description: '与ABC公司讨论新项目合作机会',
            date: new Date(2024, 5, 10, 10, 0), // 2024年6月10日 10:00
            startTime: new Date(2024, 5, 10, 10, 0),
            endTime: new Date(2024, 5, 10, 12, 0),
            location: 'ABC公司总部',
            isVirtual: false,
            eventType: 'client_visit',
            category: '商务拜访',
            tags: ['客户', '合作', '项目'],
            scale: 'small',
            participants: [
              {
                contactId: 'contact_004',
                name: '陈总',
                status: 'confirmed',
                relationshipContext: 'existing',
                interactionLevel: 'extensive',
                businessCardExchanged: false,
                followUpRequired: true,
              }
            ],
            organizerId: 'current-user',
            agenda: '项目需求讨论\n技术方案介绍\n合作条件谈判',
            objectives: ['确定项目需求', '展示技术能力', '达成初步合作意向'],
            notes: '',
            tasks: [
              {
                title: '准备技术方案PPT',
                description: '包含详细的技术架构和实施计划',
                priority: 'high',
                isCompleted: false,
              },
              {
                title: '准备报价方案',
                description: '根据需求制定详细报价',
                priority: 'high',
                isCompleted: false,
              }
            ],
            followUps: [],
            status: 'confirmed',
            isArchived: false,
            relationshipsEstablished: 0,
            relationshipsStrengthened: 0,
          }
        ];

        // 添加示例事件
        for (const eventData of sampleEvents) {
          await get().addEvent(eventData);
        }
      },
    }),
    {
      name: 'business-events-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        events: state.events,
      }),
    }
  )
);

// 导出选择器
export const useBusinessEventSelectors = {
  useEvents: () => useBusinessEventStore((state) => state.events),
  useFilteredEvents: () => {
    const { events, searchQuery, statusFilter, typeFilter, dateFilter, scaleFilter } = useBusinessEventStore();
    return filterEvents(events, { searchQuery, statusFilter, typeFilter, dateFilter, scaleFilter });
  },
  useSelectedEvent: () => useBusinessEventStore((state) => state.selectedEvent),
  useUpcomingEvents: () => useBusinessEventStore((state) => state.getUpcomingEvents()),
  usePastEvents: () => useBusinessEventStore((state) => state.getPastEvents()),
  useEventStats: () => useBusinessEventStore((state) => state.getEventStats()),
  useIsLoading: () => useBusinessEventStore((state) => state.isLoading),
  useError: () => useBusinessEventStore((state) => state.error),
  useFilters: () => useBusinessEventStore((state) => ({
    searchQuery: state.searchQuery,
    statusFilter: state.statusFilter,
    typeFilter: state.typeFilter,
    dateFilter: state.dateFilter,
    scaleFilter: state.scaleFilter,
  })),
};

// 向后兼容的别名
export const useMeetingsStore = useBusinessEventStore;
export const useMeetingsSelectors = useBusinessEventSelectors;
