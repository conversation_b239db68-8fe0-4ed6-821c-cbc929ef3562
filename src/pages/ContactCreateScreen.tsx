import React, { useState, useEffect, useRef } from 'react'; // useRef for initialRef
import {
  ScrollView,
  KeyboardAvoidingView,
  Box,
  VStack,
  HStack,
  Avatar,
  AvatarFallbackText,
  AvatarImage,
  Pressable,
  Icon,
  Text,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  Textarea,
  TextareaInput,
  Button,
  ButtonText,
  ButtonIcon,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
  Heading
} from '@gluestack-ui/themed';
import { Platform } from 'react-native';
import { useNavigation, NavigationProp, RouteProp } from '@react-navigation/native';
import { Mail, Phone, MapPin, X, User as UserIcon, Edit3, PlusIcon, Save as SaveIcon, Check } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
//  // Will replace with Gluestack styling if possible
import { Tag, Contact, TagCategory, RootStackParamList } from '../types'; // Import global Tag and Contact


// Define types for tags and component props
// Removed local TagCategory, using imported one.

type ContactCreateScreenProps = {
  route: RouteProp<RootStackParamList, 'ContactCreate'>;
};

const ContactCreateScreen = ({ route }: ContactCreateScreenProps) => {
  const toast = useToast();

  const initialRef = useRef(null); // For modal input focus
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  // Check if we're editing an existing contact
  const isEditing = route?.params?.contact !== undefined;
  const existingContact = route?.params?.contact || null;
  
  // Form state
  const [contactPhoto, setContactPhoto] = useState<string | null>(
    isEditing ? existingContact?.photo || null : null
  );
  const [firstName, setFirstName] = useState(
    isEditing ? (existingContact?.firstName || '') : ''
  );
  const [lastName, setLastName] = useState(
    isEditing ? (existingContact?.lastName || '') : ''
  );
  const [company, setCompany] = useState(
    isEditing ? (existingContact?.company || '') : ''
  );
  const [position, setPosition] = useState(
    isEditing ? (existingContact?.position || '') : ''
  );
  const [email, setEmail] = useState(
    isEditing ? (existingContact?.email || '') : ''
  );
  const [phone, setPhone] = useState(
    isEditing ? (existingContact?.phone || '') : ''
  );
  const [location, setLocation] = useState(
    isEditing ? (existingContact?.location || '') : ''
  );
  const [notes, setNotes] = useState(isEditing ? (existingContact?.notes || '') : '');
  const [selectedTags, setSelectedTags] = useState<Tag[]>(isEditing ? existingContact?.tags || [] : []);

  // Input validation states
  const [firstNameIsInvalid, setFirstNameIsInvalid] = useState(false);
  const [lastNameIsInvalid, setLastNameIsInvalid] = useState(false);
  const [emailIsInvalid, setEmailIsInvalid] = useState(false);

  useEffect(() => {
    navigation.setOptions({
      title: isEditing ? 'Edit Contact' : 'Create Contact',
      // You could further customize headerLeft/headerRight here if needed,
      // e.g., to use Gluestack Buttons directly in the navigation header.
      // For now, focusing on the title as per typical navigation patterns.
    });
  }, [navigation, isEditing]);

  const pickImageAsync = async () => {
    // Request media library permissions
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (permissionResult.granted === false) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>Permission required</ToastTitle>
            <ToastDescription>
              Permission to access photo library is required to choose an image.
            </ToastDescription>
          </Toast>
        ),
      });
      return;
    }

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) { // result is ImagePickerSuccessResult
      if (result.assets && result.assets.length > 0 && result.assets[0].uri) {
        setContactPhoto(result.assets[0].uri);
      } else {
        // Optional: Log or toast if assets are unexpectedly empty after a successful pick
        console.log('Image picked, but assets array was empty or URI was missing on the first asset.');
        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="error" variant="accent">
              <ToastTitle>Image Error</ToastTitle>
              <ToastDescription>Could not retrieve the selected image. Please try again.</ToastDescription>
            </Toast>
          ),
        });
      }
    } else {
      // User cancelled, no toast needed unless specifically requested for this case.
    }
  };

  // Available tag categories with sample tags
  const tagCategories: Record<TagCategory, Tag[]> = {
    industry: [
      { id: 'ind1', name: 'Technology', color: '#3498db' },
      { id: 'ind2', name: 'Finance', color: '#2ecc71' },
      { id: 'ind3', name: 'Healthcare', color: '#e74c3c' },
      { id: 'ind4', name: 'Education', color: '#f39c12' },
      { id: 'ind5', name: 'Manufacturing', color: '#9b59b6' },
    ],
    relationship: [
      { id: 'rel1', name: 'Client', color: '#1abc9c' },
      { id: 'rel2', name: 'Partner', color: '#d35400' },
      { id: 'rel3', name: 'Vendor', color: '#8e44ad' },
      { id: 'rel4', name: 'Colleague', color: '#27ae60' },
      { id: 'rel5', name: 'Prospect', color: '#c0392b' },
    ],
    personal: [
      { id: 'per1', name: 'Golf', color: '#16a085' },
      { id: 'per2', name: 'Coffee', color: '#7f8c8d' },
      { id: 'per3', name: 'Travel', color: '#f1c40f' },
      { id: 'per4', name: 'Art', color: '#2980b9' },
      { id: 'per5', name: 'Wine', color: '#c0392b' },
    ]
  };
  
  // Current active tab for tag selection
  const [activeTagCategory, setActiveTagCategory] = useState<TagCategory>('industry');

  const handleSaveNewTag = () => {
    if (!newTagName.trim()) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={`toast-${id}`} action="error" variant="solid">
            <ToastTitle>Invalid Tag Name</ToastTitle>
            <ToastDescription>Tag name cannot be empty.</ToastDescription>
          </Toast>
        ),
      });
      return;
    }
    // In a real app, you would add the new tag to your state/backend
    console.log('New Tag to save:', { name: newTagName /*, color: newTagColor */ });
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={`toast-${id}`} action="success" variant="solid">
          <ToastTitle>Tag Created (Simulated)</ToastTitle>
          <ToastDescription>{newTagName} has been added.</ToastDescription>
        </Toast>
      ),
    });
    setNewTagName('');
    setShowAddTagModal(false);
  };

  // State for Add New Tag Modal
  const [showAddTagModal, setShowAddTagModal] = useState(false);
  const [newTagName, setNewTagName] = useState('');

  // Function to pick an image from gallery
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });
  
      if (!result.canceled && result.assets && result.assets[0].uri) {
        setContactPhoto(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>Image Error</ToastTitle>
            <ToastDescription>
              Failed to select image. Please try again.
            </ToastDescription>
          </Toast>
        ),
      });
    }
  };

  // Function to toggle a tag selection
  const handleToggleTag = (tag: Tag) => {
    if (selectedTags.some((t: Tag) => t.id === tag.id)) {
      setSelectedTags(selectedTags.filter((t: Tag) => t.id !== tag.id));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  // Function to save the contact
  const saveContact = () => {
    // Validate required fields
    if (!firstName!.trim() || !lastName!.trim()) {
      if (!firstName!.trim()) setFirstNameIsInvalid(true);
      if (!lastName!.trim()) setLastNameIsInvalid(true);
      toast.show({
        placement: "top",
        render: ({ id }) => {
          return (
            <Toast nativeID={'toast-' + id} action="error" variant="solid">
              <VStack space="xs">
                <ToastTitle>Missing Information</ToastTitle>
                <ToastDescription>
                  Please fill in first and last name.
                </ToastDescription>
              </VStack>
            </Toast>
          );
        }
      });
      return;
    }

    // Email validation (if email is provided)
    if (email!.trim()) {
      const emailRegex = /\S+@\S+\.\S+/;
      if (!emailRegex.test(email!.trim())) {
        setEmailIsInvalid(true);
        toast.show({
          placement: "top",
          render: ({ id }) => {
            return (
              <Toast nativeID={'toast-' + id} action="error" variant="solid">
                <VStack space="xs">
                  <ToastTitle>Invalid Email</ToastTitle>
                  <ToastDescription>
                    Please enter a valid email address.
                  </ToastDescription>
                </VStack>
              </Toast>
            );
          }
        });
        return;
      } // Closes: if (!emailRegex.test(email!.trim()))
    } // Closes: if (email!.trim())
    
    const contactData = {
      photo: contactPhoto || undefined,
      firstName: firstName!.trim(),
      lastName: lastName!.trim(),
      company: company!.trim() || undefined,
      position: position!.trim() || undefined,
      email: email!.trim() || undefined,
      phone: phone!.trim() || undefined,
      location: location!.trim() || undefined,
      notes: notes!.trim() || undefined,
      tags: selectedTags,
    };

    let finalContact: Contact;

    if (isEditing && existingContact) {
      finalContact = {
        ...existingContact,
        ...contactData,
        name: `${contactData.firstName} ${contactData.lastName}`.trim(),
        avatar: contactData.photo || existingContact.avatar || '', 
      };
    } else {
      const newId = Date.now().toString();
      finalContact = {
        id: newId,
        name: `${contactData.firstName} ${contactData.lastName}`.trim(),
        avatar: contactData.photo || '',
        photo: contactData.photo,
        firstName: contactData.firstName,
        lastName: contactData.lastName,
        company: contactData.company,
        position: contactData.position,
        email: contactData.email,
        phone: contactData.phone,
        location: contactData.location,
        notes: contactData.notes,
        tags: contactData.tags,
        role: contactData.position, 
        address: contactData.location, 
        birthday: existingContact?.birthday || undefined, 
      };
    } // This closes the 'else' block properly

  toast.show({
    placement: "top",
    render: ({ id }) => (
      <Toast nativeID={id} action={isEditing ? "info" : "success"} variant="accent">
        <ToastTitle>{isEditing ? "Contact Updated" : "Contact Saved"}</ToastTitle>
        <ToastDescription>
          {firstName} {lastName} has been successfully {isEditing ? "updated" : "saved"}.
        </ToastDescription>
      </Toast>
    ),
  });

    // Navigate back with the contact data
    if (isEditing) {
      navigation.navigate('ContactDetail', { contact: finalContact, isEditedJustNow: true });
    } else {
      navigation.navigate('Contacts', { newContact: finalContact });
    }
  };

return (
  <KeyboardAvoidingView
    flex={1}
    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    sx={{ _light: { bg: '$backgroundLight50' }, _dark: { bg: '$backgroundDark900' } }}
  >
    {/* Screen Header */}
    <Box 
      flexDirection="row" 
      alignItems="center" 
      p="$4" 
      borderBottomWidth={1} 
      sx={{ 
        _light: { borderBottomColor: '$borderLight200', bg: '$primary500' }, 
        _dark: { borderBottomColor: '$borderDark700', bg: '$primary700' } 
      }}
    >
      <Pressable onPress={() => navigation.goBack()} mr="$3">
        <Icon as={X} size="xl" color="$textLight50" />
      </Pressable>
      <Heading size="lg" color="$textLight50" mt={Platform.OS === 'ios' ? 0 : "$0"} mb="$0">
        {isEditing ? 'Edit Contact' : 'Create New Contact'}
      </Heading>
    </Box>

    <ScrollView contentContainerStyle={{ flexGrow: 1 }} bounces={false}>
      <VStack space="lg" p="$4">
        {/* Contact Photo Section */}
        <VStack space="md" alignItems="center" mt="$2" mb="$2">
          <Heading size="sm" mb="$2">Contact Photo</Heading>
          <Pressable onPress={pickImageAsync} position="relative"> 
            <Avatar size="2xl" bg="$primary300">
              {contactPhoto ? (
                <AvatarImage source={{ uri: contactPhoto }} alt={`${firstName} ${lastName} photo`} />
              ) : (
                <AvatarFallbackText>
                  {(`${firstName?.[0] || ''}${lastName?.[0] || ''}`).toUpperCase() || (
                    <Icon as={UserIcon} size="xl" color="$primary600" />
                  )}
                </AvatarFallbackText>
              )}
            </Avatar>
            <Box
              position="absolute"
              bottom={0}
              right={0}
              bg="$primary500"
              p="$1.5"
              borderRadius="$full"
              borderWidth={2}
              borderColor="$backgroundLight0"
              sx={{ _dark: { borderColor: '$backgroundDark900' } }}
            >
              <Icon as={contactPhoto ? Edit3 : PlusIcon} size="sm" color="$white" />
            </Box>
          </Pressable>
        </VStack>

        {/* Basic Information Section */}
        <VStack space="md">
          <Heading size="md" mb="$2">Basic Information</Heading>
          {/* Name Inputs */}
          <HStack space="md" width="$full">
            <FormControl flex={1} isInvalid={firstNameIsInvalid}>
              <FormControlLabel mb="$1">
                <FormControlLabelText color="$textLight500">First Name</FormControlLabelText>
              </FormControlLabel>
              <Input variant="outline" size="lg">
                <InputField
                  type="text"
                  placeholder="Enter first name"
                  value={firstName}
                  onChangeText={(text) => {
                  setFirstName(text);
                  if (firstNameIsInvalid) setFirstNameIsInvalid(false);
                }}
                />
              </Input>
            </FormControl>
            <FormControl flex={1} isInvalid={lastNameIsInvalid}>
              <FormControlLabel mb="$1">
                <FormControlLabelText color="$textLight500">Last Name</FormControlLabelText>
              </FormControlLabel>
              <Input variant="outline" size="lg">
                <InputField
                  type="text"
                  placeholder="Enter last name"
                  value={lastName}
                  onChangeText={(text) => {
                  setLastName(text);
                  if (lastNameIsInvalid) setLastNameIsInvalid(false);
                }}
                />
              </Input>
            </FormControl>
          </HStack>

          <FormControl>
            <FormControlLabel mb="$1">
              <FormControlLabelText color="$textLight500">Company</FormControlLabelText>
            </FormControlLabel>
            <Input variant="outline" size="lg">
              <InputField
                value={company}
                onChangeText={setCompany}
                placeholder="Company Name"
              />
            </Input>
          </FormControl>

          <FormControl>
            <FormControlLabel mb="$1">
              <FormControlLabelText color="$textLight500">Position</FormControlLabelText>
            </FormControlLabel>
            <Input variant="outline" size="lg">
              <InputField
                value={position}
                onChangeText={setPosition}
                placeholder="Job Title"
              />
            </Input>
          </FormControl>
        </VStack>

        {/* Contact Details Section */}
        <VStack space="md" mt="$4">
          <Heading size="md" mb="$2">Contact Details</Heading>
          <FormControl isInvalid={emailIsInvalid}>
            <FormControlLabel mb="$1">
              <FormControlLabelText color="$textLight500">Email</FormControlLabelText>
            </FormControlLabel>
            <Input variant="outline" size="lg">
              <InputSlot pl="$3">
                <InputIcon as={Mail} />
              </InputSlot>
              <InputField
                value={email}
                onChangeText={(text) => {
                setEmail(text);
                if (emailIsInvalid) setEmailIsInvalid(false);
              }}
                placeholder="Email Address"
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </Input>
          </FormControl>

          <FormControl>
            <FormControlLabel mb="$1">
              <FormControlLabelText color="$textLight500">Phone</FormControlLabelText>
            </FormControlLabel>
            <Input variant="outline" size="lg">
              <InputSlot pl="$3">
                <InputIcon as={Phone} />
              </InputSlot>
              <InputField
                value={phone}
                onChangeText={setPhone}
                placeholder="Phone Number"
                keyboardType="phone-pad"
              />
            </Input>
          </FormControl>

          <FormControl>
            <FormControlLabel mb="$1">
              <FormControlLabelText color="$textLight500">Location</FormControlLabelText>
            </FormControlLabel>
            <Input variant="outline" size="lg">
              <InputSlot pl="$3">
                <InputIcon as={MapPin} />
              </InputSlot>
              <InputField
                value={location}
                onChangeText={setLocation}
                placeholder="Location (e.g., City, State)"
              />
            </Input>
          </FormControl>
        </VStack>

        {/* Tags Section */}
        <VStack space="md" mt="$4">
          <Heading size="md" mb="$2">Tags</Heading>
          <HStack space="sm" justifyContent="center" mb="$3">
            {(Object.keys(tagCategories) as Array<keyof typeof tagCategories>).map((category) => (
              <Button
                key={category}
                variant={activeTagCategory === category ? 'solid' : 'outline'}
                size="sm"
                onPress={() => setActiveTagCategory(category)}
                sx={{
                  borderColor: activeTagCategory === category ? '$primary500' : '$borderLight400',
                  bg: activeTagCategory === category ? '$primary500' : 'transparent',
                  _text: {
                    color: activeTagCategory === category ? '$white' : '$primary500',
                  },
                  _dark: {
                    borderColor: activeTagCategory === category ? '$primary400' : '$borderDark600',
                    bg: activeTagCategory === category ? '$primary400' : 'transparent',
                    _text: {
                      color: activeTagCategory === category ? '$textDark900' : '$primary400',
                    },
                  },
                }}
              >
                <ButtonText>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </ButtonText>
              </Button>
            ))}
          </HStack>

          <Box flexDirection="row" flexWrap="wrap" gap="$2">
            {tagCategories[activeTagCategory].map((tag) => {
              const isSelected = selectedTags.find((t) => t.id === tag.id);
              return (
                <Pressable
                  key={tag.id}
                  onPress={() => handleToggleTag(tag)}
                  sx={{
                    py: '$1.5',
                    px: '$3',
                    borderRadius: '$full',
                    borderWidth: 1,
                    _light: {
                      borderColor: isSelected ? '$primary500' : '$borderLight400',
                      bg: isSelected ? '$primary100' : '$backgroundLight50',
                    },
                    _dark: {
                      borderColor: isSelected ? '$primary400' : '$borderDark600',
                      bg: isSelected ? '$primary800' : '$backgroundDark900',
                    },
                  }}
                >
                  <Text
                    size="sm"
                    sx={{
                      _light: { color: isSelected ? '$primary700' : '$textLight700' },
                      _dark: { color: isSelected ? '$primary200' : '$textDark300' },
                    }}
                  >
                    {tag.name}
                  </Text>
                </Pressable>
              );
            })}
          </Box>
          {/* New Tag Button */}
          <Button 
            variant="outline" 
            action="secondary" 
            size="sm" 
            onPress={() => setShowAddTagModal(true)}
            mt="$2"
            alignSelf="flex-start"
          >
            <ButtonIcon as={PlusIcon} mr="$1" />
            <ButtonText>New Tag</ButtonText>
          </Button>
        </VStack>

        {/* Notes Section */}
        <VStack space="md" mt="$4">
          <Heading size="md" mb="$2">Notes</Heading>
          <Textarea size="lg" isReadOnly={false} isInvalid={false} isDisabled={false} sx={{ w: '$full' }}>
            <TextareaInput
              value={notes}
              onChangeText={setNotes}
              placeholder="Add notes about this contact..."
              multiline
              role={undefined}
            />
          </Textarea>
        </VStack>

        {/* Bottom Actions */}
        <HStack space="md" p="$4" justifyContent="space-between" mt="$auto">
          <Button variant="link" action="secondary" onPress={() => navigation.goBack()} flex={1} mr="$2">
            <ButtonText>Cancel</ButtonText>
          </Button>

          <Button action="primary" onPress={saveContact} flex={1} ml="$2">
            <ButtonIcon as={SaveIcon} mr="$2" />
            <ButtonText>{isEditing ? 'Update Contact' : 'Save New Contact'}</ButtonText>
          </Button>
        </HStack>

        {/* Extra space at the bottom for better scrolling */}
        <Box sx={{ h: '$16' }} />
      </VStack>
    </ScrollView>

    {/* Add New Tag Modal */}
    <Modal
      isOpen={showAddTagModal}
        onClose={() => {
          setShowAddTagModal(false);
          setNewTagName(''); // Reset on close
        }}
        initialFocusRef={initialRef}
    >
      <ModalBackdrop />
      <ModalContent>
        <ModalHeader>
          <Heading size="lg">Create New Tag</Heading>
          <ModalCloseButton>
            <Icon as={X} />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody>
          <FormControl isRequired mt="$2"> {/* Added mt for spacing */}
            <FormControlLabel mb="$1">
              <FormControlLabelText>Tag Name</FormControlLabelText>
            </FormControlLabel>
            <Input ref={initialRef}>
              <InputField
                placeholder="Enter tag name"
                value={newTagName}
                onChangeText={setNewTagName}
                onSubmitEditing={handleSaveNewTag} // Allow saving with return key
              />
            </Input>
            {/* Optional: Color Picker could go here */}
          </FormControl>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="outline"
            size="sm"
            action="secondary"
            mr="$3"
            onPress={() => {
              setShowAddTagModal(false);
              setNewTagName('');
            }}
          >
            <ButtonText>Cancel</ButtonText>
          </Button>
          <Button
            size="sm"
            action="primary"
            onPress={handleSaveNewTag}
          >
            <ButtonIcon as={Check} mr="$1" />
            <ButtonText>Save Tag</ButtonText>
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </KeyboardAvoidingView>
);
};

export default ContactCreateScreen;