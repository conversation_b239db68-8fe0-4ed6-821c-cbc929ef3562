import React, { useMemo } from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Box,
  Text,
  Heading,
  VStack,
  HStack,
  Button,
  ButtonText,
  ButtonIcon,
  Pressable,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  Image,
  ScrollView,
  Link,
  LinkText,
  Icon,
  StatusBar
} from '@gluestack-ui/themed';
import { Bell, Plus, Users, Calendar, Map, Settings, User, Briefcase, Clock, ChevronRight, Zap } from 'lucide-react-native';
import { Platform } from 'react-native';
import { RootStackParamList } from '../types';

// Import global state and hooks
import { useContactsStore, useSettingsSelectors, useAppSelectors, useReminderSelectors, useBusinessEventStore } from '../store';
import { usePerformanceMonitor } from '../hooks';
import { ErrorBoundary, CircularButton, StatsCardSkeleton, ContactCardSkeleton } from '../components/ui';
import { EventStatsDashboard } from '../components/visualization/EventStatsDashboard';

// This will be calculated from real data
const getStatsData = (contacts: any[], reminderStats: any) => {
  const now = new Date();
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  const newThisWeek = contacts.filter(contact =>
    contact.createdAt && new Date(contact.createdAt) > oneWeekAgo
  ).length;

  const favoriteContacts = contacts.filter(contact => contact.isFavorite).length;

  return [
    { label: '总联系人', value: contacts.length, colorToken: '$blue500' },
    { label: '本周新增', value: newThisWeek, colorToken: '$green500' },
    { label: '收藏联系人', value: favoriteContacts, colorToken: '$yellow500' },
    { label: '待处理提醒', value: reminderStats.pending || 0, colorToken: '$red500' },
  ];
};

// Get recent contacts from real data
const getRecentContacts = (contacts: any[]) => {
  return contacts
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 4)
    .map(contact => ({
      id: contact.id,
      name: contact.name,
      company: contact.company || '未知公司',
      avatarUrl: contact.avatar,
      lastContact: formatRelativeTime(contact.updatedAt)
    }));
};

// Helper function to format relative time
const formatRelativeTime = (date: Date) => {
  const now = new Date();
  const diffInMs = now.getTime() - new Date(date).getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) return '今天';
  if (diffInDays === 1) return '昨天';
  if (diffInDays < 7) return `${diffInDays}天前`;
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)}周前`;
  return `${Math.floor(diffInDays / 30)}月前`;
};

// Get upcoming reminders from real data
const getUpcomingReminders = (reminders: any[]) => {
  return reminders
    .filter(reminder => reminder.status === 'pending')
    .sort((a, b) => new Date(a.triggerDate).getTime() - new Date(b.triggerDate).getTime())
    .slice(0, 3)
    .map(reminder => ({
      id: reminder.id,
      title: reminder.title,
      time: formatReminderTime(reminder.triggerDate),
      type: reminder.type,
      colorToken: getReminderColorToken(reminder.type),
    }));
};

// Helper function to format reminder time
const formatReminderTime = (date: Date) => {
  const now = new Date();
  const reminderDate = new Date(date);
  const diffInMs = reminderDate.getTime() - now.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) return `今天 ${reminderDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
  if (diffInDays === 1) return `明天 ${reminderDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
  if (diffInDays < 7) return `${diffInDays}天后`;
  return reminderDate.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
};

// Helper function to get reminder color token
const getReminderColorToken = (type: string) => {
  switch (type) {
    case 'birthday': return '$pink500';
    case 'follow_up': return '$blue500';
    case 'meeting': return '$green500';
    case 'anniversary': return '$purple500';
    case 'task_deadline': return '$orange500';
    default: return '$gray500';
  }
};

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  // 使用静态时间，避免状态更新导致的无限循环
  const currentTime = useMemo(() => new Date(), []);

  // Performance monitoring
  usePerformanceMonitor('HomeScreen');

  // 临时注释掉所有store使用，逐步添加来找出问题
  // const { contacts, filteredContacts } = useContactsStore();
  // const { events } = useBusinessEventStore();
  // const userProfile = useSettingsSelectors.useUserProfile();
  // const { isLoading } = useAppSelectors.useLoadingStates();
  // const upcomingReminders = useReminderSelectors.useUpcomingReminders(7);
  // const reminderStats = useReminderSelectors.useReminderStats();

  // 使用静态数据进行测试
  const contacts = [];
  const events = [];
  const userProfile = { firstName: 'Test', lastName: 'User', name: 'Test User' };
  const isLoading = false;
  const upcomingReminders = [];
  const reminderStats = { total: 0, completed: 0, pending: 0 };

  // 移除定时器以避免无限循环问题
  // useEffect(() => {
  //   const timer = setInterval(() => {
  //     setCurrentTime(new Date());
  //   }, 60000);
  //   return () => clearInterval(timer);
  // }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return '早上好';
    if (hour < 18) return '下午好';
    return '晚上好';
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long',
    });
  };

  const quickActions = [
    { id: 1, title: '添加联系人', icon: Plus, colorToken: '$blue500', onPress: () => navigation.navigate('ContactCreate', {}) },
    { id: 2, title: '商务活动', icon: Calendar, colorToken: '$green500', onPress: () => navigation.navigate('BusinessEventRecord', {}) },
    { id: 3, title: '智能推荐', icon: Zap, colorToken: '$purple500', onPress: () => navigation.navigate('Recommendation') },
    { id: 4, title: '查看图谱', icon: Map, colorToken: '$yellow500', onPress: () => navigation.navigate('NetworkVisual') },
    { id: 5, title: '设置', icon: Settings, colorToken: '$trueGray500', onPress: () => navigation.navigate('Settings') },
  ];

  // Calculate dynamic data
  const statsData = getStatsData(contacts, reminderStats);
  const recentContacts = getRecentContacts(contacts);
  const displayReminders = getUpcomingReminders(upcomingReminders);

  return (
    <ErrorBoundary>
      <Box flex={1} bg="$backgroundLight50">
        <StatusBar
          barStyle={Platform.OS === 'ios' ? 'dark-content' : 'light-content'}
          backgroundColor="$backgroundLight50"
        />

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 16 }}
          pt={24}
          px={16}
        >
          <VStack space="lg">
            {/* 头部问候区域 */}
            <HStack justifyContent="space-between" alignItems="center">
              <VStack>
                <Heading size="lg" color="$textDark900">
                  {getGreeting()}, {userProfile.name.split(' ')[0]}
                </Heading>
                <Text size="sm" color="$textDark700">
                  {formatDate(currentTime)}
                </Text>
              </VStack>
              <Pressable onPress={() => navigation.navigate('Settings')} bg="$backgroundLight0" p={12} rounded="$full">
                <Icon as={Bell} size="md" color="$primary500" />
              </Pressable>
            </HStack>

          {/* 统计数据卡片 */}
          <HStack space="sm" justifyContent="space-between">
            {statsData.map((stat, index) => (
              <Pressable 
                key={index} 
                flex={1} 
                bg="$backgroundLight0" 
                rounded="$md" 
                p={16} 
                alignItems="center"
                sx={{
                  _dark: {
                    bg: '$backgroundDark900'
                  }
                }}
              >
                <Heading size="2xl" color={stat.colorToken}>
                  {stat.value}
                </Heading>
                <Text size="xs" color="$textDark700" textAlign="center" mt={4}>
                  {stat.label}
                </Text>
              </Pressable>
            ))}
          </HStack>

          {/* 快速操作按钮 */}
          <VStack space="md">
            <HStack justifyContent="space-around">
              {quickActions.slice(0, 3).map((action) => (
                <VStack key={action.id} alignItems="center" space="sm">
                  <CircularButton
                    icon={action.icon}
                    size="xl"
                    variant="outline"
                    action="primary"
                    onPress={action.onPress}
                  />
                  <Text size="xs" color="$textDark700" textAlign="center">
                    {action.title}
                  </Text>
                </VStack>
              ))}
            </HStack>
            <HStack justifyContent="space-around">
              {quickActions.slice(3).map((action) => (
                <VStack key={action.id} alignItems="center" space="sm">
                  <CircularButton
                    icon={action.icon}
                    size="xl"
                    variant="outline"
                    action="primary"
                    onPress={action.onPress}
                  />
                  <Text size="xs" color="$textDark700" textAlign="center">
                    {action.title}
                  </Text>
                </VStack>
              ))}
              {/* 占位符保持对称 */}
              <VStack alignItems="center" space="sm" opacity={0}>
                <CircularButton
                  icon={Settings}
                  size="xl"
                  variant="outline"
                  action="primary"
                  onPress={() => {}}
                />
                <Text size="xs" color="$textDark700" textAlign="center">
                  占位
                </Text>
              </VStack>
            </HStack>
          </VStack>

          {/* 最近联系人 */}
          <VStack space="md">
            <HStack justifyContent="space-between" alignItems="center">
              <Heading size="md" color="$textDark900">最近联系人</Heading>
              <Link onPress={() => { /* Navigate to all contacts */ }}>
                <LinkText size="sm" color="$primary500">查看全部</LinkText>
              </Link>
            </HStack>
            {isLoading ? (
              <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ gap: 16 }}>
                {Array.from({ length: 4 }).map((_, index) => (
                  <Box key={index} w={140}>
                    <ContactCardSkeleton animated={true} />
                  </Box>
                ))}
              </ScrollView>
            ) : (
              <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ gap: 16 }}>
                {recentContacts.map((contact) => (
                  <Pressable
                    key={contact.id}
                    bg="$backgroundLight0"
                    rounded="$md"
                    p={16}
                    w={140}
                    alignItems="center"
                    onPress={() => navigation.navigate('ContactDetail', { contactId: contact.id })}
                    sx={{
                      _dark: {
                        bg: '$backgroundDark900'
                      }
                    }}
                  >
                    <Avatar size="md" mb={8}>
                      {contact.avatarUrl ? (
                        <AvatarImage source={{ uri: contact.avatarUrl }} alt={contact.name} />
                      ) : (
                        <AvatarFallbackText>{contact.name.charAt(0)}</AvatarFallbackText>
                      )}
                    </Avatar>
                    <Text fontWeight="$bold" color="$textDark900" size="sm">
                      {contact.name}
                    </Text>
                    <Text size="xs" color="$textDark700">
                      {contact.company}
                    </Text>
                    <Text size="xs" color="$textDark500" mt={4}>
                      {contact.lastContact}
                    </Text>
                  </Pressable>
                ))}
              </ScrollView>
            )}
          </VStack>

          {/* 提醒事项 */}
          <VStack space="md">
            <HStack justifyContent="space-between" alignItems="center">
              <Heading size="md" color="$textDark900">提醒事项</Heading>
              <Link onPress={() => navigation.navigate('MainTabs', { screen: 'RemindersTab' })}>
                <LinkText size="sm" color="$primary500">查看全部</LinkText>
              </Link>
            </HStack>
            <VStack space="sm">
              {displayReminders.length > 0 ? (
                displayReminders.map((reminder) => (
                  <Pressable
                    key={reminder.id}
                    bg="$backgroundLight0"
                    rounded="$md"
                    p={16}
                    onPress={() => navigation.navigate('MainTabs', { screen: 'RemindersTab' })}
                    sx={{
                      _dark: {
                        bg: '$backgroundDark900'
                      }
                    }}
                  >
                    <HStack alignItems="center" space="md">
                      <Box bg={reminder.colorToken} rounded="$full" w={8} h={8} />
                      <VStack flex={1}>
                        <Text fontWeight="$medium" color="$textDark900" size="sm">
                          {reminder.title}
                        </Text>
                        <HStack alignItems="center" mt={4} space="xs">
                          <Icon as={Clock} size="xs" color="$textDark700" />
                          <Text size="xs" color="$textDark700">
                            {reminder.time}
                          </Text>
                        </HStack>
                      </VStack>
                      <Icon as={ChevronRight} size="md" color="$textDark500" />
                    </HStack>
                  </Pressable>
                ))
              ) : (
                <Pressable
                  bg="$backgroundLight0"
                  rounded="$md"
                  p={16}
                  alignItems="center"
                  onPress={() => navigation.navigate('MainTabs', { screen: 'RemindersTab' })}
                  sx={{
                    _dark: {
                      bg: '$backgroundDark900'
                    }
                  }}
                >
                  <Icon as={Bell} size="xl" color="$textDark400" />
                  <Text size="sm" color="$textDark600" mt={8} textAlign="center">
                    暂无待处理提醒
                  </Text>
                  <Text size="xs" color="$textDark500" mt={4} textAlign="center">
                    系统会自动为你生成生日和联系提醒
                  </Text>
                </Pressable>
              )}
            </VStack>
          </VStack>

          {/* 活动统计仪表板 */}
          {events.length > 0 && (
            <VStack space="md">
              <HStack justifyContent="space-between" alignItems="center">
                <Heading size="md" color="$textDark900">活动统计</Heading>
                <Link onPress={() => navigation.navigate('BusinessEventRecord', {})}>
                  <LinkText size="sm" color="$primary500">查看详情</LinkText>
                </Link>
              </HStack>
              <Box
                bg="$backgroundLight0"
                rounded="$lg"
                sx={{
                  _dark: {
                    bg: '$backgroundDark900'
                  }
                }}
              >
                <VStack space="md" p="$4">
                  <Text fontWeight="$bold" size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    活动统计概览
                  </Text>
                  <HStack space="md">
                    <Box
                      flex={1}
                      p="$4"
                      bg="$backgroundLight50"
                      borderRadius="$lg"
                      sx={{ _dark: { bg: '$backgroundDark800' } }}
                    >
                      <VStack space="sm">
                        <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                          总活动数
                        </Text>
                        <Text fontSize="$2xl" fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                          {events.length}
                        </Text>
                      </VStack>
                    </Box>
                    <Box
                      flex={1}
                      p="$4"
                      bg="$backgroundLight50"
                      borderRadius="$lg"
                      sx={{ _dark: { bg: '$backgroundDark800' } }}
                    >
                      <VStack space="sm">
                        <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                          即将到来
                        </Text>
                        <Text fontSize="$2xl" fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                          {events.filter(e => new Date(e.date) > new Date()).length}
                        </Text>
                      </VStack>
                    </Box>
                  </HStack>
                </VStack>
              </Box>
            </VStack>
          )}

          {/* 网络图谱预览 */}
          <VStack space="md">
            <HStack justifyContent="space-between" alignItems="center">
              <Heading size="md" color="$textDark900">关系图谱</Heading>
            </HStack>
            <Pressable 
              bg="$backgroundLight0" 
              rounded="$md" 
              p={24} 
              alignItems="center"
              onPress={() => navigation.navigate('NetworkVisual')}
              sx={{
                _dark: {
                  bg: '$backgroundDark900'
                }
              }}
            >
              <Icon as={Map} size="2xl" color="$primary500" />
              <Heading size="sm" color="$textDark900" mt={8}>探索您的关系网络</Heading>
              <Text size="xs" color="$textDark700" textAlign="center" mt={4}>
                可视化人脉连接，发现潜在机会。
              </Text>
            </Pressable>
          </VStack>

        </VStack>
      </ScrollView>
    </Box>
    </ErrorBoundary>
  );
};

export default HomeScreen;