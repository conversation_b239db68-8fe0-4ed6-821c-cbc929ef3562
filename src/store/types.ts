// Store types and interfaces

export interface Contact {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  avatar?: string;
  company?: string;
  position?: string;
  notes?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  isFavorite: boolean;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  position: string;
  notes: string;
  tags: string[];
  avatar?: string;
}

export interface SettingsState {
  // Notifications
  notificationsEnabled: boolean;
  emailNotifications: boolean;
  reminderNotifications: boolean;
  networkUpdates: boolean;
  
  // App Settings
  dataSync: boolean;
  darkMode: boolean;
  aiSuggestions: boolean;
  autoTagging: boolean;
  
  // Security
  biometricLogin: boolean;
  locationTracking: boolean;
  
  // Language & Locale
  language: string;
  
  // User Profile
  userProfile: {
    name: string;
    email: string;
    avatar: string;
    plan: string;
  };
}

export interface AppState {
  // Loading states
  isLoading: boolean;
  isContactsLoading: boolean;
  isSettingsLoading: boolean;
  
  // Error states
  error: string | null;
  contactsError: string | null;
  settingsError: string | null;
  
  // Network state
  isOnline: boolean;
  
  // Authentication
  isAuthenticated: boolean;
  
  // App info
  version: string;
  buildNumber: string;
}

export interface UIState {
  // Modal states
  isConfirmationModalVisible: boolean;
  confirmationModalConfig: {
    title: string;
    message: string;
    confirmText: string;
    cancelText: string;
    confirmAction: 'default' | 'destructive';
    onConfirm: () => void;
  } | null;
  
  // Navigation state
  activeTab: string;
  
  // Search state
  searchQuery: string;
  searchFilters: {
    tags: string[];
    isFavorite?: boolean;
  };
  
  // Form states
  isFormDirty: boolean;
  formErrors: Record<string, string>;
}

export interface ContactsState {
  contacts: Contact[];
  filteredContacts: Contact[];
  selectedContact: Contact | null;
  searchQuery: string;
  selectedTags: string[];
  availableTags: string[];
  sortBy: 'name' | 'createdAt' | 'updatedAt';
  sortOrder: 'asc' | 'desc';
  isLoading: boolean;
  error: string | null;
}

// Store action types
export interface SettingsActions {
  updateSetting: <K extends keyof SettingsState>(key: K, value: SettingsState[K]) => void;
  resetSettings: () => void;
  updateUserProfile: (profile: Partial<SettingsState['userProfile']>) => void;
  toggleNotifications: (enabled: boolean) => void;
  setLanguage: (language: string) => void;
}

export interface ContactsActions {
  // CRUD operations
  addContact: (contact: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateContact: (id: string, updates: Partial<Contact>) => void;
  deleteContact: (id: string) => void;
  toggleFavorite: (id: string) => void;
  
  // Search and filter
  setSearchQuery: (query: string) => void;
  setSelectedTags: (tags: string[]) => void;
  setSortBy: (sortBy: ContactsState['sortBy']) => void;
  setSortOrder: (order: ContactsState['sortOrder']) => void;
  
  // Selection
  setSelectedContact: (contact: Contact | null) => void;
  
  // Bulk operations
  deleteMultipleContacts: (ids: string[]) => void;
  addTagToMultiple: (ids: string[], tag: string) => void;
  
  // Data management
  loadContacts: () => Promise<void>;
  syncContacts: () => Promise<void>;
  exportContacts: () => Promise<string>;
  importContacts: (data: Contact[]) => Promise<void>;
}

export interface AppActions {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setOnlineStatus: (isOnline: boolean) => void;
  setAuthenticated: (isAuthenticated: boolean) => void;
  clearErrors: () => void;
}

export interface UIActions {
  showConfirmationModal: (config: UIState['confirmationModalConfig']) => void;
  hideConfirmationModal: () => void;
  setActiveTab: (tab: string) => void;
  setSearchQuery: (query: string) => void;
  setSearchFilters: (filters: UIState['searchFilters']) => void;
  setFormDirty: (isDirty: boolean) => void;
  setFormErrors: (errors: Record<string, string>) => void;
  clearFormErrors: () => void;
}

// Combined store types
export type SettingsStore = SettingsState & SettingsActions;
export type ContactsStore = ContactsState & ContactsActions;
export type AppStore = AppState & AppActions;
export type UIStore = UIState & UIActions;
