import { useEffect } from 'react';
import NetInfo from '@react-native-community/netinfo';
import { useAppActions } from '../store';

/**
 * 网络状态监听Hook
 * 监听网络连接状态变化并更新全局状态
 */
export const useNetworkStatus = () => {
  const { setOnlineStatus } = useAppActions();

  useEffect(() => {
    // 获取初始网络状态
    const getInitialState = async () => {
      const state = await NetInfo.fetch();
      setOnlineStatus(state.isConnected ?? false);
    };

    getInitialState();

    // 监听网络状态变化
    const unsubscribe = NetInfo.addEventListener(state => {
      setOnlineStatus(state.isConnected ?? false);
    });

    return () => {
      unsubscribe();
    };
  }, [setOnlineStatus]);
};
