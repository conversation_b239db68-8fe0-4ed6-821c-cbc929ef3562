/**
 * 沟通记录类型定义
 */

export type CommunicationType = 
  | 'call'           // 电话通话
  | 'video_call'     // 视频通话
  | 'email'          // 邮件
  | 'message'        // 短信/即时消息
  | 'meeting'        // 面对面会议
  | 'social_media'   // 社交媒体互动
  | 'letter'         // 信件
  | 'other';         // 其他方式

export type CommunicationDirection = 
  | 'outgoing'       // 主动联系
  | 'incoming'       // 被动接收
  | 'mutual';        // 双向互动

export type CommunicationSentiment = 
  | 'very_positive'  // 非常积极
  | 'positive'       // 积极
  | 'neutral'        // 中性
  | 'negative'       // 消极
  | 'very_negative'; // 非常消极

export type CommunicationOutcome = 
  | 'successful'     // 成功达成目标
  | 'partial'        // 部分达成
  | 'unsuccessful'   // 未达成目标
  | 'follow_up_needed' // 需要后续跟进
  | 'no_response'    // 无回应
  | 'postponed';     // 延期处理

export interface CommunicationParticipant {
  contactId: string;
  name: string;
  role?: string;           // 在此次沟通中的角色
  participationLevel: 'primary' | 'secondary' | 'observer'; // 参与程度
}

export interface CommunicationAttachment {
  id: string;
  name: string;
  type: 'document' | 'image' | 'audio' | 'video' | 'link' | 'other';
  url?: string;            // 文件URL或路径
  size?: number;           // 文件大小（字节）
  description?: string;    // 附件描述
}

export interface CommunicationTag {
  id: string;
  name: string;
  color: string;
  category: 'topic' | 'project' | 'priority' | 'status' | 'custom';
}

export interface CommunicationRecord {
  id: string;
  
  // 基本信息
  type: CommunicationType;
  direction: CommunicationDirection;
  subject?: string;        // 主题/标题
  content?: string;        // 内容摘要
  
  // 时间信息
  startTime: Date;         // 开始时间
  endTime?: Date;          // 结束时间（用于通话、会议等）
  duration?: number;       // 持续时间（分钟）
  
  // 参与者信息
  primaryContactId: string; // 主要联系人ID
  participants: CommunicationParticipant[]; // 所有参与者
  
  // 沟通分析
  sentiment?: CommunicationSentiment;
  outcome?: CommunicationOutcome;
  effectiveness?: number;   // 沟通效果评分 (1-10)
  importance?: number;      // 重要程度 (1-5)
  
  // 内容分析
  topics?: string[];        // 讨论的主题
  keywords?: string[];      // 关键词
  actionItems?: string[];   // 行动项
  decisions?: string[];     // 决策记录
  
  // 关联信息
  relatedMeetingId?: string;    // 关联的会议ID
  relatedProjectId?: string;    // 关联的项目ID
  parentCommunicationId?: string; // 父级沟通记录（用于回复链）
  
  // 附件和标签
  attachments?: CommunicationAttachment[];
  tags?: CommunicationTag[];
  
  // 后续跟进
  followUpRequired?: boolean;
  followUpDate?: Date;
  followUpNotes?: string;
  
  // 元数据
  location?: string;        // 沟通地点
  platform?: string;       // 沟通平台（微信、钉钉、Zoom等）
  isConfidential?: boolean; // 是否机密
  isArchived?: boolean;     // 是否归档
  
  // 系统字段
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;       // 创建者ID
}

export interface CommunicationTemplate {
  id: string;
  name: string;
  type: CommunicationType;
  subject?: string;
  content: string;
  tags?: string[];
  isDefault?: boolean;
  category: 'greeting' | 'follow_up' | 'meeting_request' | 'thank_you' | 'custom';
  createdAt: Date;
  updatedAt: Date;
}

export interface CommunicationStats {
  // 总体统计
  totalCommunications: number;
  totalDuration: number;        // 总时长（分钟）
  averageFrequency: number;     // 平均沟通频率（天）
  
  // 按类型统计
  byType: Record<CommunicationType, number>;
  byDirection: Record<CommunicationDirection, number>;
  bySentiment: Record<CommunicationSentiment, number>;
  byOutcome: Record<CommunicationOutcome, number>;
  
  // 时间统计
  thisWeek: number;
  thisMonth: number;
  thisQuarter: number;
  thisYear: number;
  
  // 趋势分析
  frequencyTrend: 'increasing' | 'stable' | 'decreasing';
  sentimentTrend: 'improving' | 'stable' | 'declining';
  effectivenessTrend: 'improving' | 'stable' | 'declining';
  
  // 最近活动
  lastCommunicationDate?: Date;
  daysSinceLastCommunication?: number;
  mostFrequentType?: CommunicationType;
  averageSentiment?: number;
  averageEffectiveness?: number;
}

export interface CommunicationAnalysis {
  contactId: string;
  
  // 沟通模式分析
  preferredTypes: CommunicationType[];     // 偏好的沟通方式
  preferredTimes: string[];                // 偏好的沟通时间
  responseTime: {                          // 响应时间分析
    average: number;                       // 平均响应时间（小时）
    fastest: number;
    slowest: number;
  };
  
  // 关系发展分析
  relationshipStrength: number;           // 关系强度 (0-100)
  engagementLevel: number;                // 参与度 (0-100)
  communicationQuality: number;           // 沟通质量 (0-100)
  
  // 沟通效果分析
  successRate: number;                    // 成功率 (0-100)
  averageEffectiveness: number;           // 平均效果评分
  sentimentDistribution: Record<CommunicationSentiment, number>;
  
  // 话题分析
  commonTopics: string[];                 // 常见话题
  topKeywords: string[];                  // 高频关键词
  
  // 建议和洞察
  insights: string[];                     // 分析洞察
  recommendations: string[];              // 改进建议
  
  // 预测
  nextCommunicationPrediction?: Date;     // 预测下次沟通时间
  relationshipRisk?: 'low' | 'medium' | 'high'; // 关系风险评估
  
  lastAnalyzedAt: Date;
}

export interface CommunicationFilter {
  contactIds?: string[];
  types?: CommunicationType[];
  directions?: CommunicationDirection[];
  sentiments?: CommunicationSentiment[];
  outcomes?: CommunicationOutcome[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  tags?: string[];
  importance?: {
    min: number;
    max: number;
  };
  effectiveness?: {
    min: number;
    max: number;
  };
  hasFollowUp?: boolean;
  isArchived?: boolean;
  searchQuery?: string;
}

export interface CommunicationSummary {
  period: 'day' | 'week' | 'month' | 'quarter' | 'year';
  startDate: Date;
  endDate: Date;
  
  // 数量统计
  totalCount: number;
  byType: Record<CommunicationType, number>;
  byContact: Array<{
    contactId: string;
    contactName: string;
    count: number;
    lastCommunication: Date;
  }>;
  
  // 质量分析
  averageSentiment: number;
  averageEffectiveness: number;
  successRate: number;
  
  // 趋势对比
  comparedToPrevious: {
    countChange: number;        // 数量变化百分比
    sentimentChange: number;    // 情感变化
    effectivenessChange: number; // 效果变化
  };
  
  // 亮点和问题
  highlights: string[];          // 亮点
  concerns: string[];           // 关注点
  actionItems: string[];        // 建议行动
}

// 沟通记录快速创建模板
export interface QuickCommunicationTemplate {
  type: CommunicationType;
  subject: string;
  content?: string;
  duration?: number;
  sentiment?: CommunicationSentiment;
  outcome?: CommunicationOutcome;
  tags?: string[];
}
