/**
 * AI智能推荐页面
 * 显示个性化推荐和智能建议
 */

import React, { useEffect, useCallback } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Heading,
  ScrollView,
  RefreshControl,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import {
  ArrowLeft,
  Settings,
  TrendingUp,
  Zap,
} from 'lucide-react-native';

import { ErrorBoundary, StandardButton, CircularButton } from '../components/ui';
import { RecommendationList } from '../components/recommendation/RecommendationList';
import { 
  useContactsStore, 
  useBusinessEventStore, 
  useRelationshipStore, 
  useCommunicationStore,
  useRecommendationStore,
  useRecommendationSelectors
} from '../store';
import { usePerformanceMonitor, useNetworkError } from '../hooks';
import { RecommendationContext } from '../services/aiRecommendationService';

type RecommendationScreenNavigationProp = any; // TODO: 添加正确的导航类型

/**
 * AI智能推荐屏幕
 */
const RecommendationScreen: React.FC = () => {
  const navigation = useNavigation<RecommendationScreenNavigationProp>();
  const toast = useToast();

  // Performance monitoring
  usePerformanceMonitor('RecommendationScreen');

  // Global state
  const { contacts } = useContactsStore();
  const { events } = useBusinessEventStore();
  const { relationships } = useRelationshipStore();
  const { communications } = useCommunicationStore();
  
  const {
    generateRecommendations,
    refreshRecommendations,
    cleanupExpiredRecommendations,
    setError,
    clearError
  } = useRecommendationStore();

  const activeRecommendations = useRecommendationSelectors.useActiveRecommendations();
  const isLoading = useRecommendationSelectors.useIsLoading();
  const error = useRecommendationSelectors.useError();
  const stats = useRecommendationSelectors.useRecommendationStats();
  const { showError } = useNetworkError();

  // Local state
  const [refreshing, setRefreshing] = React.useState(false);

  // Build recommendation context
  const buildRecommendationContext = useCallback((): RecommendationContext => {
    return {
      contacts,
      events,
      relationships,
      communications,
      userPreferences: {
        industries: [],
        eventTypes: [],
        relationshipGoals: [],
        communicationFrequency: 'medium',
        networkingStyle: 'moderate'
      }
    };
  }, [contacts, events, relationships, communications]);

  // Initialize recommendations on screen focus
  useFocusEffect(
    useCallback(() => {
      const initializeRecommendations = async () => {
        try {
          clearError();
          cleanupExpiredRecommendations();
          
          const context = buildRecommendationContext();
          await generateRecommendations(context);
        } catch (error) {
          console.error('Error initializing recommendations:', error);
          showError({
            message: '初始化推荐时出错',
            code: 'INIT_ERROR'
          });
        }
      };

      initializeRecommendations();
    }, [generateRecommendations, buildRecommendationContext, clearError, cleanupExpiredRecommendations, showError])
  );

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      clearError();
      const context = buildRecommendationContext();
      await refreshRecommendations(context);
      
      toast.show({
        placement: 'top',
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>推荐已更新</ToastTitle>
            <ToastDescription>已生成最新的智能推荐</ToastDescription>
          </Toast>
        ),
      });
    } catch (error) {
      console.error('Error refreshing recommendations:', error);
      showError({
        message: '刷新推荐时出错，请重试',
        code: 'REFRESH_ERROR'
      });
    } finally {
      setRefreshing(false);
    }
  }, [refreshRecommendations, buildRecommendationContext, clearError, toast, showError]);

  // Handle recommendation action
  const handleRecommendationAction = useCallback((recommendationId: string, action: string) => {
    // Navigate to appropriate screen based on action
    switch (action) {
      case 'primary':
        // TODO: 根据推荐类型导航到相应页面
        toast.show({
          placement: 'top',
          render: ({ id }) => (
            <Toast nativeID={id} action="info" variant="accent">
              <ToastTitle>执行推荐</ToastTitle>
              <ToastDescription>正在处理推荐操作...</ToastDescription>
            </Toast>
          ),
        });
        break;
      case 'later':
        toast.show({
          placement: 'top',
          render: ({ id }) => (
            <Toast nativeID={id} action="info" variant="accent">
              <ToastTitle>稍后处理</ToastTitle>
              <ToastDescription>已标记为稍后处理</ToastDescription>
            </Toast>
          ),
        });
        break;
    }
  }, [toast]);

  // Handle navigation
  const handleGoBack = () => {
    navigation.goBack();
  };

  const handleOpenSettings = () => {
    // TODO: 导航到推荐设置页面
    toast.show({
      placement: 'top',
      render: ({ id }) => (
        <Toast nativeID={id} action="info" variant="accent">
          <ToastTitle>推荐设置</ToastTitle>
          <ToastDescription>推荐设置功能即将推出</ToastDescription>
        </Toast>
      ),
    });
  };

  return (
    <ErrorBoundary>
      <Box flex={1} bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark950' } }}>
        {/* Header */}
        <Box
          pt="$12"
          pb="$4"
          px="$4"
          bg="$backgroundLight0"
          sx={{ _dark: { bg: '$backgroundDark900' } }}
          borderBottomWidth="$1"
          borderBottomColor="$borderLight200"
          sx={{ _dark: { borderBottomColor: '$borderDark700' } }}
        >
          <HStack justifyContent="space-between" alignItems="center">
            <HStack alignItems="center" space="md">
              <CircularButton
                icon={ArrowLeft}
                size="sm"
                variant="ghost"
                onPress={handleGoBack}
              />
              <VStack>
                <Heading size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  智能推荐
                </Heading>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  基于AI的个性化建议
                </Text>
              </VStack>
            </HStack>

            <CircularButton
              icon={Settings}
              size="sm"
              variant="ghost"
              onPress={handleOpenSettings}
            />
          </HStack>

          {/* Quick Stats */}
          <HStack mt="$4" space="md" justifyContent="space-around">
            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$primary600">
                {activeRecommendations.length}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                活跃推荐
              </Text>
            </VStack>

            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$success600">
                {stats.byPriority.high || 0}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                高优先级
              </Text>
            </VStack>

            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$warning600">
                {stats.byPriority.urgent || 0}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                紧急
              </Text>
            </VStack>

            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$info600">
                {Object.keys(stats.byType).length}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                推荐类型
              </Text>
            </VStack>
          </HStack>
        </Box>

        {/* Content */}
        <Box flex={1} p="$4">
          {activeRecommendations.length === 0 && !isLoading ? (
            // Empty state with suggestions
            <ScrollView
              flex={1}
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
              }
            >
              <VStack space="lg" alignItems="center" pt="$16">
                <Box
                  p="$6"
                  borderRadius="$full"
                  bg="$primary100"
                  sx={{ _dark: { bg: '$primary900' } }}
                >
                  <Zap size={48} color="$primary600" />
                </Box>
                
                <VStack space="sm" alignItems="center">
                  <Text fontWeight="$bold" size="xl" textAlign="center" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    开始使用智能推荐
                  </Text>
                  <Text textAlign="center" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }} maxWidth="$80">
                    系统会根据您的联系人、活动和沟通记录，为您提供个性化的智能建议
                  </Text>
                </VStack>

                <VStack space="md" w="$full" maxWidth="$80">
                  <Text fontWeight="$semibold" color="$textLight800" sx={{ _dark: { color: '$textDark200' } }}>
                    推荐功能包括：
                  </Text>
                  
                  <VStack space="sm">
                    {[
                      { icon: TrendingUp, title: '联系人推荐', desc: '发现潜在的有价值联系' },
                      { icon: TrendingUp, title: '活动建议', desc: '推荐适合的商务活动' },
                      { icon: TrendingUp, title: '关系维护', desc: '提醒维护重要关系' },
                      { icon: TrendingUp, title: '商务机会', desc: '识别潜在商务机会' },
                      { icon: TrendingUp, title: '网络扩展', desc: '建议扩展关系网络' },
                      { icon: TrendingUp, title: '跟进提醒', desc: '智能跟进建议' },
                    ].map((item, index) => (
                      <HStack key={index} space="sm" alignItems="center">
                        <Box
                          p="$2"
                          borderRadius="$md"
                          bg="$primary100"
                          sx={{ _dark: { bg: '$primary900' } }}
                        >
                          <item.icon size={16} color="$primary600" />
                        </Box>
                        <VStack flex={1}>
                          <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                            {item.title}
                          </Text>
                          <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                            {item.desc}
                          </Text>
                        </VStack>
                      </HStack>
                    ))}
                  </VStack>
                </VStack>

                <StandardButton
                  variant="solid"
                  action="primary"
                  onPress={handleRefresh}
                  leftIcon={TrendingUp}
                  mt="$6"
                >
                  生成推荐
                </StandardButton>
              </VStack>
            </ScrollView>
          ) : (
            // Recommendation list
            <RecommendationList
              onRecommendationAction={handleRecommendationAction}
              onRefresh={handleRefresh}
              showFilters={true}
              showHeader={false}
            />
          )}
        </Box>
      </Box>
    </ErrorBoundary>
  );
};

export default RecommendationScreen;
