import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import BottomTabNavigator from './BottomTabNavigator';
import SettingsScreen from '../pages/SettingsScreen';
import ContactDetailScreen from '../pages/ContactDetailScreen';
import ContactCreateScreen from '../pages/ContactCreateScreen';
import MeetingRecordScreen from '../pages/MeetingRecordScreen';
import NetworkVisualScreen from '../pages/NetworkVisualScreen';
import { RootStackParamList } from '../types';

const Stack = createNativeStackNavigator<RootStackParamList>();

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="MainTabs" component={BottomTabNavigator} />
        <Stack.Screen name="Settings" component={SettingsScreen} />
        <Stack.Screen name="ContactDetail" component={ContactDetailScreen} />
        <Stack.Screen name="ContactCreate" component={ContactCreateScreen} />
        <Stack.Screen name="MeetingRecord" component={MeetingRecordScreen} />
        <Stack.Screen name="NetworkVisual" component={NetworkVisualScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
