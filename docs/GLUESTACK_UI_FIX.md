# Gluestack UI Metro 模块解析问题修复文档

## 问题描述

React Native Expo 项目中 Gluestack UI 包的模块解析失败，主要错误为：
```
Unable to resolve "@gluestack-ui/button" from "node_modules/@gluestack-ui/themed/build/components/Button/index.js"
```

## 根本原因分析

1. **文件扩展名缺失**：Gluestack UI 包的 `package.json` 中 `main` 和 `module` 字段指向 `lib/index`，但实际文件为 `lib/index.jsx` 或 `lib/index.js`
2. **Metro 配置冲突**：`@stagewise-plugins` 需要 `unstable_enablePackageExports = true`，但这与 Gluestack UI 的解析机制冲突
3. **依赖管理问题**：存在冲突的依赖导致模块解析异常

## 解决方案

### 1. 核心修复：包路径问题

创建了自动修复脚本 `scripts/fix-gluestack-ui.js`，用于：
- 检测所有 `@gluestack-ui` 包中 `main` 和 `module` 字段缺失扩展名的问题
- 自动添加正确的文件扩展名（`.jsx` 或 `.js`）
- 共修复了 33 个包的路径问题

### 2. Metro 配置优化

在 `metro.config.js` 中：
```javascript
// 禁用 package exports，避免与 Gluestack UI 冲突
config.resolver.unstable_enablePackageExports = false;

// 设置正确的解析优先级
config.resolver.resolverMainFields = ['react-native', 'browser', 'module', 'main'];

// 确保包含所有必要的文件扩展名
config.resolver.sourceExts = ['js', 'jsx', 'json', 'ts', 'tsx', 'cjs', 'mjs'];
```

### 3. 依赖清理

- 移除了冲突的 `@stagewise-plugins/react` 和 `@stagewise/toolbar-react` 依赖
- 完全重新安装依赖包以确保干净的环境

### 4. 持久化修复

在 `package.json` 中添加 `postinstall` 脚本：
```json
{
  "scripts": {
    "postinstall": "node scripts/fix-gluestack-ui.js"
  }
}
```

这确保了每次安装依赖后自动执行修复。

## 验证结果

✅ Metro bundler 正常启动，无模块解析错误  
✅ Web 构建成功完成（100% 进度）  
✅ 应用正常运行在 http://localhost:8101  
✅ 所有 Gluestack UI 组件正常工作  
✅ 没有运行时错误  

## 技术细节

### 修复的包列表
- @gluestack-ui/accordion
- @gluestack-ui/actionsheet
- @gluestack-ui/alert-dialog
- @gluestack-ui/alert
- @gluestack-ui/avatar
- @gluestack-ui/button
- @gluestack-ui/checkbox
- @gluestack-ui/divider
- @gluestack-ui/fab
- @gluestack-ui/form-control
- @gluestack-ui/hooks
- @gluestack-ui/icon
- @gluestack-ui/image
- @gluestack-ui/input
- @gluestack-ui/link
- @gluestack-ui/menu
- @gluestack-ui/modal
- @gluestack-ui/overlay
- @gluestack-ui/popover
- @gluestack-ui/pressable
- @gluestack-ui/progress
- @gluestack-ui/provider
- @gluestack-ui/radio
- @gluestack-ui/select
- @gluestack-ui/slider
- @gluestack-ui/spinner
- @gluestack-ui/switch
- @gluestack-ui/tabs
- @gluestack-ui/textarea
- @gluestack-ui/themed
- @gluestack-ui/toast
- @gluestack-ui/tooltip
- @gluestack-ui/transitions
- @gluestack-ui/utils

### 修复前后对比
```json
// 修复前
{
  "main": "lib/index",
  "module": "lib/index"
}

// 修复后
{
  "main": "lib/index.jsx",
  "module": "lib/index.jsx"
}
```

## 最佳实践建议

1. **定期检查**：运行修复脚本确保包配置正确
2. **版本锁定**：考虑锁定 Gluestack UI 版本避免新版本引入类似问题
3. **监控更新**：关注 Gluestack UI 和 Metro 的更新，这些问题可能在未来版本中修复
4. **文档更新**：团队成员应了解这个修复机制

## 故障排除

如果问题再次出现：

1. 运行修复脚本：`node scripts/fix-gluestack-ui.js`
2. 清除 Metro 缓存：`npx expo start --reset-cache`
3. 删除并重新安装依赖：`rm -rf node_modules yarn.lock && yarn install`
4. 检查 Metro 配置是否正确

---

**修复日期**: 2025-06-15  
**版本**: Expo SDK 50, Gluestack UI 1.1.73  
**状态**: ✅ 已解决
