import React, { useState, useEffect } from 'react';
import {
  Platform
} from 'react-native';
import {
  Modal as GSModal,
  ModalBackdrop,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dalCloseButton,
  ModalBody,
  <PERSON>dal<PERSON>ooter,
  Heading,
  Text,
  Input,
  InputField,
  Button,
  ButtonText,
  Icon,
  VStack,
  HStack,
  Box,
  Pressable
} from '@gluestack-ui/themed';
import { XCircle, CalendarDays } from 'lucide-react-native';
import { useAlert } from '../../../context/AlertContext'; // Adjusted path
import { Reminder } from '../hooks/useContactDetail';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker'; // Assuming Reminder type is exported from here

export interface ReminderFormData {
  title: string;
  dueDate: string; // Consider using a date picker in a real app
  priority: 'high' | 'medium' | 'low';
}

interface ReminderModalProps {
  visible: boolean;
  reminder?: Reminder | null; // For editing
  onClose: () => void;
  onSave: (data: ReminderFormData & { id?: string }) => void;
}

const ReminderModal: React.FC<ReminderModalProps> = ({ visible, reminder, onSave, onClose }) => {
  const { showAlert } = useAlert();
  const [title, setTitle] = useState('');
  const [dueDate, setDueDate] = useState(new Date()); // Initialize with a Date object
  const [showDueDatePicker, setShowDueDatePicker] = useState(false);
  const [priority, setPriority] = useState<'high' | 'medium' | 'low'>('medium');

  useEffect(() => {
    if (reminder) {
      setTitle(reminder.title);
      setDueDate(new Date(reminder.dueDate)); // Convert string from reminder to Date object
      setPriority(reminder.priority);
    } else {
      // Reset form for new reminder
      setTitle('');
      setDueDate(new Date());
      setPriority('medium');
    }
  }, [reminder, visible]);

  const handleSave = () => {
    if (!title.trim()) {
      showAlert({
        title: 'Validation Error',
        message: 'Title is required.',
        buttons: [{ text: 'OK', onPress: () => {} }],
      });
      return;
    }
    // Basic date validation (can be improved)
    // Due date is now a Date object, so direct trim() check is not applicable.
    // The DateTimePicker ensures a valid date is always selected or the initial date is used.
    // If specific validation like 'not in past' is needed, it can be added here.
    if (!dueDate) { // Basic check, though DateTimePicker usually handles this
      showAlert({
        title: 'Validation Error',
        message: 'Due date is required.',
        buttons: [{ text: 'OK', onPress: () => {} }],
      });
      return;
    }

    onSave({
      id: reminder?.id,
      title,
      dueDate: dueDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
      priority,
    });
    onClose(); // Close modal after save
  };

  const priorities: Array<'low' | 'medium' | 'high'> = ['low', 'medium', 'high'];

  const handleDueDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    const currentDate = selectedDate || dueDate;
    setShowDueDatePicker(Platform.OS === 'ios');
    if (Platform.OS !== 'ios') {
        setShowDueDatePicker(false);
    }
    setDueDate(currentDate);
  };

  return (
    <GSModal isOpen={visible} onClose={onClose} finalFocusRef={undefined}>
      <ModalBackdrop />
      <ModalContent w={'$full'} maxW={'$lg'} m={'$4'}>
        <ModalHeader>
          <Heading size="lg">{reminder ? 'Edit Reminder' : 'Add Reminder'}</Heading>
          <ModalCloseButton>
            <Icon as={XCircle} />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody>
          <VStack space="md">
            <VStack space="xs">
              <Text size="sm" fontWeight="$medium">Title</Text>
              <Input>
                <InputField
                  value={title}
                  onChangeText={setTitle}
                  placeholder="Enter reminder title"
                />
              </Input>
            </VStack>

            <VStack space="xs">
              <Text size="sm" fontWeight="$medium">Due Date</Text>
              <Pressable onPress={() => setShowDueDatePicker(true)} borderWidth={1} borderColor='$borderLight300' borderRadius='$sm' p='$3' flexDirection='row' justifyContent='space-between' alignItems='center'>
                <Text>{dueDate.toLocaleDateString()}</Text>
                <Icon as={CalendarDays} size='md' color='$textLight500' />
              </Pressable>
              {showDueDatePicker && (
                <DateTimePicker
                  testID="dueDateTimePicker"
                  value={dueDate}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={handleDueDateChange}
                />
              )}
            </VStack>

            <VStack space="xs">
              <Text size="sm" fontWeight="$medium">Priority</Text>
              <HStack space="sm" justifyContent="space-between">
                {priorities.map((p) => (
                  <Button
                    key={p}
                    variant={priority === p ? 'solid' : 'outline'}
                    action={priority === p ? 'primary' : 'secondary'}
                    onPress={() => setPriority(p)}
                    flex={1}
                  >
                    <ButtonText>{p.charAt(0).toUpperCase() + p.slice(1)}</ButtonText>
                  </Button>
                ))}
              </HStack>
            </VStack>
          </VStack>
        </ModalBody>
        <ModalFooter>
          <HStack space="sm">
            <Button variant="outline" action="secondary" onPress={onClose}>
              <ButtonText>Cancel</ButtonText>
            </Button>
            <Button onPress={handleSave}>
              <ButtonText>Save</ButtonText>
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </GSModal>
  );
};

export default ReminderModal;
