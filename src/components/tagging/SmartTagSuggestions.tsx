/**
 * 智能标签建议组件
 * 显示AI生成的标签建议并允许用户选择应用
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  BadgeText,
  Pressable,
  ScrollView,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Heading,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';
import {
  Sparkles,
  Check,
  X,
  Plus,
  Tag,
  Brain,
  Zap,
  Target,
} from 'lucide-react-native';

import { StandardButton } from '../ui/StandardButton';
import { StandardIcon } from '../ui/StandardIcon';
import { 
  intelligentTaggingService, 
  TagSuggestion, 
  IntelligentTag, 
  TagType 
} from '../../services/intelligentTaggingService';
import { Contact, Communication, BusinessEvent } from '../../types';

interface SmartTagSuggestionsProps {
  contact?: Contact;
  event?: BusinessEvent;
  communications?: Communication[];
  events?: BusinessEvent[];
  contacts?: Contact[];
  onTagsApplied?: (tags: string[]) => void;
  isVisible: boolean;
  onClose: () => void;
}

export const SmartTagSuggestions: React.FC<SmartTagSuggestionsProps> = ({
  contact,
  event,
  communications = [],
  events = [],
  contacts = [],
  onTagsApplied,
  isVisible,
  onClose,
}) => {
  const [suggestions, setSuggestions] = useState<TagSuggestion[]>([]);
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();

  useEffect(() => {
    if (isVisible && (contact || event)) {
      generateSuggestions();
    }
  }, [isVisible, contact, event]);

  const generateSuggestions = async () => {
    setIsLoading(true);
    try {
      let newSuggestions: TagSuggestion[] = [];

      if (contact) {
        newSuggestions = intelligentTaggingService.generateContactTags(
          contact,
          communications,
          events
        );
      } else if (event) {
        newSuggestions = intelligentTaggingService.generateEventTags(event, contacts);
      }

      setSuggestions(newSuggestions);
    } catch (error) {
      console.error('Failed to generate tag suggestions:', error);
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={`toast-${id}`} action="error" variant="accent">
            <ToastTitle>生成失败</ToastTitle>
            <ToastDescription>无法生成智能标签建议</ToastDescription>
          </Toast>
        )
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTagToggle = (tagId: string) => {
    const newSelected = new Set(selectedTags);
    if (newSelected.has(tagId)) {
      newSelected.delete(tagId);
    } else {
      newSelected.add(tagId);
    }
    setSelectedTags(newSelected);
  };

  const handleApplyTags = () => {
    const selectedTagTexts = suggestions
      .filter(s => selectedTags.has(s.tag.id))
      .map(s => s.tag.text);

    onTagsApplied?.(selectedTagTexts);
    
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={`toast-${id}`} action="success" variant="accent">
          <ToastTitle>标签已应用</ToastTitle>
          <ToastDescription>已添加 {selectedTagTexts.length} 个智能标签</ToastDescription>
        </Toast>
      )
    });

    setSelectedTags(new Set());
    onClose();
  };

  const getTagTypeIcon = (type: TagType) => {
    const icons = {
      industry: Target,
      role: Brain,
      relationship: Sparkles,
      interest: Zap,
      skill: Plus,
      location: Tag,
      event: Tag,
      communication: Tag,
      custom: Tag,
    };
    return icons[type] || Tag;
  };

  const getTagTypeColor = (type: TagType) => {
    const colors = {
      industry: '$primary600',
      role: '$success600',
      relationship: '$warning600',
      interest: '$info600',
      skill: '$purple600',
      location: '$secondary600',
      event: '$orange600',
      communication: '$pink600',
      custom: '$gray600',
    };
    return colors[type] || '$gray600';
  };

  const getConfidenceLevel = (confidence: number) => {
    if (confidence >= 0.8) return { label: '高', color: '$success600' };
    if (confidence >= 0.6) return { label: '中', color: '$warning600' };
    return { label: '低', color: '$error600' };
  };

  const groupedSuggestions = suggestions.reduce((groups, suggestion) => {
    const type = suggestion.tag.type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(suggestion);
    return groups;
  }, {} as Record<TagType, TagSuggestion[]>);

  return (
    <Modal isOpen={isVisible} onClose={onClose} size="full">
      <ModalBackdrop />
      <ModalContent>
        <ModalHeader>
          <HStack space="sm" alignItems="center" flex={1}>
            <StandardIcon as={Sparkles} size="md" color="$primary600" />
            <VStack flex={1}>
              <Heading size="lg">智能标签建议</Heading>
              <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                AI为您推荐的相关标签
              </Text>
            </VStack>
          </HStack>
          <ModalCloseButton>
            <StandardIcon as={X} />
          </ModalCloseButton>
        </ModalHeader>

        <ModalBody>
          {isLoading ? (
            <VStack space="md" alignItems="center" py="$8">
              <StandardIcon as={Brain} size="2xl" color="$primary600" />
              <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                AI正在分析并生成标签建议...
              </Text>
            </VStack>
          ) : suggestions.length === 0 ? (
            <VStack space="md" alignItems="center" py="$8">
              <StandardIcon as={Tag} size="2xl" color="$textLight400" />
              <Text textAlign="center" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                暂无标签建议
              </Text>
              <Text textAlign="center" size="sm" color="$textLight400" sx={{ _dark: { color: '$textDark600' } }}>
                请提供更多信息以获得更好的标签建议
              </Text>
            </VStack>
          ) : (
            <ScrollView showsVerticalScrollIndicator={false}>
              <VStack space="lg">
                {Object.entries(groupedSuggestions).map(([type, typeSuggestions]) => (
                  <VStack key={type} space="sm">
                    <HStack space="sm" alignItems="center">
                      <StandardIcon 
                        as={getTagTypeIcon(type as TagType)} 
                        size="sm" 
                        color={getTagTypeColor(type as TagType)} 
                      />
                      <Text 
                        fontWeight="$bold" 
                        color="$textLight900" 
                        sx={{ _dark: { color: '$textDark100' } }}
                      >
                        {getTagTypeDisplayName(type as TagType)}
                      </Text>
                      <Badge size="xs" variant="outline">
                        <BadgeText>{typeSuggestions.length}</BadgeText>
                      </Badge>
                    </HStack>

                    <VStack space="xs">
                      {typeSuggestions.map((suggestion) => {
                        const isSelected = selectedTags.has(suggestion.tag.id);
                        const confidenceLevel = getConfidenceLevel(suggestion.tag.confidence);

                        return (
                          <Pressable
                            key={suggestion.tag.id}
                            onPress={() => handleTagToggle(suggestion.tag.id)}
                            p="$3"
                            borderRadius="$md"
                            borderWidth="$1"
                            borderColor={isSelected ? '$primary300' : '$borderLight200'}
                            bg={isSelected ? '$primary50' : '$backgroundLight0'}
                            sx={{
                              _dark: {
                                borderColor: isSelected ? '$primary700' : '$borderDark700',
                                bg: isSelected ? '$primary900' : '$backgroundDark900',
                              }
                            }}
                          >
                            <VStack space="sm">
                              <HStack justifyContent="space-between" alignItems="flex-start">
                                <HStack space="sm" alignItems="center" flex={1}>
                                  <Box
                                    width={20}
                                    height={20}
                                    borderRadius="$sm"
                                    borderWidth="$2"
                                    borderColor={isSelected ? '$primary600' : '$borderLight300'}
                                    bg={isSelected ? '$primary600' : 'transparent'}
                                    justifyContent="center"
                                    alignItems="center"
                                  >
                                    {isSelected && (
                                      <StandardIcon as={Check} size="xs" color="$white" />
                                    )}
                                  </Box>
                                  
                                  <VStack flex={1} space="xs">
                                    <HStack space="sm" alignItems="center">
                                      <Text
                                        fontWeight="$medium"
                                        color="$textLight900"
                                        sx={{ _dark: { color: '$textDark100' } }}
                                      >
                                        {suggestion.tag.text}
                                      </Text>
                                      <Badge 
                                        size="xs" 
                                        variant="solid" 
                                        action={confidenceLevel.color.includes('success') ? 'success' : 
                                               confidenceLevel.color.includes('warning') ? 'warning' : 'error'}
                                      >
                                        <BadgeText>{confidenceLevel.label}置信度</BadgeText>
                                      </Badge>
                                    </HStack>
                                    
                                    <Text
                                      size="sm"
                                      color="$textLight600"
                                      sx={{ _dark: { color: '$textDark400' } }}
                                    >
                                      {suggestion.reason}
                                    </Text>
                                  </VStack>
                                </HStack>
                              </HStack>

                              {suggestion.tag.metadata && (
                                <HStack space="md" ml="$8">
                                  {suggestion.tag.metadata.frequency && (
                                    <HStack space="xs" alignItems="center">
                                      <Text size="xs" color="$textLight500">频次:</Text>
                                      <Text size="xs" color="$textLight600">
                                        {suggestion.tag.metadata.frequency}
                                      </Text>
                                    </HStack>
                                  )}
                                  <HStack space="xs" alignItems="center">
                                    <Text size="xs" color="$textLight500">来源:</Text>
                                    <Text size="xs" color="$textLight600">
                                      {getSourceDisplayName(suggestion.tag.source)}
                                    </Text>
                                  </HStack>
                                </HStack>
                              )}
                            </VStack>
                          </Pressable>
                        );
                      })}
                    </VStack>
                  </VStack>
                ))}
              </VStack>
            </ScrollView>
          )}
        </ModalBody>

        <ModalFooter>
          <HStack space="md" flex={1}>
            <StandardButton
              variant="outline"
              onPress={onClose}
              flex={1}
            >
              取消
            </StandardButton>
            
            <StandardButton
              variant="solid"
              action="primary"
              onPress={handleApplyTags}
              disabled={selectedTags.size === 0}
              flex={1}
              leftIcon={selectedTags.size > 0 ? Check : undefined}
            >
              应用标签 ({selectedTags.size})
            </StandardButton>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

function getTagTypeDisplayName(type: TagType): string {
  const displayNames: Record<TagType, string> = {
    industry: '行业标签',
    role: '职位角色',
    relationship: '关系类型',
    interest: '兴趣爱好',
    skill: '技能专长',
    location: '地理位置',
    event: '活动相关',
    communication: '沟通特征',
    custom: '自定义',
  };
  return displayNames[type] || type;
}

function getSourceDisplayName(source: string): string {
  const displayNames: Record<string, string> = {
    profile: '个人资料',
    communication: '沟通内容',
    event: '活动参与',
    behavior: '行为分析',
    manual: '手动添加',
  };
  return displayNames[source] || source;
}

export default SmartTagSuggestions;
