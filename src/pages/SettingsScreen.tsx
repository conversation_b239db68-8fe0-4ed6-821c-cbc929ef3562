import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Platform,
  Linking
} from 'react-native';
import {
  Text,
  Switch,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Heading,
  Button,
  ButtonText,
  Icon
} from '@gluestack-ui/themed';
import { useNavigation, CommonActions, NavigationProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { ComponentProps } from 'react';

import { ChevronRight, ChevronLeft, Bell, Clock, Mail, MapPin, RefreshCw, Trash2, Users, HelpCircle, ShieldCheck, FileText, Key, Database, X } from 'lucide-react-native';
import type { RootStackParamList } from '../types';

interface SettingItemProps {
  icon: React.ComponentType<any>;
  
  title: string;
  description?: string;
  toggle?: boolean;
  value?: boolean | string;
  onValueChange?: (newValue: boolean) => void;
  action?: () => void;
}

const iconColorMap = {
  'notifications': '#FFCC00', // TODO: Replace with Gluestack UI token (was colors.accentWarning) // Mapped from warning
  'mail': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.primary)
  'time': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.accentNeutral/info) // Mapped from info
  'people': '#34C759', // TODO: Replace with Gluestack UI token (was colors.success) // Assuming 'success' exists and is appropriate, else accentPositive
  'cloud-sync': '#5856D6', // TODO: Replace with Gluestack UI token (was colors.secondary)
  'moon': '#333333', // TODO: Replace with Gluestack UI token (was colors.textPrimary/darkText) // Mapped from darkText
  'bulb': '#FFCC00', // TODO: Replace with Gluestack UI token (was colors.accentWarning) // Mapped from warning
  'pricetag': '#34C759', // TODO: Replace with Gluestack UI token (was colors.success) // Assuming 'success' exists
  'finger-print': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.primary)
  'location': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.accentNeutral/info) // Mapped from info
  'shield-checkmark': '#34C759', // TODO: Replace with Gluestack UI token (was colors.success) // Assuming 'success' exists
  'document-text': '#5856D6', // TODO: Replace with Gluestack UI token (was colors.secondary)
  'information-circle': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.accentNeutral/info) // Mapped from info
  'exit': '#FF3B30', // TODO: Replace with Gluestack UI token (was colors.accentNegative/danger) // Mapped from danger
  'trash': '#FF3B30', // TODO: Replace with Gluestack UI token (was colors.accentNegative/danger) // Mapped from danger
  'checkmark-circle-outline': '#34C759', // TODO: Replace with Gluestack UI token (was colors.success) // Assuming 'success' exists
  'ellipse-outline': '#C7C7CC', // TODO: Replace with Gluestack UI token (was colors.mediumGray)
  'key': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.primary)
  'user-shield': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.primary)
  'file-export': '#5856D6', // TODO: Replace with Gluestack UI token (was colors.secondary)
  'database-export': '#5856D6', // TODO: Replace with Gluestack UI token (was colors.secondary)
  'book-outline': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.accentNeutral/info) // Mapped from info
  'help-circle': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.accentNeutral/info) // Mapped from info
  'code-slash': '#333333', // TODO: Replace with Gluestack UI token (was colors.textPrimary/darkText) // Mapped from darkText
  'information': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.accentNeutral/info) // Mapped from info
  'lock-closed': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.primary)
  'bar-chart': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.accentNeutral/info) // Mapped from info
  'trending-up': '#34C759', // TODO: Replace with Gluestack UI token (was colors.success) // Assuming 'success' exists
  'color-palette': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.primary)
  'language': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.primary)
  'list': '#5856D6', // TODO: Replace with Gluestack UI token (was colors.secondary)
  'refresh': '#007AFF', // TODO: Replace with Gluestack UI token (was colors.primary)
  'chevron-forward': '#C7C7CC' // TODO: Replace with Gluestack UI token (was colors.mediumGray)
} as const;

type IconColorMapKey = keyof typeof iconColorMap;

const SettingsScreen = () => {
  const toast = useToast();
  const { t, i18n } = useTranslation();


  // State for the confirmation modal
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalContent, setModalContent] = useState({
    title: '',
    message: '',
    confirmText: 'Confirm',
    confirmAction: 'default' as 'default' | 'destructive',
    onConfirm: () => {},
  });

  const openConfirmationModal = (config: typeof modalContent) => {
    setModalContent(config);
    setIsModalVisible(true);
  };
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  // Settings state
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [reminderNotifications, setReminderNotifications] = useState(true);
  const [networkUpdates, setNetworkUpdates] = useState(true);
  const [dataSync, setDataSync] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState(true);
  const [autoTagging, setAutoTagging] = useState(true);
  const [biometricLogin, setBiometricLogin] = useState(false);
  const [locationTracking, setLocationTracking] = useState(false);

  const supportedLanguages = [
    { code: 'en', name: 'English' },
    { code: 'zh', name: '中文' },
  ];
  const currentLanguage = i18n.language;

  const handleChangeLanguage = (langCode: string) => {
    if (langCode !== currentLanguage) {
      i18n.changeLanguage(langCode);
      // Optional: Force a re-render or navigation reset if UI doesn't update automatically
      // navigation.dispatch(
      //   CommonActions.reset({
      //     index: 0,
      //     routes: [{ name: 'Settings' }], // Or back to Home/previous route
      //   })
      // );
    }
  };

  // Mock user data
  const user = {
    name: "Alex Morgan",
    email: "<EMAIL>",
    avatar: "https://api.a0.dev/assets/image?text=AM&aspect=1:1",
    plan: "Professional",
    storageUsed: "2.3 GB",
    storageTotal: "10 GB",
  };

    const handleLogout = () => {
    openConfirmationModal({
      title: t('settings.logoutConfirmationTitle', 'Confirm Logout'),
      message: t('settings.logoutConfirmationMessage', 'Are you sure you want to log out?'),
      confirmText: t('settings.logout', 'Logout'),
      confirmAction: 'destructive',
      onConfirm: () => {
        // In a real app, this would clear auth state and navigate to login
        console.log('User logged out');
        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="success">
              <ToastTitle>{t('settings.loggedOutTitle', 'Logged Out')}</ToastTitle>
              <ToastDescription>
                {t('settings.loggedOutMessage', 'You have been logged out successfully.')}
              </ToastDescription>
            </Toast>
          ),
        });
      },
    });
  };

    const handleResetPreferences = () => {
    openConfirmationModal({
      title: 'Reset Preferences',
      message: 'Are you sure you want to reset all preferences to default settings?',
      confirmText: 'Reset',
      confirmAction: 'destructive',
      onConfirm: () => {
        setNotificationsEnabled(true);
        setEmailNotifications(true);
        setReminderNotifications(true);
        setNetworkUpdates(true);
        setDataSync(true);
        setDarkMode(false);
        setAiSuggestions(true);
        setAutoTagging(true);
        setBiometricLogin(false);
        setLocationTracking(false);
        toast.show({
          placement: "top",
          render: ({ id }: { id: string }) => (
            <Toast nativeID={`toast-${id}`} action="success" variant="accent">
              <ToastTitle>Success</ToastTitle>
              <ToastDescription>Preferences have been reset.</ToastDescription>
            </Toast>
          ),
        });
      }
    });
  };

  const handleExportData = () => {
    showAlert({
      title: 'Export Data',
      message: 'Your data will be prepared for export. You\'ll receive an email with your data file once it\'s ready.',
      buttons: [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: "Export",
          onPress: () => {
            toast.show({
              placement: "top",
              render: ({ id }: { id: string }) => (
                <Toast nativeID={`toast-${id}`} action="success" variant="accent">
                  <ToastTitle>Success</ToastTitle>
                  <ToastDescription>Your data export has started and will be sent to your email.</ToastDescription>
                </Toast>
              ),
            });
          }
        }
      ]
    });
  };

    const handleDeleteAccount = () => {
    openConfirmationModal({
      title: "Delete Account",
      message: "This action cannot be undone. All your data will be permanently deleted.",
      confirmText: "Delete Account",
      confirmAction: 'destructive',
      onConfirm: () => {
        // Open the second, final confirmation modal
        openConfirmationModal({
          title: "Confirm Final Deletion",
          message: "Are you absolutely sure you want to permanently delete your account? This cannot be undone.",
          confirmText: "Yes, Delete My Account",
          confirmAction: 'destructive',
          onConfirm: () => {
            console.log('Account deleted');
            toast.show({
              placement: "top",
              render: ({ id }: { id: string }) => (
                <Toast nativeID={`toast-${id}`} action="success" variant="accent">
                  <ToastTitle>Account Deleted</ToastTitle>
                  <ToastDescription>Your account has been successfully deleted.</ToastDescription>
                </Toast>
              ),
            });
          },
        });
      },
    });
  };

  const openPrivacyPolicy = () => {
    Linking.openURL('https://example.com/privacy-policy');
  };

  const openTermsOfService = () => {
    Linking.openURL('https://example.com/terms-of-service');
  };

  const openHelpCenter = () => {
    Linking.openURL('https://example.com/help-center');
  };

  const renderSectionHeader = (title: string) => (
    <Text style={styles.sectionHeader}>{title}</Text>
  );

  const renderSettingItem = (props: SettingItemProps) => {
    const { icon, title, description, toggle, value, onValueChange, action } = props;
    const IconComponent = icon;
    
    return (
      <View style={styles.settingItem}>
        <View style={[styles.iconContainer, { backgroundColor: getIconBackgroundColor(title) }]}>
          <IconComponent size={24} color={'#FFFFFF' /* TODO: Replace with Gluestack UI token (was colors.white) */} />
        </View>
        
        <View style={styles.settingInfo}>
          <Text style={styles.settingTitle}>{title}</Text>
          {description && <Text style={styles.settingDescription}>{description}</Text>}
        </View>
        
        {toggle && (
          <Switch 
            value={typeof value === 'boolean' ? value : false} 
            onValueChange={onValueChange} 
            trackColor={{ false: '#F4F4F4', true: '#007AFF' }}
            thumbColor={value ? '#007AFF' : '#F4F4F4'}
          />
        )}
        
        {action && (
          <TouchableOpacity onPress={action} style={styles.actionButton}>
            <ChevronRight size={20} color="#999" />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // Function to assign background colors based on icon name
type IconColorMapKey = keyof typeof iconColorMap;
  const getIconBackgroundColor = (iconName: string): string => {
    if (iconName in iconColorMap) {
      return iconColorMap[iconName as IconColorMapKey];
    }
    return '#C7C7CC';
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
        >
          <ChevronLeft size={24} color="#333" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>{t('settings.title')}</Text>
        
        <View style={styles.rightPlaceholder} />
      </View>
      
      <ScrollView>
        {/* Profile Section */}
        <TouchableOpacity
          style={styles.profileSection}
          onPress={() => { /* TODO: Implement Profile screen or section and update navigation */ navigation.navigate('Settings'); }}
        >
          <Image source={{ uri: user.avatar }} style={styles.profileAvatar} />
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>{user.name}</Text>
            <Text style={styles.profileEmail}>{user.email}</Text>
            <View style={styles.profilePlanContainer}>
              <Text style={styles.profilePlan}>{user.plan}</Text>
            </View>
          </View>
          <ChevronRight size={20} color="#999" />
        </TouchableOpacity>

        {/* Storage Section */}
        <View style={styles.storageSection}>
          <View style={styles.storageInfo}>
            <Text style={styles.storageText}>Storage</Text>
            <Text style={styles.storagePercentage}>23% Used</Text>
          </View>
          <View style={styles.storageBar}>
            <View style={[styles.storageUsed, { width: '23%', backgroundColor: '#007AFF' /* TODO: Replace with Gluestack UI token (was colors.storageUsedBackground/primary) */ }]} />
          </View>
        </View>

        {/* App Settings */}
        {renderSectionHeader(t('settings.appSettings', 'App Settings'))}
        {renderSettingItem({
          icon: Bell,
          title: t('darkMode'),
          description: 'Switch between light and dark themes',
          toggle: true,
          value: darkMode,
          onValueChange: setDarkMode,
        })}
        {renderSettingItem({
          icon: Clock,
          title: 'AI Suggestions',
          description: 'Get personalized recommendations',
          toggle: true,
          value: aiSuggestions,
          onValueChange: setAiSuggestions,
        })}
        {renderSettingItem({
          icon: Mail,
          title: 'Auto-Tagging',
          description: 'Automatically tag contacts based on info',
          toggle: true,
          value: autoTagging,
          onValueChange: setAutoTagging,
        })}
        {renderSettingItem({
          icon: MapPin,
          title: 'Biometric Login',
          description: 'Use Face ID or Touch ID to secure your account',
          toggle: true,
          value: biometricLogin,
          onValueChange: setBiometricLogin,
        })}
        {renderSettingItem({
          icon: Users,
          title: 'Location Tracking',
          description: 'Allow the app to use your location',
          toggle: true,
          value: locationTracking,
          onValueChange: setLocationTracking,
        })}
        
        <View style={styles.separator} />
        
        {/* App Preferences */}
        {renderSectionHeader(t('settings.preferences', 'Preferences'))}
        
        <TouchableOpacity onPress={() => {
            showAlert({
              title: t('selectLanguage'),
              message: '',
              buttons: supportedLanguages.map(lang => ({
                text: lang.name,
                onPress: () => handleChangeLanguage(lang.code),
              })),
            });
          }}>
          <View style={styles.settingItem}>
            <View style={[styles.iconContainer, { backgroundColor: getIconBackgroundColor('language') }]}>
              <Key size={24} color={'#FFFFFF' /* TODO: Replace with Gluestack UI token (was colors.white) */} />
            </View>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>{t('language')}</Text>
            </View>
            <Text style={styles.valueText}>{supportedLanguages.find(l => l.code === currentLanguage)?.name || ''}</Text>
          </View>
        </TouchableOpacity>
        
        <View style={styles.separator} />

        {/* Notifications */}
        {renderSectionHeader(t('settings.notifications.title', 'Notifications'))}
        {renderSettingItem({
          icon: Bell,
          title: t('settings.notifications.title', 'Notifications'),
          description: 'Manage how you receive alerts',
          toggle: true,
          value: notificationsEnabled,
          onValueChange: setNotificationsEnabled,
        })}
        
        {notificationsEnabled && (
          <>
            {renderSettingItem({
              icon: Mail,
              title: t('settings.notifications.email', 'Email Notifications'),
              description: 'Receive updates via email',
              toggle: true,
              value: emailNotifications,
              onValueChange: setEmailNotifications,
            })}
            {renderSettingItem({
              icon: Clock,
              title: t('settings.notifications.reminders', 'Reminder Alerts'),
              description: 'Get notified for upcoming events',
              toggle: true,
              value: reminderNotifications,
              onValueChange: setReminderNotifications,
            })}
            {renderSettingItem({
              icon: Users,
              title: t('settings.notifications.network', 'Network Updates'),
              description: 'Alerts from your connections',
              toggle: true,
              value: networkUpdates,
              onValueChange: setNetworkUpdates,
            })}
          </>
        )}
        
        <View style={styles.separator} />
        
        {/* Data & Privacy */}
        {renderSectionHeader(t('settings.dataPrivacy', 'Data & Privacy'))}
        {renderSettingItem({
          icon: Database,
          title: 'Data Sync',
          description: 'Keep your data updated across devices',
          toggle: true,
          value: dataSync,
          onValueChange: setDataSync,
        })}
        {renderSettingItem({
          icon: ShieldCheck,
          title: 'Privacy Policy',
          action: openPrivacyPolicy,
        })}
        {renderSettingItem({
          icon: FileText,
          title: 'Terms of Service',
          action: openTermsOfService,
        })}
        
        <View style={styles.separator} />
        
        {/* Support */}
        {renderSectionHeader(t('settings.support', 'Support'))}
        {renderSettingItem({
          icon: HelpCircle,
          title: 'Help Center',
          action: openHelpCenter,
        })}
        
        <View style={styles.separator} />
        
        {/* Account */}
        {renderSectionHeader(t('settings.account', 'Account'))}
        {renderSettingItem({
          icon: Key,
          title: 'Change Password',
          action: () => { /* TODO: Implement ChangePassword screen and update navigation */ navigation.navigate('Settings'); },
        })}
        {renderSettingItem({
          icon: RefreshCw,
          title: 'Reset Preferences',
          description: 'Restore all settings to their default values',
          action: handleResetPreferences,
        })}
        {renderSettingItem({
          icon: Database,
          title: 'Export Data',
          description: 'Download a copy of your data',
          action: handleExportData,
        })}
        {renderSettingItem({
          icon: Trash2,
          title: 'Delete Account',
          description: 'Permanently remove your account and data',
          action: handleDeleteAccount,
        })}
        
        {/* App Version Info */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>RelationHub v1.0.0</Text>
          <Text style={styles.copyrightText}> 2025 RelationHub Inc.</Text>
        </View>
      </ScrollView>

      {/* Confirmation Modal */}
      <Modal
        isOpen={isModalVisible}
        onClose={() => setIsModalVisible(false)}
      >
        <ModalBackdrop />
        <ModalContent>
          <ModalHeader>
            <Heading size="lg">{modalContent.title}</Heading>
            <ModalCloseButton>
              <Icon as={X} />
            </ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            <Text>{modalContent.message}</Text>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="outline"
              size="sm"
              action="secondary"
              mr="$3"
              onPress={() => setIsModalVisible(false)}
            >
              <ButtonText>Cancel</ButtonText>
            </Button>
            <Button
              size="sm"
              action={modalContent.confirmAction === 'destructive' ? 'negative' : 'primary'}
              onPress={() => {
                modalContent.onConfirm();
                setIsModalVisible(false);
              }}
            >
              <ButtonText>{modalContent.confirmText}</ButtonText>
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFF',
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: 10,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E1E1',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  rightPlaceholder: {
    width: 40,
  },
  profileSection: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E1E1',
  },
  profileAvatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#F0F0F0',
  },
  profileInfo: {
    flex: 1,
    marginLeft: 15,
  },
  profileName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  profileEmail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
  },
  profilePlanContainer: {
    backgroundColor: '#E1F5FE',
    alignSelf: 'flex-start',
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 12,
  },
  profilePlan: {
    color: '#0288D1',
    fontSize: 12,
    fontWeight: '600',
  },
  editProfileButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  editProfileText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '500',
  },
  storageSection: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    marginBottom: 20,
  },
  storageInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  storageText: {
    fontSize: 14,
    color: '#666',
  },
  storagePercentage: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  storageBar: {
    height: 6,
    backgroundColor: '#E1E1E1',
    borderRadius: 3,
    overflow: 'hidden',
  },
  storageUsed: {
    height: '100%',
    borderRadius: 3,
  },
  sectionHeader: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
    paddingHorizontal: 15,
  },
  settingItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingVertical: 12,
    paddingHorizontal: 15,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  settingDescription: {
    fontSize: 13,
    color: '#8E8E93',
    marginTop: 2,
  },
  actionButton: {
    padding: 8,
  },
  valueText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  versionContainer: {
    alignItems: 'center',
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  versionText: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 5,
  },
  copyrightText: {
    fontSize: 12,
    color: '#8E8E93',
  },
  separator: {
    height: 1,
    backgroundColor: '#F0F0F0', // Match existing borderBottomColor
    marginHorizontal: 15, // Match paddingHorizontal of settingItem
    marginVertical: 8, // Add some vertical spacing
  },
});

export default SettingsScreen;