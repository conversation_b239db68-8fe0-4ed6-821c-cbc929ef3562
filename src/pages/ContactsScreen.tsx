import React, { useState, useMemo } from 'react';
import { FlatList } from 'react-native';
import {
  Box,
  VStack,
  Heading,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  Spinner,
  Text,
  Button,
  ButtonIcon,
  HStack
} from '@gluestack-ui/themed';
import { Search, Plus } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { CompositeNavigationProp } from '@react-navigation/native';

// Import global state and hooks
import { useContactsStore, useAppSelectors } from '../store';
import { usePerformanceMonitor } from '../hooks';
import { ErrorBoundary } from '../components/ui';
import { ContactsFilter } from '../components/Contacts';
import ContactListItem from '../features/Contacts/components/ContactListItem';
import { RootStackParamList, BottomTabParamList } from '../types';

// Define the navigation prop type for this screen
type ContactsScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<BottomTabParamList, 'ContactsTab'>,
  NativeStackNavigationProp<RootStackParamList>
>;

const ContactsScreen = () => {
  const navigation = useNavigation<ContactsScreenNavigationProp>();

  // Performance monitoring
  usePerformanceMonitor('ContactsScreen');

  // Global state
  const { filteredContacts } = useContactsStore();
  const { isContactsLoading } = useAppSelectors.useLoadingStates();

  // Use filtered contacts from global store
  const displayContacts = filteredContacts;

  const handleAddContact = () => {
    navigation.navigate('ContactCreate', {});
  };

  if (isContactsLoading) {
    return (
      <ErrorBoundary>
        <Box flex={1} justifyContent="center" alignItems="center">
          <Spinner size="large" />
        </Box>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <Box flex={1} bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark900' } }}>
        {/* Header */}
        <HStack
          justifyContent="space-between"
          alignItems="center"
          p="$4"
          bg="$backgroundLight0"
          sx={{ _dark: { bg: '$backgroundDark900' } }}
        >
          <Heading size="xl" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
            Contacts ({displayContacts.length})
          </Heading>
          <Button size="sm" action="primary" onPress={handleAddContact}>
            <ButtonIcon as={Plus} />
          </Button>
        </HStack>

        {/* Search and Filter */}
        <ContactsFilter />

        {/* Contacts List */}
        {displayContacts.length === 0 ? (
          <Box flex={1} justifyContent="center" alignItems="center" p="$4">
            <Text color="$textLight500" textAlign="center" sx={{ _dark: { color: '$textDark400' } }}>
              No contacts found. Try adjusting your search or add your first contact!
            </Text>
          </Box>
        ) : (
          <FlatList
            data={displayContacts}
            keyExtractor={item => item.id}
            renderItem={({ item }) => (
              <ContactListItem
                contact={item}
                onPress={() => navigation.navigate('ContactDetail', { contactId: item.id })}
                showFavoriteButton={true}
              />
            )}
            contentContainerStyle={{ paddingBottom: 20 }}
            showsVerticalScrollIndicator={false}
          />
        )}
      </Box>
    </ErrorBoundary>
  );
};

export default ContactsScreen;
