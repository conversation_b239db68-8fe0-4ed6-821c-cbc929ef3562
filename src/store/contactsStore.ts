import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ContactsStore, Contact } from './types';

// Mock data for development
const mockContacts: Contact[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Tech Corp',
    position: 'Software Engineer',
    notes: 'Met at tech conference',
    tags: ['work', 'tech'],
    avatar: 'https://api.a0.dev/assets/image?text=JD&aspect=1:1',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    isFavorite: true,
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Design Studio',
    position: 'UX Designer',
    notes: 'Collaborated on mobile app project',
    tags: ['work', 'design'],
    avatar: 'https://api.a0.dev/assets/image?text=JS&aspect=1:1',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
    isFavorite: false,
  },
];

// Helper functions
const generateId = () => Date.now().toString();

const filterContacts = (
  contacts: Contact[],
  searchQuery: string,
  selectedTags: string[]
): Contact[] => {
  return contacts.filter((contact) => {
    // Search filter
    const matchesSearch = searchQuery === '' || 
      contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.company?.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Tags filter
    const matchesTags = selectedTags.length === 0 ||
      selectedTags.some(tag => contact.tags.includes(tag));
    
    return matchesSearch && matchesTags;
  });
};

const sortContacts = (
  contacts: Contact[],
  sortBy: 'name' | 'createdAt' | 'updatedAt',
  sortOrder: 'asc' | 'desc'
): Contact[] => {
  return [...contacts].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'createdAt':
        comparison = a.createdAt.getTime() - b.createdAt.getTime();
        break;
      case 'updatedAt':
        comparison = a.updatedAt.getTime() - b.updatedAt.getTime();
        break;
    }
    
    return sortOrder === 'asc' ? comparison : -comparison;
  });
};

const extractTags = (contacts: Contact[]): string[] => {
  const tagSet = new Set<string>();
  contacts.forEach(contact => {
    contact.tags.forEach(tag => tagSet.add(tag));
  });
  return Array.from(tagSet).sort();
};

/**
 * Contacts Store
 * 管理联系人数据和相关操作
 */
export const useContactsStore = create<ContactsStore>()(
  persist(
    (set, get) => ({
      // Initial state
      contacts: mockContacts,
      filteredContacts: mockContacts,
      selectedContact: null,
      searchQuery: '',
      selectedTags: [],
      availableTags: extractTags(mockContacts),
      sortBy: 'name',
      sortOrder: 'asc',
      isLoading: false,
      error: null,
      
      // CRUD operations
      addContact: (contactData) => {
        const newContact: Contact = {
          ...contactData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        set((state) => {
          const updatedContacts = [...state.contacts, newContact];
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
          };
        });
      },
      
      updateContact: (id, updates) => {
        set((state) => {
          const updatedContacts = state.contacts.map(contact =>
            contact.id === id
              ? { ...contact, ...updates, updatedAt: new Date() }
              : contact
          );
          
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
            selectedContact: state.selectedContact?.id === id
              ? { ...state.selectedContact, ...updates, updatedAt: new Date() }
              : state.selectedContact,
          };
        });
      },
      
      deleteContact: (id) => {
        set((state) => {
          const updatedContacts = state.contacts.filter(contact => contact.id !== id);
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
            selectedContact: state.selectedContact?.id === id ? null : state.selectedContact,
          };
        });
      },
      
      toggleFavorite: (id) => {
        set((state) => {
          const updatedContacts = state.contacts.map(contact =>
            contact.id === id
              ? { ...contact, isFavorite: !contact.isFavorite, updatedAt: new Date() }
              : contact
          );
          
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            selectedContact: state.selectedContact?.id === id
              ? { ...state.selectedContact, isFavorite: !state.selectedContact.isFavorite }
              : state.selectedContact,
          };
        });
      },
      
      // Search and filter
      setSearchQuery: (query) => {
        set((state) => {
          const filteredContacts = sortContacts(
            filterContacts(state.contacts, query, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            searchQuery: query,
            filteredContacts,
          };
        });
      },
      
      setSelectedTags: (tags) => {
        set((state) => {
          const filteredContacts = sortContacts(
            filterContacts(state.contacts, state.searchQuery, tags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            selectedTags: tags,
            filteredContacts,
          };
        });
      },
      
      setSortBy: (sortBy) => {
        set((state) => {
          const filteredContacts = sortContacts(
            state.filteredContacts,
            sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            sortBy,
            filteredContacts,
          };
        });
      },
      
      setSortOrder: (order) => {
        set((state) => {
          const filteredContacts = sortContacts(
            state.filteredContacts,
            state.sortBy,
            order
          );
          
          return {
            ...state,
            sortOrder: order,
            filteredContacts,
          };
        });
      },
      
      // Selection
      setSelectedContact: (contact) => {
        set((state) => ({
          ...state,
          selectedContact: contact,
        }));
      },
      
      // Bulk operations
      deleteMultipleContacts: (ids) => {
        set((state) => {
          const updatedContacts = state.contacts.filter(contact => !ids.includes(contact.id));
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
            selectedContact: ids.includes(state.selectedContact?.id || '') ? null : state.selectedContact,
          };
        });
      },
      
      addTagToMultiple: (ids, tag) => {
        set((state) => {
          const updatedContacts = state.contacts.map(contact =>
            ids.includes(contact.id)
              ? { ...contact, tags: [...new Set([...contact.tags, tag])], updatedAt: new Date() }
              : contact
          );
          
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
          };
        });
      },
      
      // Data management (placeholder implementations)
      loadContacts: async () => {
        set((state) => ({ ...state, isLoading: true, error: null }));
        try {
          // TODO: Implement actual API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          set((state) => ({ ...state, isLoading: false }));
        } catch (error) {
          set((state) => ({ 
            ...state, 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to load contacts' 
          }));
        }
      },
      
      syncContacts: async () => {
        // TODO: Implement sync logic
        console.log('Syncing contacts...');
      },
      
      exportContacts: async () => {
        const { contacts } = get();
        return JSON.stringify(contacts, null, 2);
      },
      
      importContacts: async (data) => {
        set((state) => {
          const updatedContacts = [...state.contacts, ...data];
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
          };
        });
      },
    }),
    {
      name: 'contacts-storage',
      storage: createJSONStorage(() => AsyncStorage),
      // 只持久化数据，不持久化actions
      partialize: (state) => ({
        contacts: state.contacts,
        selectedTags: state.selectedTags,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
      }),
    }
  )
);
