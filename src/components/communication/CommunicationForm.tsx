/**
 * 沟通记录创建/编辑表单
 */

import React, { useState } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Input,
  InputField,
  Textarea,
  TextareaInput,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Heading,
  Pressable,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';
import {
  X,
  Phone,
  Video,
  Mail,
  MessageCircle,
  Users,
  Calendar,
  Clock,
  Star,
  ChevronDown,
} from 'lucide-react-native';

import { StandardButton, StandardIcon } from '../ui';
import { useCommunicationStore } from '../../store/communicationStore';
import { 
  CommunicationType, 
  CommunicationDirection, 
  CommunicationSentiment,
  CommunicationOutcome 
} from '../../types';

interface CommunicationFormProps {
  isOpen: boolean;
  onClose: () => void;
  contactId: string;
  contactName: string;
  initialData?: any;
}

export const CommunicationForm: React.FC<CommunicationFormProps> = ({
  isOpen,
  onClose,
  contactId,
  contactName,
  initialData,
}) => {
  const toast = useToast();
  const { addCommunication, updateCommunication } = useCommunicationStore();

  // 表单状态
  const [formData, setFormData] = useState({
    type: 'call' as CommunicationType,
    direction: 'outgoing' as CommunicationDirection,
    subject: '',
    content: '',
    duration: '',
    sentiment: 'neutral' as CommunicationSentiment,
    outcome: 'successful' as CommunicationOutcome,
    effectiveness: '5',
    importance: '3',
    topics: '',
    startTime: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:mm
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // 更新表单数据
  const updateFormData = (updates: Partial<typeof formData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      type: 'call',
      direction: 'outgoing',
      subject: '',
      content: '',
      duration: '',
      sentiment: 'neutral',
      outcome: 'successful',
      effectiveness: '5',
      importance: '3',
      topics: '',
      startTime: new Date().toISOString().slice(0, 16),
    });
  };

  // 处理提交
  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      const communicationData = {
        type: formData.type,
        direction: formData.direction,
        subject: formData.subject || undefined,
        content: formData.content || undefined,
        primaryContactId: contactId,
        participants: [{ 
          contactId, 
          name: contactName, 
          participationLevel: 'primary' as const 
        }],
        startTime: new Date(formData.startTime),
        duration: formData.duration ? parseInt(formData.duration) : undefined,
        sentiment: formData.sentiment,
        outcome: formData.outcome,
        effectiveness: parseInt(formData.effectiveness),
        importance: parseInt(formData.importance),
        topics: formData.topics ? formData.topics.split(',').map(t => t.trim()).filter(Boolean) : undefined,
      };

      if (initialData) {
        await updateCommunication(initialData.id, communicationData);
        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="success" variant="accent">
              <ToastTitle>沟通记录已更新</ToastTitle>
            </Toast>
          ),
        });
      } else {
        await addCommunication(communicationData);
        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="success" variant="accent">
              <ToastTitle>沟通记录已添加</ToastTitle>
            </Toast>
          ),
        });
      }

      resetForm();
      onClose();
    } catch (error) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>操作失败</ToastTitle>
            <ToastDescription>请重试</ToastDescription>
          </Toast>
        ),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理关闭
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 获取沟通类型选项
  const communicationTypes = [
    { value: 'call', label: '电话通话', icon: Phone },
    { value: 'video_call', label: '视频通话', icon: Video },
    { value: 'email', label: '邮件', icon: Mail },
    { value: 'message', label: '消息', icon: MessageCircle },
    { value: 'meeting', label: '面对面会议', icon: Users },
    { value: 'other', label: '其他', icon: MessageCircle },
  ];

  const directions = [
    { value: 'outgoing', label: '主动联系' },
    { value: 'incoming', label: '被动接收' },
    { value: 'mutual', label: '双向互动' },
  ];

  const sentiments = [
    { value: 'very_positive', label: '非常积极' },
    { value: 'positive', label: '积极' },
    { value: 'neutral', label: '中性' },
    { value: 'negative', label: '消极' },
    { value: 'very_negative', label: '非常消极' },
  ];

  const outcomes = [
    { value: 'successful', label: '成功达成' },
    { value: 'partial', label: '部分达成' },
    { value: 'unsuccessful', label: '未达成' },
    { value: 'follow_up_needed', label: '需要跟进' },
    { value: 'no_response', label: '无回应' },
    { value: 'postponed', label: '延期处理' },
  ];

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <ModalBackdrop />
      <ModalContent maxWidth="$96">
        <ModalHeader>
          <Heading size="lg">
            {initialData ? '编辑沟通记录' : '添加沟通记录'}
          </Heading>
          <ModalCloseButton>
            <StandardIcon as={X} />
          </ModalCloseButton>
        </ModalHeader>
        
        <ModalBody>
          <VStack space="lg">
            {/* 基本信息 */}
            <VStack space="md">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                基本信息
              </Text>
              
              {/* 沟通类型 */}
              <VStack space="sm">
                <Text size="sm" fontWeight="$medium">沟通类型 *</Text>
                <Select selectedValue={formData.type} onValueChange={(value) => updateFormData({ type: value as CommunicationType })}>
                  <SelectTrigger variant="outline" size="md">
                    <SelectInput placeholder="选择沟通类型" />
                    <SelectIcon mr="$3">
                      <StandardIcon as={ChevronDown} />
                    </SelectIcon>
                  </SelectTrigger>
                  <SelectPortal>
                    <SelectBackdrop />
                    <SelectContent>
                      <SelectDragIndicatorWrapper>
                        <SelectDragIndicator />
                      </SelectDragIndicatorWrapper>
                      {communicationTypes.map((type) => (
                        <SelectItem key={type.value} label={type.label} value={type.value} />
                      ))}
                    </SelectContent>
                  </SelectPortal>
                </Select>
              </VStack>

              {/* 方向 */}
              <VStack space="sm">
                <Text size="sm" fontWeight="$medium">沟通方向</Text>
                <Select selectedValue={formData.direction} onValueChange={(value) => updateFormData({ direction: value as CommunicationDirection })}>
                  <SelectTrigger variant="outline" size="md">
                    <SelectInput placeholder="选择沟通方向" />
                    <SelectIcon mr="$3">
                      <StandardIcon as={ChevronDown} />
                    </SelectIcon>
                  </SelectTrigger>
                  <SelectPortal>
                    <SelectBackdrop />
                    <SelectContent>
                      <SelectDragIndicatorWrapper>
                        <SelectDragIndicator />
                      </SelectDragIndicatorWrapper>
                      {directions.map((direction) => (
                        <SelectItem key={direction.value} label={direction.label} value={direction.value} />
                      ))}
                    </SelectContent>
                  </SelectPortal>
                </Select>
              </VStack>

              {/* 主题 */}
              <VStack space="sm">
                <Text size="sm" fontWeight="$medium">主题</Text>
                <Input variant="outline" size="md">
                  <InputField
                    placeholder="沟通主题或标题"
                    value={formData.subject}
                    onChangeText={(text) => updateFormData({ subject: text })}
                  />
                </Input>
              </VStack>

              {/* 时间和持续时间 */}
              <HStack space="md">
                <VStack space="sm" flex={1}>
                  <Text size="sm" fontWeight="$medium">开始时间</Text>
                  <Input variant="outline" size="md">
                    <InputField
                      placeholder="YYYY-MM-DD HH:mm"
                      value={formData.startTime}
                      onChangeText={(text) => updateFormData({ startTime: text })}
                    />
                  </Input>
                </VStack>
                
                {(formData.type === 'call' || formData.type === 'video_call' || formData.type === 'meeting') && (
                  <VStack space="sm" flex={1}>
                    <Text size="sm" fontWeight="$medium">持续时间（分钟）</Text>
                    <Input variant="outline" size="md">
                      <InputField
                        placeholder="30"
                        value={formData.duration}
                        onChangeText={(text) => updateFormData({ duration: text })}
                        keyboardType="numeric"
                      />
                    </Input>
                  </VStack>
                )}
              </HStack>
            </VStack>

            {/* 内容和话题 */}
            <VStack space="md">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                内容详情
              </Text>
              
              {/* 内容摘要 */}
              <VStack space="sm">
                <Text size="sm" fontWeight="$medium">内容摘要</Text>
                <Textarea h="$20">
                  <TextareaInput
                    placeholder="简要描述沟通内容..."
                    value={formData.content}
                    onChangeText={(text) => updateFormData({ content: text })}
                  />
                </Textarea>
              </VStack>

              {/* 话题标签 */}
              <VStack space="sm">
                <Text size="sm" fontWeight="$medium">话题标签</Text>
                <Input variant="outline" size="md">
                  <InputField
                    placeholder="用逗号分隔多个话题，如：工作,项目,合作"
                    value={formData.topics}
                    onChangeText={(text) => updateFormData({ topics: text })}
                  />
                </Input>
              </VStack>
            </VStack>

            {/* 评估 */}
            <VStack space="md">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                沟通评估
              </Text>
              
              <HStack space="md">
                {/* 情感 */}
                <VStack space="sm" flex={1}>
                  <Text size="sm" fontWeight="$medium">情感倾向</Text>
                  <Select selectedValue={formData.sentiment} onValueChange={(value) => updateFormData({ sentiment: value as CommunicationSentiment })}>
                    <SelectTrigger variant="outline" size="md">
                      <SelectInput placeholder="选择情感" />
                      <SelectIcon mr="$3">
                        <StandardIcon as={ChevronDown} />
                      </SelectIcon>
                    </SelectTrigger>
                    <SelectPortal>
                      <SelectBackdrop />
                      <SelectContent>
                        <SelectDragIndicatorWrapper>
                          <SelectDragIndicator />
                        </SelectDragIndicatorWrapper>
                        {sentiments.map((sentiment) => (
                          <SelectItem key={sentiment.value} label={sentiment.label} value={sentiment.value} />
                        ))}
                      </SelectContent>
                    </SelectPortal>
                  </Select>
                </VStack>

                {/* 结果 */}
                <VStack space="sm" flex={1}>
                  <Text size="sm" fontWeight="$medium">沟通结果</Text>
                  <Select selectedValue={formData.outcome} onValueChange={(value) => updateFormData({ outcome: value as CommunicationOutcome })}>
                    <SelectTrigger variant="outline" size="md">
                      <SelectInput placeholder="选择结果" />
                      <SelectIcon mr="$3">
                        <StandardIcon as={ChevronDown} />
                      </SelectIcon>
                    </SelectTrigger>
                    <SelectPortal>
                      <SelectBackdrop />
                      <SelectContent>
                        <SelectDragIndicatorWrapper>
                          <SelectDragIndicator />
                        </SelectDragIndicatorWrapper>
                        {outcomes.map((outcome) => (
                          <SelectItem key={outcome.value} label={outcome.label} value={outcome.value} />
                        ))}
                      </SelectContent>
                    </SelectPortal>
                  </Select>
                </VStack>
              </HStack>

              <HStack space="md">
                {/* 效果评分 */}
                <VStack space="sm" flex={1}>
                  <Text size="sm" fontWeight="$medium">效果评分 (1-10)</Text>
                  <Input variant="outline" size="md">
                    <InputField
                      placeholder="5"
                      value={formData.effectiveness}
                      onChangeText={(text) => updateFormData({ effectiveness: text })}
                      keyboardType="numeric"
                    />
                  </Input>
                </VStack>

                {/* 重要程度 */}
                <VStack space="sm" flex={1}>
                  <Text size="sm" fontWeight="$medium">重要程度 (1-5)</Text>
                  <Input variant="outline" size="md">
                    <InputField
                      placeholder="3"
                      value={formData.importance}
                      onChangeText={(text) => updateFormData({ importance: text })}
                      keyboardType="numeric"
                    />
                  </Input>
                </VStack>
              </HStack>
            </VStack>
          </VStack>
        </ModalBody>
        
        <ModalFooter>
          <HStack space="md" flex={1}>
            <StandardButton
              variant="outline"
              onPress={handleClose}
              flex={1}
            >
              取消
            </StandardButton>
            
            <StandardButton
              variant="solid"
              action="primary"
              onPress={handleSubmit}
              flex={1}
              isDisabled={isSubmitting || !formData.type}
            >
              {isSubmitting ? '保存中...' : (initialData ? '更新' : '添加')}
            </StandardButton>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default CommunicationForm;
