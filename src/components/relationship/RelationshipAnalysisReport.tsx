/**
 * 关系分析报告组件
 * 显示详细的关系强度分析和洞察
 */

import React from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Heading,
  Progress,
  ProgressFilledTrack,
  Badge,
  BadgeText,
  ScrollView,
} from '@gluestack-ui/themed';
import {
  TrendingUp,
  TrendingDown,
  Minus,
  AlertTriangle,
  CheckCircle,
  Clock,
  MessageCircle,
  Star,
  Users,
  Calendar,
} from 'lucide-react-native';

import { StandardIcon } from '../ui';
import { RelationshipAnalysis, RelationshipInsight } from '../../services/relationshipAnalyzer';
import { RelationshipTrendChart } from './RelationshipTrendChart';
import { CommunicationRecord } from '../../types';

interface RelationshipAnalysisReportProps {
  analysis: RelationshipAnalysis;
  contactName: string;
  communications?: CommunicationRecord[];
  showInsights?: boolean;
  showTrend?: boolean;
  compact?: boolean;
}

export const RelationshipAnalysisReport: React.FC<RelationshipAnalysisReportProps> = ({
  analysis,
  contactName,
  communications = [],
  showInsights = true,
  showTrend = true,
  compact = false,
}) => {
  const { metrics, insights } = analysis;

  // 获取趋势图标
  const getTrendIcon = (trend: number) => {
    if (trend > 10) return TrendingUp;
    if (trend < -10) return TrendingDown;
    return Minus;
  };

  // 获取趋势颜色
  const getTrendColor = (trend: number) => {
    if (trend > 10) return '$success600';
    if (trend < -10) return '$error600';
    return '$textLight500';
  };

  // 获取风险等级颜色
  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return '$error500';
      case 'medium': return '$warning500';
      case 'low': return '$success500';
      default: return '$textLight500';
    }
  };

  // 获取关系阶段颜色
  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'building': return '$success500';
      case 'stable': return '$primary500';
      case 'declining': return '$warning500';
      case 'dormant': return '$coolGray500';
      default: return '$textLight500';
    }
  };

  // 获取洞察图标
  const getInsightIcon = (insight: RelationshipInsight) => {
    switch (insight.type) {
      case 'strength': return Users;
      case 'frequency': return Calendar;
      case 'quality': return Star;
      case 'trend': return insight.level === 'positive' ? TrendingUp : TrendingDown;
      case 'risk': return AlertTriangle;
      default: return MessageCircle;
    }
  };

  // 获取洞察颜色
  const getInsightColor = (insight: RelationshipInsight) => {
    switch (insight.level) {
      case 'positive': return '$success600';
      case 'negative': return '$error600';
      case 'neutral': return '$warning600';
      default: return '$textLight600';
    }
  };

  // 渲染指标卡片
  const renderMetricCard = (
    title: string,
    value: number,
    maxValue: number = 100,
    color: string = '$primary500',
    suffix: string = '%'
  ) => (
    <VStack space="sm" flex={1}>
      <Text size="sm" fontWeight="$medium" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
        {title}
      </Text>
      <Text size="xl" fontWeight="$bold" color={color}>
        {Math.round(value)}{suffix}
      </Text>
      {!compact && (
        <Progress value={value} w="100%" h="$1" bg="$backgroundLight200" sx={{ _dark: { bg: '$backgroundDark700' } }}>
          <ProgressFilledTrack bg={color} />
        </Progress>
      )}
    </VStack>
  );

  return (
    <Box p="$4" bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark900' } }}>
      <VStack space="lg">
        {/* 标题 */}
        <HStack justifyContent="space-between" alignItems="center">
          <Heading size={compact ? "sm" : "md"}>
            与{contactName}的关系分析
          </Heading>
          <Badge
            size="sm"
            variant="solid"
            bg={getPhaseColor(metrics.relationshipPhase)}
          >
            <BadgeText>
              {metrics.relationshipPhase === 'building' ? '发展中' :
               metrics.relationshipPhase === 'stable' ? '稳定' :
               metrics.relationshipPhase === 'declining' ? '下降' :
               metrics.relationshipPhase === 'dormant' ? '休眠' : '未知'}
            </BadgeText>
          </Badge>
        </HStack>

        {/* 核心指标 */}
        <VStack space="md">
          <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
            核心指标
          </Text>
          
          <HStack space="md">
            {renderMetricCard(
              '连接强度',
              metrics.connectionStrength,
              100,
              metrics.connectionStrength >= 70 ? '$success600' : 
              metrics.connectionStrength >= 40 ? '$warning600' : '$error600'
            )}
            
            {renderMetricCard(
              '沟通质量',
              metrics.communicationQuality,
              100,
              '$primary600'
            )}
            
            {renderMetricCard(
              '互动频率',
              metrics.interactionFrequency,
              100,
              '$info600'
            )}
          </HStack>
        </VStack>

        {/* 详细指标 */}
        {!compact && (
          <VStack space="md">
            <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              详细分析
            </Text>
            
            <VStack space="sm">
              <HStack space="md">
                {renderMetricCard('最近性', metrics.recencyScore, 100, '$purple600')}
                {renderMetricCard('频率', metrics.frequencyScore, 100, '$blue600')}
              </HStack>
              
              <HStack space="md">
                {renderMetricCard('质量', metrics.qualityScore, 100, '$green600')}
                {renderMetricCard('多样性', metrics.diversityScore, 100, '$orange600')}
              </HStack>
              
              <HStack space="md">
                {renderMetricCard('互动性', metrics.mutualityScore, 100, '$pink600')}
                <VStack space="sm" flex={1}>
                  <Text size="sm" fontWeight="$medium" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                    关系趋势
                  </Text>
                  <HStack space="xs" alignItems="center">
                    <StandardIcon 
                      as={getTrendIcon(metrics.relationshipTrend)} 
                      size="sm" 
                      color={getTrendColor(metrics.relationshipTrend)} 
                    />
                    <Text size="xl" fontWeight="$bold" color={getTrendColor(metrics.relationshipTrend)}>
                      {metrics.relationshipTrend > 0 ? '+' : ''}{Math.round(metrics.relationshipTrend)}%
                    </Text>
                  </HStack>
                </VStack>
              </HStack>
            </VStack>
          </VStack>
        )}

        {/* 风险评估 */}
        <HStack justifyContent="space-between" alignItems="center">
          <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
            风险评估
          </Text>
          <Badge
            size="sm"
            variant="solid"
            bg={getRiskColor(metrics.riskLevel)}
          >
            <BadgeText>
              {metrics.riskLevel === 'high' ? '高风险' :
               metrics.riskLevel === 'medium' ? '中风险' : '低风险'}
            </BadgeText>
          </Badge>
        </HStack>

        {/* 关系趋势图表 */}
        {showTrend && !compact && communications.length > 0 && (
          <RelationshipTrendChart
            communications={communications}
            contactName={contactName}
            timeRange="quarter"
            height={150}
          />
        )}

        {/* 预测信息 */}
        {!compact && metrics.nextContactPrediction && (
          <VStack space="sm">
            <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              预测下次联系
            </Text>
            <HStack space="xs" alignItems="center">
              <StandardIcon as={Clock} size="sm" color="$textLight500" />
              <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                {metrics.nextContactPrediction.toLocaleDateString('zh-CN')}
              </Text>
            </HStack>
          </VStack>
        )}

        {/* 洞察和建议 */}
        {showInsights && insights.length > 0 && (
          <VStack space="md">
            <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              洞察和建议
            </Text>
            
            <VStack space="sm">
              {insights.slice(0, compact ? 2 : 5).map((insight, index) => {
                const Icon = getInsightIcon(insight);
                const color = getInsightColor(insight);
                
                return (
                  <Box
                    key={index}
                    p="$3"
                    bg="$backgroundLight0"
                    borderRadius="$md"
                    borderLeftWidth="$3"
                    borderLeftColor={color}
                    sx={{ _dark: { bg: '$backgroundDark800' } }}
                  >
                    <VStack space="xs">
                      <HStack space="sm" alignItems="center">
                        <StandardIcon as={Icon} size="sm" color={color} />
                        <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                          {insight.title}
                        </Text>
                        <Badge size="xs" variant="outline">
                          <BadgeText>{Math.round(insight.confidence * 100)}%</BadgeText>
                        </Badge>
                      </HStack>
                      
                      <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                        {insight.description}
                      </Text>
                      
                      {insight.recommendation && (
                        <Text size="sm" color={color} fontWeight="$medium">
                          建议: {insight.recommendation}
                        </Text>
                      )}
                    </VStack>
                  </Box>
                );
              })}
            </VStack>
          </VStack>
        )}

        {/* 数据质量指示 */}
        {!compact && (
          <HStack justifyContent="space-between" alignItems="center">
            <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
              数据质量: {Math.round(analysis.dataQuality)}%
            </Text>
            <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
              分析时间: {analysis.lastAnalyzed.toLocaleDateString('zh-CN')}
            </Text>
          </HStack>
        )}
      </VStack>
    </Box>
  );
};

export default RelationshipAnalysisReport;
