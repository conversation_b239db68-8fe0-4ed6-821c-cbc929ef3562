import React from 'react';
import { 
  Box, 
  Text, 
  Pressable, 
  Avatar, 
  AvatarFallbackText, 
  AvatarImage, 
  HStack, 
  VStack, 
  Icon,
  Heading
} from '@gluestack-ui/themed';
import { ArrowLeft, Edit, MoreVertical, Phone, Mail, MessageSquare, Calendar } from 'lucide-react-native';
import { ContactData } from '../hooks/useContactDetail';

interface ContactHeaderProps {
  contact: ContactData;
  onNavigateBack: () => void;
  onEditContact: () => void;
  onMoreOptions: () => void;
  onCall: () => void;
  onEmail: () => void;
  onMessage: () => void;
  onSchedule: () => void;
}

const ContactHeader: React.FC<ContactHeaderProps> = ({
  contact,
  onNavigateBack,
  onEditContact,
  onMoreOptions,
  onCall,
  onEmail,
  onMessage,
  onSchedule,
}) => {
  return (
    <Box
      pt={'$12'}
      pb={'$5'}
      px={'$5'}
      sx={{
        _web: {
          background: 'linear-gradient(135deg, #3498db, #2980b9)',
        },
        '@base': {
          bg: '$primary500',
        },
      }}
    >
      <HStack justifyContent="space-between" alignItems="center">
        <Pressable
          onPress={onNavigateBack}
          p={'$2'}
          borderRadius={'$full'}
          bg="rgba(255, 255, 255, 0.2)"
        >
          <Icon as={ArrowLeft} size="xl" color="white" />
        </Pressable>
        
        <HStack space="md">
          <Pressable
            onPress={onEditContact}
            p={'$2'}
            borderRadius={'$full'}
            bg="rgba(255, 255, 255, 0.2)"
          >
            <Icon as={Edit} size="lg" color="white" />
          </Pressable>
          <Pressable
            onPress={onMoreOptions}
            p={'$2'}
            borderRadius={'$full'}
            bg="rgba(255, 255, 255, 0.2)"
          >
            <Icon as={MoreVertical} size="lg" color="white" />
          </Pressable>
        </HStack>
      </HStack>
      
      <VStack alignItems="center" mt={'$5'}>
        <Avatar size="xl" borderColor="white" borderWidth={2}>
          <AvatarFallbackText>{contact.name}</AvatarFallbackText>
          <AvatarImage source={{ uri: contact.avatar }} />
        </Avatar>
        <Heading color="white" size={'2xl'} fontWeight={'$bold'} mt={'$3'}>
          {contact.name}
        </Heading>
        <Text color="rgba(255, 255, 255, 0.8)" fontSize={'$md'} mt={'$1'}>
          {contact.position} at {contact.company}
        </Text>
      </VStack>
      
      <HStack justifyContent="space-around" mt={'$5'}>
        <ActionButton icon={Phone} label="Call" onPress={onCall} />
        <ActionButton icon={Mail} label="Email" onPress={onEmail} />
        <ActionButton icon={MessageSquare} label="Message" onPress={onMessage} />
        <ActionButton icon={Calendar} label="Schedule" onPress={onSchedule} />
      </HStack>
    </Box>
  );
};

interface ActionButtonProps {
  icon: React.ElementType;
  label: string;
  onPress: () => void;
}

const ActionButton: React.FC<ActionButtonProps> = ({ icon, label, onPress }) => (
  <Pressable onPress={onPress} alignItems="center">
    <Box
      w={'$12'}
      h={'$12'}
      borderRadius={'$full'}
      bg="rgba(255, 255, 255, 0.2)"
      justifyContent="center"
      alignItems="center"
    >
      <Icon as={icon} size="xl" color="white" />
    </Box>
    <Text color="white" fontSize={'$xs'} mt={'$2'}>
      {label}
    </Text>
  </Pressable>
);



export default ContactHeader;
