import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  TouchableWithoutFeedback,
  FlatList,
  Alert,
  Platform
} from 'react-native';
import {
  Text,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription
} from '@gluestack-ui/themed';
import { useNavigation } from '@react-navigation/native';
import { HelpCircle, Search, Plus, X, Check, Tag as TagIcon, ArrowLeft, Trash2 } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAlert } from '../context/AlertContext';

// Define types for Tag structure
interface TagItem {
  id: string;
  name: string;
  color: string;
  count: number;
}

interface TagsByCategory {
  [category: string]: TagItem[];
}

// Mock tag data by categories
const INITIAL_TAGS: TagsByCategory = {
  industry: [
    { id: 'ind1', name: 'Technology', color: '#3498db', count: 24 },
    { id: 'ind2', name: 'Finance', color: '#2ecc71', count: 18 },
    { id: 'ind3', name: 'Healthcare', color: '#e74c3c', count: 12 },
    { id: 'ind4', name: 'Education', color: '#f39c12', count: 9 },
    { id: 'ind5', name: 'Manufacturing', color: '#9b59b6', count: 7 },
    { id: 'ind6', name: 'Real Estate', color: '#1abc9c', count: 5 },
    { id: 'ind7', name: 'Retail', color: '#d35400', count: 4 },
  ],
  relationship: [
    { id: 'rel1', name: 'Client', color: '#1abc9c', count: 31 },
    { id: 'rel2', name: 'Partner', color: '#d35400', count: 15 },
    { id: 'rel3', name: 'Vendor', color: '#8e44ad', count: 8 },
    { id: 'rel4', name: 'Colleague', color: '#27ae60', count: 22 },
    { id: 'rel5', name: 'Prospect', color: '#c0392b', count: 13 },
  ],
  personal: [
    { id: 'per1', name: 'Golf', color: '#16a085', count: 6 },
    { id: 'per2', name: 'Coffee', color: '#7f8c8d', count: 11 },
    { id: 'per3', name: 'Travel', color: '#f1c40f', count: 7 },
    { id: 'per4', name: 'Art', color: '#2980b9', count: 4 },
    { id: 'per5', name: 'Wine', color: '#c0392b', count: 5 },
  ],
};

// Available colors for tags
const TAG_COLORS = [
  '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
  '#1abc9c', '#d35400', '#8e44ad', '#27ae60', '#c0392b',
  '#16a085', '#7f8c8d', '#f1c40f', '#2980b9', '#34495e',
];

const TagManagementScreen = () => {
  const navigation = useNavigation();
  const toast = useToast();
  const { showAlert } = useAlert();
  
  // State for tag data
  const [tags, setTags] = useState<TagsByCategory>(INITIAL_TAGS);
  
  // State for active category
  const [activeCategory, setActiveCategory] = useState('industry');
  
  // State for search
  const [searchQuery, setSearchQuery] = useState('');

  // State for tag modal
  const [tagModalVisible, setTagModalVisible] = useState(false);
  const [editingTag, setEditingTag] = useState<TagItem | null>(null);
  const [newTagName, setNewTagName] = useState('');
  const [selectedColor, setSelectedColor] = useState(TAG_COLORS[0]);
  const [selectedCategory, setSelectedCategory] = useState('industry');
  
  // Filtered tags based on search
  const filteredTags = searchQuery.trim() === '' 
    ? tags[activeCategory]
    : Object.values(tags).flat().filter(tag => 
        tag.name.toLowerCase().includes(searchQuery.toLowerCase())
      );

  // Handle tag press
  const handleTagPress = (tag: TagItem) => {
    showAlert({
      title: `${tag.name}`,
      message: `This tag is used by ${tag.count} contacts.`,
      buttons: [
        {
          text: 'View Contacts',
          onPress: () => {
            toast.show({
              placement: "top",
              render: ({ id }: { id: string }) => {
                const toastId = "toast-" + id;
                return (
                  <Toast nativeID={toastId} action="info" variant="solid">
                    <ToastTitle>Action Placeholder</ToastTitle>
                    <ToastDescription>
                      Navigation to filtered contacts for '{tag.name}' would occur here.
                    </ToastDescription>
                  </Toast>
                );
              },
            });
          },
        },
        {
          text: 'Edit',
          onPress: () => openEditTagModal(tag),
        },
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => {}, 
        },
      ],
    });
  };

  // Open tag creation modal
  const openNewTagModal = () => {
    setEditingTag(null);
    setNewTagName('');
    setSelectedColor(TAG_COLORS[0]);
    setSelectedCategory(activeCategory);
    setTagModalVisible(true);
  };

  // Open tag editing modal
  const openEditTagModal = (tag: TagItem) => {
    setEditingTag(tag);
    setNewTagName(tag.name);
    setSelectedColor(tag.color);
    
    // Find which category the tag belongs to
    for (const [category, tagList] of Object.entries(tags)) {
      if (tagList.some(t => t.id === tag.id)) {
        setSelectedCategory(category);
        break;
      }
    }
    
    setTagModalVisible(true);
  };

  // Save new or edited tag
  const saveTag = () => {
    if (!newTagName.trim()) {
      toast.show({
        placement: "top",
        render: ({ id }: { id: string }) => (
          <Toast nativeID={`toast-${id}`} action="error" variant="accent">
            <ToastTitle>Error</ToastTitle>
            <ToastDescription>Please enter a tag name.</ToastDescription>
          </Toast>
        ),
      });
      return;
    }
    
    if (editingTag) {
      // Edit existing tag
      const updatedTags = { ...tags };
      
      // Find the category where the tag exists
      for (const category of Object.keys(updatedTags)) {
        const tagIndex = updatedTags[category].findIndex(t => t.id === editingTag.id);
        
        if (tagIndex !== -1) {
          // Remove from original category if category changed
          if (category !== selectedCategory) {
            const tagToMove = { ...updatedTags[category][tagIndex] };
            updatedTags[category] = updatedTags[category].filter(t => t.id !== editingTag.id);
            
            // Add to new category
            tagToMove.name = newTagName;
            tagToMove.color = selectedColor;
            updatedTags[selectedCategory] = [...updatedTags[selectedCategory], tagToMove];
          } else {
            // Update in the same category
            updatedTags[category][tagIndex] = {
              ...updatedTags[category][tagIndex],
              name: newTagName,
              color: selectedColor,
            };
          }
          break;
        }
      }
      
      setTags(updatedTags);
      toast.show({
        placement: "top",
        render: ({ id }: { id: string }) => (
          <Toast nativeID={`toast-${id}`} action="success" variant="accent">
            <ToastTitle>Success</ToastTitle>
            <ToastDescription>Tag updated successfully.</ToastDescription>
          </Toast>
        ),
      });
    } else {
      // Create new tag
      const newTag = {
        id: `tag-${Date.now()}`,
        name: newTagName,
        color: selectedColor,
        count: 0, // New tag has no contacts yet
      };
      
      setTags({
        ...tags,
        [selectedCategory]: [...tags[selectedCategory], newTag],
      });
      
      toast.show({
        placement: "top",
        render: ({ id }: { id: string }) => (
          <Toast nativeID={`toast-${id}`} action="success" variant="accent">
            <ToastTitle>Success</ToastTitle>
            <ToastDescription>New tag created successfully.</ToastDescription>
          </Toast>
        ),
      });
    }
    
    setTagModalVisible(false);
  };

  // Delete tag
  const deleteTag = () => {
    if (!editingTag) return;
    
    showAlert({
      title: 'Confirm Delete',
      message: `Are you sure you want to delete the "${editingTag.name}" tag? This will remove the tag from all ${editingTag.count} contacts.`,
      buttons: [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => {}, 
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Existing deletion logic remains the same
            const updatedTags = { ...tags };
            for (const category of Object.keys(updatedTags)) {
              const tagExists = updatedTags[category].some(t => t.id === editingTag.id);
              if (tagExists) {
                updatedTags[category] = updatedTags[category].filter(t => t.id !== editingTag.id);
                setTags(updatedTags);
                break;
              }
            }
            
            setTagModalVisible(false);
            toast.show({
              placement: "top",
              render: ({ id }: { id: string }) => (
                <Toast nativeID={`toast-${id}`} action="success" variant="accent">
                  <ToastTitle>Success</ToastTitle>
                  <ToastDescription>Tag deleted successfully.</ToastDescription>
                </Toast>
              ),
            });
          },
        },
      ],
    });
  };

  // Render tag item
  const renderTag = ({ item }: { item: TagItem }) => (
    <TouchableOpacity
      style={[styles.tag, { borderColor: item.color }]}
      onPress={() => handleTagPress(item)}
    >
      <View style={[styles.tagDot, { backgroundColor: item.color }]} />
      <Text style={styles.tagName}>{item.name}</Text>
      <View style={styles.tagCountContainer}>
        <Text style={styles.tagCount}>{item.count}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tag Management</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Search size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search tags..."
          placeholderTextColor="#999"
          value={searchQuery}
          onChangeText={setSearchQuery}
          clearButtonMode="while-editing"
        />
      </View>

      {/* Category Tabs */}
      {searchQuery.trim() === '' && (
        <View style={styles.categoryTabsContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.categoryTabs}>
            {Object.keys(tags).map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryTab,
                  activeCategory === category && styles.activeCategoryTab
                ]}
                onPress={() => setActiveCategory(category)}
              >
                <Text style={[
                  styles.categoryTabText,
                  activeCategory === category && styles.activeCategoryTabText
                ]}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </Text>
                {activeCategory === category && <View style={styles.categoryTabIndicator} />}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Tag List */}
      <FlatList
        data={filteredTags}
        renderItem={renderTag}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.tagListContainer}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={() => searchQuery.trim() !== '' ? (
          <Text style={styles.searchResultsText}>
            {filteredTags.length} tags found for &quot;{searchQuery}&quot;
          </Text>
        ) : null}
        ListEmptyComponent={() => (
          <View style={styles.emptyState}>
            <TagIcon size={60} color="#ccc" />
            <Text style={styles.emptyStateText}>
              {searchQuery.trim() !== '' ? 'No tags found matching your search' : 'No tags in this category yet'}
            </Text>
            <TouchableOpacity style={styles.emptyStateButton} onPress={openNewTagModal}>
              <Text style={styles.emptyStateButtonText}>Create a tag</Text>
            </TouchableOpacity>
          </View>
        )}
      />

      {/* Add Tag Button */}
      <TouchableOpacity style={styles.addButton} onPress={openNewTagModal}>
        <LinearGradient
          colors={['#4A78D9', '#2F5FE3']}
          style={styles.addButtonGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Plus size={28} color="#fff" />
        </LinearGradient>
      </TouchableOpacity>

      {/* Tag Creation/Editing Modal */}
      <Modal
        visible={tagModalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setTagModalVisible(false)}
      >
        <TouchableWithoutFeedback onPress={() => setTagModalVisible(false)}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
              <View style={styles.modalContainer}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>
                    {editingTag ? 'Edit Tag' : 'Create New Tag'}
                  </Text>
                  <TouchableOpacity onPress={() => setTagModalVisible(false)}>
                    <X size={24} color="#555" />
                  </TouchableOpacity>
                </View>

                <View style={styles.modalContent}>
                  <View style={styles.modalInputContainer}>
                    <Text style={styles.modalInputLabel}>Tag Name</Text>
                    <TextInput
                      style={styles.modalInput}
                      placeholder="Enter tag name"
                      placeholderTextColor="#999"
                      value={newTagName}
                      onChangeText={setNewTagName}
                      autoFocus
                    />
                  </View>

                  <Text style={styles.modalInputLabel}>Tag Color</Text>
                  <View style={styles.colorPickerContainer}>
                    {TAG_COLORS.map(color => (
                      <TouchableOpacity
                        key={color}
                        style={[
                          styles.colorOption,
                          { backgroundColor: color },
                          selectedColor === color && styles.selectedColorOption
                        ]}
                        onPress={() => setSelectedColor(color)}
                      >
                        {selectedColor === color && (
                          <Check size={18} color="#fff" />
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>

                  <Text style={styles.modalInputLabel}>Tag Category</Text>
                  <View style={styles.categoryPickerContainer}>
                    {Object.keys(tags).map(category => (
                      <TouchableOpacity
                        key={category}
                        style={[
                          styles.categoryOption,
                          selectedCategory === category && { backgroundColor: selectedColor }
                        ]}
                        onPress={() => setSelectedCategory(category)}
                      >
                        <Text style={[
                          styles.categoryOptionText,
                          selectedCategory === category && { color: '#fff' }
                        ]}>
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                <View style={styles.modalActions}>
                  {editingTag && (
                    <TouchableOpacity style={styles.deleteButton} onPress={deleteTag}>
                      <Trash2 size={20} color="#e74c3c" />
                      <Text style={styles.deleteButtonText}>Delete</Text>
                    </TouchableOpacity>
                  )}

                  <TouchableOpacity
                    style={[styles.saveTagButton, { backgroundColor: selectedColor }]}
                    onPress={saveTag}
                  >
                    <Text style={styles.saveTagButtonText}>
                      {editingTag ? 'Update Tag' : 'Create Tag'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  categoryTabsContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  categoryTabs: {
    paddingHorizontal: 16,
  },
  categoryTab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
    position: 'relative',
  },
  activeCategoryTab: {},
  categoryTabText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#777',
  },
  activeCategoryTabText: {
    color: '#4A78D9',
  },
  categoryTabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 16,
    right: 16,
    height: 3,
    backgroundColor: '#4A78D9',
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  tagListContainer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 80,
  },
  searchResultsText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    fontStyle: 'italic',
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.05)',
    } : {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
    }),
    elevation: 1,
  },
  tagDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 12,
  },
  tagName: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  tagCountContainer: {
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    paddingVertical: 2,
    paddingHorizontal: 8,
  },
  tagCount: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#777',
    marginTop: 12,
    marginBottom: 24,
    textAlign: 'center',
  },
  emptyStateButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#4A78D9',
    borderRadius: 8,
  },
  emptyStateButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#fff',
  },
  addButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.2)',
    } : {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 5,
    }),
    elevation: 5,
  },
  addButtonGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  modalContent: {
    padding: 16,
  },
  modalInputContainer: {
    marginBottom: 16,
  },
  modalInputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
    marginBottom: 8,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  colorPickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  colorOption: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
    marginBottom: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedColorOption: {
    borderWidth: 2,
    borderColor: '#fff',
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 0px 3px 0px rgba(0, 0, 0, 0.2)',
    } : {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.2,
      shadowRadius: 3,
    }),
    elevation: 3,
  },
  categoryPickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  categoryOption: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    marginRight: 8,
    marginBottom: 8,
  },
  categoryOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#eaeaea',
    padding: 16,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButtonText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#e74c3c',
    marginLeft: 4,
  },
  saveTagButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: '#4A78D9',
  },
  saveTagButtonText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#fff',
  },
});

export default TagManagementScreen;