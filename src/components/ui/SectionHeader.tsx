import React from 'react';
import { Heading } from '@gluestack-ui/themed';

interface SectionHeaderProps {
  title: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * 通用的区域标题组件
 * 用于在各个页面中显示一致的区域标题样式
 */
const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  size = 'md'
}) => {
  return (
    <Heading
      size={size}
      mb="$2"
      color="$textLight900"
      sx={{
        _dark: {
          color: '$textDark100'
        }
      }}
    >
      {title}
    </Heading>
  );
};

export default SectionHeader;
