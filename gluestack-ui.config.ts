import { config as defaultConfig } from '@gluestack-ui/config'; // 从 @gluestack-ui/config 导入默认配置

export const config = {
  ...defaultConfig,
  // 您可以在这里覆盖或扩展默认主题
  // 例如，覆盖默认颜色：
  // tokens: {
  //   ...defaultConfig.tokens,
  //   colors: {
  //     ...defaultConfig.tokens.colors,
  //     primary500: '#007AFF', // 您的项目主色调
  //   },
  // },
  //
  // 如果您之前有自定义的主题或令牌，应该在这里合并它们。
  // 由于我们没有找到旧的配置文件，这里先使用默认配置。
};

// 如果您有自定义组件的配置，也应该在这里导出
// export { Button } from './components/Button'; // 示例
