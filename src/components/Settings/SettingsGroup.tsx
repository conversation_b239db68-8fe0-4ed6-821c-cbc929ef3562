import React from 'react';
import {
  VStack,
  Text,
  Box
} from '@gluestack-ui/themed';

interface SettingsGroupProps {
  title: string;
  children: React.ReactNode;
  showSeparator?: boolean;
}

/**
 * 设置组组件
 * 用于组织相关的设置项，包含标题和分隔符
 */
const SettingsGroup: React.FC<SettingsGroupProps> = ({
  title,
  children,
  showSeparator = true
}) => {
  return (
    <VStack space="xs" mb="$4">
      {/* Section Header */}
      <Text 
        fontSize="$lg" 
        fontWeight="$semibold" 
        color="$textLight900"
        mt="$5"
        mb="$2"
        px="$4"
        sx={{ _dark: { color: '$textDark100' } }}
      >
        {title}
      </Text>

      {/* Settings Items */}
      <VStack>
        {children}
      </VStack>

      {/* Separator */}
      {showSeparator && (
        <Box
          h="$px"
          bg="$borderLight100"
          mx="$4"
          my="$2"
          sx={{
            _dark: {
              bg: '$borderDark800'
            }
          }}
        />
      )}
    </VStack>
  );
};

export default SettingsGroup;
