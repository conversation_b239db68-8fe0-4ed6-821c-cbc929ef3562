import React, { useState } from 'react';
import {
  Platform,
} from 'react-native';
import {
  Modal as GSModal,
  ModalBackdrop,
  ModalContent,
  <PERSON>dal<PERSON>eader,
  ModalCloseButton,
  ModalBody,
  Modal<PERSON>ooter,
  Heading,
  Text,
  Input,
  InputField,
  Textarea,
  TextareaInput,
  Button,
  ButtonText,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  Icon,
  VStack,
  HStack,
  ScrollView,
  Pressable,
  Box
} from '@gluestack-ui/themed';
import { ChevronDownIcon, XCircle } from 'lucide-react-native';
import { useAlert } from '../../../context/AlertContext';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker'; // For date selection
import { Interaction } from '../hooks/useContactDetail'; // Import Interaction type
import { useTranslation } from 'react-i18next';

interface InteractionFormModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (interactionData: Omit<Interaction, 'id'>) => void;
}

const InteractionFormModal: React.FC<InteractionFormModalProps> = ({ visible, onClose, onSave }) => {
  const { showAlert } = useAlert();
  const { t } = useTranslation();
  const [interactionType, setInteractionType] = useState<'meeting' | 'email' | 'call' | 'note'>('meeting');
  const [title, setTitle] = useState('');
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [participants, setParticipants] = useState(''); // Comma-separated for simplicity
  const [description, setDescription] = useState('');
  const [outcome, setOutcome] = useState('');

  const handleSave = () => {
    if (!title.trim() || !description.trim()) {
      showAlert({
        title: t('common.validationError'),
        message: t('interactionForm.alert.fillRequiredFields'),
        buttons: [{ text: t('common.ok') }],
      });
      return;
    }
    const newInteractionData: Omit<Interaction, 'id'> = {
      type: interactionType,
      title,
      date: date.toISOString().split('T')[0], // Store date as YYYY-MM-DD string
      participants: participants.split(',').map(p => p.trim()).filter(p => p), // Basic parsing
      description,
      outcome,
    };
    onSave(newInteractionData);
    // Reset form and close
    setTitle('');
    setInteractionType('meeting');
    setDate(new Date());
    setParticipants('');
    setDescription('');
    setOutcome('');
    onClose();
  };

  const onDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    const currentDate = selectedDate || date;
    setShowDatePicker(Platform.OS === 'ios'); // iOS requires manual dismissal for inline
    if (Platform.OS !== 'ios') {
        setShowDatePicker(false); // Android closes automatically or if using 'dialog'
    }
    setDate(currentDate);
  };

  const interactionTypes = [
    { label: t('interactionForm.types.meeting'), value: 'meeting' as const },
    { label: t('interactionForm.types.email'), value: 'email' as const },
    { label: t('interactionForm.types.call'), value: 'call' as const },
    { label: t('interactionForm.types.note'), value: 'note' as const },
  ];

  return (
    <GSModal isOpen={visible} onClose={onClose} finalFocusRef={undefined}>
      <ModalBackdrop />
      <ModalContent w={'$full'} maxW={'$lg'} m={'$4'}>
        <ModalHeader>
          <Heading size="lg">{t('interactionForm.title')}</Heading>
          <ModalCloseButton>
            <Icon as={XCircle} />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody>
          <ScrollView w={'$full'}>
            <VStack space="md">
              <VStack space="xs">
                <Text size="sm" fontWeight="$medium">{t('interactionForm.type')}</Text>
                <Select selectedValue={interactionType} onValueChange={(value) => setInteractionType(value as 'meeting' | 'email' | 'call' | 'note')}>
                  <SelectTrigger variant="outline" size="md">
                    <SelectInput placeholder={t('interactionForm.typePlaceholder')} />
                    <SelectIcon mr={'$3'} as={ChevronDownIcon} />
                  </SelectTrigger>
                  <SelectPortal>
                    <SelectBackdrop />
                    <SelectContent>
                      <SelectDragIndicatorWrapper>
                        <SelectDragIndicator />
                      </SelectDragIndicatorWrapper>
                      {interactionTypes.map(type => (
                        <SelectItem key={type.value} label={type.label} value={type.value} />
                      ))}
                    </SelectContent>
                  </SelectPortal>
                </Select>
              </VStack>

              <VStack space="xs">
                <Text size="sm" fontWeight="$medium">{t('interactionForm.interactionTitle')}</Text>
                <Input>
                  <InputField
                    placeholder={t('interactionForm.interactionTitlePlaceholder')}
                    value={title}
                    onChangeText={setTitle}
                  />
                </Input>
              </VStack>

              <VStack space="xs">
                <Text size="sm" fontWeight="$medium">{t('interactionForm.date')}</Text>
                <Pressable onPress={() => setShowDatePicker(true)} borderWidth={1} borderColor='$borderLight300' borderRadius='$sm' p='$3'>
                  <Text>{date.toLocaleDateString()}</Text>
                </Pressable>
                {showDatePicker && (
                  <DateTimePicker
                    testID="dateTimePicker"
                    value={date}
                    mode="date"
                    display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                    onChange={onDateChange}
                  />
                )}
              </VStack>

              <VStack space="xs">
                <Text size="sm" fontWeight="$medium">{t('interactionForm.participants')}</Text>
                <Input>
                  <InputField
                    placeholder={t('interactionForm.participantsPlaceholder')}
                    value={participants}
                    onChangeText={setParticipants}
                  />
                </Input>
              </VStack>

              <VStack space="xs">
                <Text size="sm" fontWeight="$medium">{t('interactionForm.description')}</Text>
                <Textarea>
                  <TextareaInput
                    placeholder={t('interactionForm.descriptionPlaceholder')}
                    value={description}
                    onChangeText={setDescription}
                    h={100} // Approximate height for 3 lines
                  />
                </Textarea>
              </VStack>

              <VStack space="xs">
                <Text size="sm" fontWeight="$medium">{t('interactionForm.outcome')}</Text>
                <Input>
                  <InputField
                    placeholder={t('interactionForm.outcomePlaceholder')}
                    value={outcome}
                    onChangeText={setOutcome}
                  />
                </Input>
              </VStack>
            </VStack>
          </ScrollView>
        </ModalBody>
        <ModalFooter>
          <HStack space="sm">
            <Button variant="outline" action="secondary" onPress={onClose}>
              <ButtonText>{t('common.cancel')}</ButtonText>
            </Button>
            <Button onPress={handleSave}>
              <ButtonText>{t('common.save')}</ButtonText>
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </GSModal>
  );
};

export default InteractionFormModal;
