/**
 * 沟通历史组件
 * 显示联系人的沟通记录和分析
 */

import React, { useState, useEffect } from 'react';
import { FlatList, RefreshControl } from 'react-native';
import {
  Box,
  Text,
  VStack,
  HStack,
  Heading,
  Pressable,
  Badge,
  BadgeText,
  ScrollView,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';
import {
  Phone,
  Video,
  Mail,
  MessageCircle,
  Users,
  Calendar,
  TrendingUp,
  TrendingDown,
  Minus,
  Plus,
  MoreVertical,
  Clock,
  Star,
} from 'lucide-react-native';

import { StandardButton, StandardIcon, CircularButton } from '../ui';
import { useCommunicationStore, useCommunicationSelectors } from '../../store/communicationStore';
import { 
  CommunicationRecord, 
  CommunicationType, 
  CommunicationSentiment,
  CommunicationAnalysis 
} from '../../types';

interface CommunicationHistoryProps {
  contactId: string;
  onAddCommunication?: () => void;
  showAnalysis?: boolean;
  maxItems?: number;
}

export const CommunicationHistory: React.FC<CommunicationHistoryProps> = ({
  contactId,
  onAddCommunication,
  showAnalysis = true,
  maxItems,
}) => {
  const toast = useToast();
  
  // Store状态
  const {
    isLoading,
    error,
    analyzeContactCommunications,
    recordQuickCall,
    recordQuickMessage,
    recordQuickMeeting,
  } = useCommunicationStore();

  const communications = useCommunicationSelectors.useContactCommunications(contactId);
  const analysis = useCommunicationSelectors.useContactAnalysis(contactId);
  const stats = useCommunicationSelectors.useContactStats(contactId);

  // 本地状态
  const [refreshing, setRefreshing] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);

  // 初始化分析
  useEffect(() => {
    if (contactId && !analysis) {
      analyzeContactCommunications(contactId);
    }
  }, [contactId, analysis]);

  // 处理刷新
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await analyzeContactCommunications(contactId);
    } catch (error) {
      console.error('刷新分析失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // 获取沟通类型图标
  const getCommunicationTypeIcon = (type: CommunicationType) => {
    switch (type) {
      case 'call': return Phone;
      case 'video_call': return Video;
      case 'email': return Mail;
      case 'message': return MessageCircle;
      case 'meeting': return Users;
      default: return MessageCircle;
    }
  };

  // 获取沟通类型颜色
  const getCommunicationTypeColor = (type: CommunicationType) => {
    switch (type) {
      case 'call': return '$blue500';
      case 'video_call': return '$purple500';
      case 'email': return '$orange500';
      case 'message': return '$green500';
      case 'meeting': return '$red500';
      default: return '$gray500';
    }
  };

  // 获取情感颜色
  const getSentimentColor = (sentiment?: CommunicationSentiment) => {
    switch (sentiment) {
      case 'very_positive': return '$green600';
      case 'positive': return '$green500';
      case 'neutral': return '$gray500';
      case 'negative': return '$orange500';
      case 'very_negative': return '$red500';
      default: return '$gray400';
    }
  };

  // 获取情感文本
  const getSentimentText = (sentiment?: CommunicationSentiment) => {
    switch (sentiment) {
      case 'very_positive': return '非常积极';
      case 'positive': return '积极';
      case 'neutral': return '中性';
      case 'negative': return '消极';
      case 'very_negative': return '非常消极';
      default: return '未知';
    }
  };

  // 快速操作
  const handleQuickCall = async () => {
    try {
      await recordQuickCall(contactId, 5); // 默认5分钟通话
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>通话记录已添加</ToastTitle>
          </Toast>
        ),
      });
    } catch (error) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>添加失败</ToastTitle>
            <ToastDescription>请重试</ToastDescription>
          </Toast>
        ),
      });
    }
  };

  const handleQuickMessage = async () => {
    try {
      await recordQuickMessage(contactId, '简短交流');
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>消息记录已添加</ToastTitle>
          </Toast>
        ),
      });
    } catch (error) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>添加失败</ToastTitle>
            <ToastDescription>请重试</ToastDescription>
          </Toast>
        ),
      });
    }
  };

  // 渲染分析概览
  const renderAnalysisOverview = () => {
    if (!showAnalysis || !analysis) return null;

    return (
      <Box p="$4" bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark900' } }}>
        <VStack space="md">
          <Heading size="sm">沟通分析</Heading>
          
          {/* 关系指标 */}
          <HStack space="md" justifyContent="space-between">
            <VStack alignItems="center" flex={1}>
              <Text size="xl" fontWeight="$bold" color="$primary600">
                {Math.round(analysis.relationshipStrength)}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                关系强度
              </Text>
            </VStack>
            <VStack alignItems="center" flex={1}>
              <Text size="xl" fontWeight="$bold" color="$success600">
                {Math.round(analysis.successRate)}%
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                成功率
              </Text>
            </VStack>
            <VStack alignItems="center" flex={1}>
              <Text size="xl" fontWeight="$bold" color="$warning600">
                {analysis.averageEffectiveness.toFixed(1)}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                平均效果
              </Text>
            </VStack>
            <VStack alignItems="center" flex={1}>
              <Text size="xl" fontWeight="$bold" color="$info600">
                {stats.totalCommunications}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                总次数
              </Text>
            </VStack>
          </HStack>

          {/* 偏好的沟通方式 */}
          {analysis.preferredTypes.length > 0 && (
            <VStack space="sm">
              <Text size="sm" fontWeight="$medium">偏好的沟通方式</Text>
              <HStack space="sm">
                {analysis.preferredTypes.slice(0, 3).map((type, index) => {
                  const Icon = getCommunicationTypeIcon(type);
                  return (
                    <HStack key={type} space="xs" alignItems="center">
                      <StandardIcon 
                        as={Icon} 
                        size="sm" 
                        color={getCommunicationTypeColor(type)} 
                      />
                      <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                        {type === 'call' ? '电话' :
                         type === 'video_call' ? '视频' :
                         type === 'email' ? '邮件' :
                         type === 'message' ? '消息' :
                         type === 'meeting' ? '会议' : type}
                      </Text>
                    </HStack>
                  );
                })}
              </HStack>
            </VStack>
          )}

          {/* 洞察和建议 */}
          {analysis.insights.length > 0 && (
            <VStack space="sm">
              <Text size="sm" fontWeight="$medium">分析洞察</Text>
              {analysis.insights.slice(0, 2).map((insight, index) => (
                <Text key={index} size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  • {insight}
                </Text>
              ))}
            </VStack>
          )}
        </VStack>
      </Box>
    );
  };

  // 渲染沟通记录项
  const renderCommunicationItem = ({ item: comm }: { item: CommunicationRecord }) => {
    const Icon = getCommunicationTypeIcon(comm.type);
    const typeColor = getCommunicationTypeColor(comm.type);
    const sentimentColor = getSentimentColor(comm.sentiment);

    return (
      <Pressable
        p="$4"
        borderBottomWidth="$1"
        borderBottomColor="$borderLight200"
        sx={{ _dark: { borderBottomColor: '$borderDark700' } }}
        onPress={() => {
          // TODO: 导航到沟通记录详情
          console.log('Navigate to communication detail:', comm.id);
        }}
      >
        <HStack space="md" alignItems="flex-start">
          {/* 类型图标 */}
          <Box
            w="$10"
            h="$10"
            borderRadius="$full"
            bg={`${typeColor}20`}
            justifyContent="center"
            alignItems="center"
          >
            <StandardIcon as={Icon} size="sm" color={typeColor} />
          </Box>

          {/* 内容 */}
          <VStack flex={1} space="xs">
            <HStack justifyContent="space-between" alignItems="flex-start">
              <VStack flex={1} space="xs">
                <HStack space="sm" alignItems="center">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    {comm.subject || 
                     (comm.type === 'call' ? '电话通话' :
                      comm.type === 'video_call' ? '视频通话' :
                      comm.type === 'email' ? '邮件沟通' :
                      comm.type === 'message' ? '消息交流' :
                      comm.type === 'meeting' ? '面对面会议' : '其他沟通')}
                  </Text>
                  
                  {/* 方向指示 */}
                  <StandardIcon 
                    as={comm.direction === 'outgoing' ? TrendingUp : 
                        comm.direction === 'incoming' ? TrendingDown : Minus} 
                    size="xs" 
                    color="$textLight500" 
                  />
                </HStack>

                {comm.content && (
                  <Text 
                    size="sm" 
                    color="$textLight600" 
                    sx={{ _dark: { color: '$textDark400' } }}
                    numberOfLines={2}
                  >
                    {comm.content}
                  </Text>
                )}

                {/* 话题标签 */}
                {comm.topics && comm.topics.length > 0 && (
                  <HStack space="xs" flexWrap="wrap">
                    {comm.topics.slice(0, 3).map((topic, index) => (
                      <Badge key={index} size="sm" variant="outline">
                        <BadgeText>{topic}</BadgeText>
                      </Badge>
                    ))}
                  </HStack>
                )}
              </VStack>

              {/* 情感和效果 */}
              <VStack alignItems="flex-end" space="xs">
                {comm.sentiment && (
                  <Badge size="sm" variant="solid" bg={sentimentColor}>
                    <BadgeText color="white">
                      {getSentimentText(comm.sentiment)}
                    </BadgeText>
                  </Badge>
                )}
                
                {comm.effectiveness && (
                  <HStack space="xs" alignItems="center">
                    <StandardIcon as={Star} size="xs" color="$warning500" />
                    <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                      {comm.effectiveness}/10
                    </Text>
                  </HStack>
                )}
              </VStack>
            </HStack>

            {/* 时间和持续时间 */}
            <HStack justifyContent="space-between" alignItems="center">
              <HStack space="xs" alignItems="center">
                <StandardIcon as={Clock} size="xs" color="$textLight500" />
                <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                  {comm.startTime.toLocaleDateString()} {comm.startTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
                </Text>
              </HStack>

              {comm.duration && (
                <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                  {comm.duration}分钟
                </Text>
              )}
            </HStack>
          </VStack>
        </HStack>
      </Pressable>
    );
  };

  // 渲染快速操作
  const renderQuickActions = () => (
    <Box p="$4" borderBottomWidth="$1" borderBottomColor="$borderLight200" sx={{ _dark: { borderBottomColor: '$borderDark700' } }}>
      <VStack space="md">
        <HStack justifyContent="space-between" alignItems="center">
          <Text fontWeight="$medium">快速记录</Text>
          <Pressable onPress={() => setShowQuickActions(!showQuickActions)}>
            <StandardIcon as={showQuickActions ? Minus : Plus} size="sm" />
          </Pressable>
        </HStack>
        
        {showQuickActions && (
          <HStack space="md" justifyContent="space-around">
            <VStack alignItems="center" space="xs">
              <CircularButton
                icon={Phone}
                size="md"
                variant="outline"
                action="primary"
                onPress={handleQuickCall}
              />
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                通话
              </Text>
            </VStack>
            
            <VStack alignItems="center" space="xs">
              <CircularButton
                icon={MessageCircle}
                size="md"
                variant="outline"
                action="primary"
                onPress={handleQuickMessage}
              />
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                消息
              </Text>
            </VStack>
            
            <VStack alignItems="center" space="xs">
              <CircularButton
                icon={Plus}
                size="md"
                variant="outline"
                action="primary"
                onPress={onAddCommunication}
              />
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                详细
              </Text>
            </VStack>
          </HStack>
        )}
      </VStack>
    </Box>
  );

  const displayCommunications = maxItems 
    ? communications.slice(0, maxItems)
    : communications;

  return (
    <Box flex={1}>
      {/* 分析概览 */}
      {renderAnalysisOverview()}

      {/* 快速操作 */}
      {renderQuickActions()}

      {/* 沟通记录列表 */}
      {displayCommunications.length > 0 ? (
        <FlatList
          data={displayCommunications}
          renderItem={renderCommunicationItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#4A78D9']}
              tintColor="#4A78D9"
            />
          }
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <Box flex={1} justifyContent="center" alignItems="center" p="$8">
          <VStack space="lg" alignItems="center">
            <StandardIcon as={MessageCircle} size="4xl" color="$textLight400" />
            <VStack space="sm" alignItems="center">
              <Text 
                textAlign="center" 
                fontWeight="$medium" 
                color="$textLight500" 
                sx={{ _dark: { color: '$textDark500' } }}
              >
                还没有沟通记录
              </Text>
              <Text 
                textAlign="center" 
                color="$textLight400" 
                sx={{ _dark: { color: '$textDark600' } }}
              >
                开始记录与此联系人的沟通历史
              </Text>
            </VStack>
            <StandardButton
              variant="outline"
              onPress={onAddCommunication}
              leftIcon={Plus}
            >
              添加沟通记录
            </StandardButton>
          </VStack>
        </Box>
      )}
    </Box>
  );
};

export default CommunicationHistory;
