import React from 'react';
import {
  VStack,
  HStack,
  ScrollView,
  Pressable,
  Icon,
  Text,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription
} from '@gluestack-ui/themed';
import { Platform, Linking } from 'react-native';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import {
  ChevronLeft,
  Bell,
  Clock,
  Mail,
  MapPin,
  RefreshCw,
  Trash2,
  Users,
  HelpCircle,
  ShieldCheck,
  FileText,
  Key,
  Database
} from 'lucide-react-native';

// Import our new components - 临时注释掉避免无限循环
// import {
//   ProfileSection,
//   StorageSection,
//   SettingItem,
//   SettingsGroup,
//   VersionInfo
// } from '../components/Settings';
// import { ConfirmationModal } from '../components/ui';
// 临时注释掉store导入，避免无限循环
// import { useSettingsSelectors, useSettingsActions, useUISelectors, useUIActions } from '../store';
import { ICON_COLORS, SUPPORTED_LANGUAGES, APP_VERSION, COPYRIGHT, EXTERNAL_LINKS } from '../constants/settingsConfig';
import type { RootStackParamList } from '../types';



const SettingsScreen = () => {
  const toast = useToast();
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  // Global state - 临时注释掉store使用，避免无限循环
  // const settings = useSettingsSelectors.useNotificationSettings();
  // const appSettings = useSettingsSelectors.useAppSettings();
  // const securitySettings = useSettingsSelectors.useSecuritySettings();
  // const userProfile = useSettingsSelectors.useUserProfile();
  // const language = useSettingsSelectors.useLanguage();

  // const { updateSetting, resetSettings } = useSettingsActions();
  // const { showConfirmationModal } = useUIActions();
  // const { isVisible: isModalVisible, config: modalConfig } = useUISelectors.useConfirmationModal();

  // 使用静态数据进行测试
  const settings = {
    notificationsEnabled: true,
    emailNotifications: true,
    reminderNotifications: true,
    networkUpdates: false
  };
  const appSettings = {
    darkMode: false,
    aiSuggestions: true,
    autoTagging: true,
    dataSync: true
  };
  const securitySettings = {
    biometricLogin: false,
    locationTracking: true
  };
  const userProfile = {
    name: 'Demo User',
    email: '<EMAIL>',
    avatar: null
  };
  const language = 'en';

  const updateSetting = () => {};
  const resetSettings = () => {};
  const showConfirmationModal = () => {};
  const isModalVisible = false;
  const modalConfig = null;

  const currentLanguage = language || i18n.language;

  // User data from store with storage info
  const user = {
    ...userProfile,
    storageUsed: "2.3 GB",
    storageTotal: "10 GB",
  };

  const handleChangeLanguage = (langCode: string) => {
    if (langCode !== currentLanguage) {
      i18n.changeLanguage(langCode);
    }
  };



  const handleResetPreferences = () => {
    showConfirmationModal({
      title: 'Reset Preferences',
      message: 'Are you sure you want to reset all preferences to default settings?',
      confirmText: 'Reset',
      cancelText: 'Cancel',
      confirmAction: 'destructive',
      onConfirm: () => {
        resetSettings();
        toast.show({
          placement: "top",
          render: ({ id }: { id: string }) => (
            <Toast nativeID={`toast-${id}`} action="success" variant="accent">
              <ToastTitle>Success</ToastTitle>
              <ToastDescription>Preferences have been reset.</ToastDescription>
            </Toast>
          ),
        });
      }
    });
  };

  const handleExportData = () => {
    showConfirmationModal({
      title: 'Export Data',
      message: 'Your data will be prepared for export. You\'ll receive an email with your data file once it\'s ready.',
      confirmText: 'Export',
      cancelText: 'Cancel',
      confirmAction: 'default',
      onConfirm: () => {
        toast.show({
          placement: "top",
          render: ({ id }: { id: string }) => (
            <Toast nativeID={`toast-${id}`} action="success" variant="accent">
              <ToastTitle>Success</ToastTitle>
              <ToastDescription>Your data export has started and will be sent to your email.</ToastDescription>
            </Toast>
          ),
        });
      }
    });
  };

  const handleDeleteAccount = () => {
    showConfirmationModal({
      title: "Delete Account",
      message: "This action cannot be undone. All your data will be permanently deleted.",
      confirmText: "Delete Account",
      cancelText: "Cancel",
      confirmAction: 'destructive',
      onConfirm: () => {
        // Open the second, final confirmation modal
        showConfirmationModal({
          title: "Confirm Final Deletion",
          message: "Are you absolutely sure you want to permanently delete your account? This cannot be undone.",
          confirmText: "Yes, Delete My Account",
          cancelText: "Cancel",
          confirmAction: 'destructive',
          onConfirm: () => {
            console.log('Account deleted');
            toast.show({
              placement: "top",
              render: ({ id }: { id: string }) => (
                <Toast nativeID={`toast-${id}`} action="success" variant="accent">
                  <ToastTitle>Account Deleted</ToastTitle>
                  <ToastDescription>Your account has been successfully deleted.</ToastDescription>
                </Toast>
              ),
            });
          },
        });
      },
    });
  };

  const openExternalLink = (url: string) => {
    Linking.openURL(url).catch(err => {
      console.error('Failed to open URL:', err);
      toast.show({
        placement: "top",
        render: ({ id }: { id: string }) => (
          <Toast nativeID={`toast-${id}`} action="error" variant="accent">
            <ToastTitle>Error</ToastTitle>
            <ToastDescription>Failed to open link.</ToastDescription>
          </Toast>
        ),
      });
    });
  };



  return (
    <VStack
      flex={1}
      bg="$backgroundLight50"
      sx={{ _dark: { bg: '$backgroundDark900' } }}
    >
      {/* Header */}
      <HStack
        alignItems="center"
        justifyContent="space-between"
        bg="$backgroundLight0"
        pt={Platform.OS === 'ios' ? '$12' : '$5'}
        pb="$3"
        px="$4"
        borderBottomWidth={1}
        borderBottomColor="$borderLight200"
        sx={{
          _dark: {
            bg: '$backgroundDark900',
            borderBottomColor: '$borderDark700'
          }
        }}
      >
        <Pressable onPress={() => navigation.goBack()} p="$2">
          <Icon as={ChevronLeft} size="lg" color="$textLight900"
            sx={{ _dark: { color: '$textDark100' } }} />
        </Pressable>

        <Text
          fontSize="$lg"
          fontWeight="$semibold"
          color="$textLight900"
          sx={{ _dark: { color: '$textDark100' } }}
        >
          {t('settings.title')}
        </Text>

        <VStack w="$10" />
      </HStack>

      <ScrollView flex={1} p="$4">
        {/* 临时简化的设置界面，避免无限循环 */}

        {/* 用户信息 */}
        <VStack space="md" mb="$6">
          <Text fontSize="$lg" fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
            用户信息
          </Text>
          <VStack space="sm" p="$4" bg="$backgroundLight0" borderRadius="$lg" sx={{ _dark: { bg: '$backgroundDark800' } }}>
            <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              {userProfile.name}
            </Text>
            <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
              {userProfile.email}
            </Text>
          </VStack>
        </VStack>

        {/* 应用设置 */}
        <VStack space="md" mb="$6">
          <Text fontSize="$lg" fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
            应用设置
          </Text>
          <VStack space="sm" p="$4" bg="$backgroundLight0" borderRadius="$lg" sx={{ _dark: { bg: '$backgroundDark800' } }}>
            <HStack justifyContent="space-between" alignItems="center">
              <VStack>
                <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  深色模式
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  切换浅色和深色主题
                </Text>
              </VStack>
              <Text size="sm" color="$primary600">
                {appSettings.darkMode ? '开启' : '关闭'}
              </Text>
            </HStack>

            <HStack justifyContent="space-between" alignItems="center">
              <VStack>
                <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  AI建议
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  获取个性化推荐
                </Text>
              </VStack>
              <Text size="sm" color="$primary600">
                {appSettings.aiSuggestions ? '开启' : '关闭'}
              </Text>
            </HStack>

            <HStack justifyContent="space-between" alignItems="center">
              <VStack>
                <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  自动标签
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  根据信息自动标记联系人
                </Text>
              </VStack>
              <Text size="sm" color="$primary600">
                {appSettings.autoTagging ? '开启' : '关闭'}
              </Text>
            </HStack>
          </VStack>
        </VStack>

        {/* 通知设置 */}
        <VStack space="md" mb="$6">
          <Text fontSize="$lg" fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
            通知设置
          </Text>
          <VStack space="sm" p="$4" bg="$backgroundLight0" borderRadius="$lg" sx={{ _dark: { bg: '$backgroundDark800' } }}>
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                推送通知
              </Text>
              <Text size="sm" color="$primary600">
                {settings.notificationsEnabled ? '开启' : '关闭'}
              </Text>
            </HStack>

            <HStack justifyContent="space-between" alignItems="center">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                邮件通知
              </Text>
              <Text size="sm" color="$primary600">
                {settings.emailNotifications ? '开启' : '关闭'}
              </Text>
            </HStack>

            <HStack justifyContent="space-between" alignItems="center">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                提醒通知
              </Text>
              <Text size="sm" color="$primary600">
                {settings.reminderNotifications ? '开启' : '关闭'}
              </Text>
            </HStack>
          </VStack>
        </VStack>

        {/* 版本信息 */}
        <VStack space="md">
          <Text fontSize="$lg" fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
            关于
          </Text>
          <VStack space="sm" p="$4" bg="$backgroundLight0" borderRadius="$lg" sx={{ _dark: { bg: '$backgroundDark800' } }}>
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                版本
              </Text>
              <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                1.0.0
              </Text>
            </HStack>

            <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }} textAlign="center" mt="$4">
              © 2024 RelationHub. All rights reserved.
            </Text>
          </VStack>
        </VStack>
      </ScrollView>

      {/* Confirmation Modal - 临时注释掉避免无限循环 */}
      {/* {modalConfig && (
        <ConfirmationModal
          isVisible={isModalVisible}
          onClose={() => {}} // 临时注释掉store使用
          title={modalConfig.title}
          message={modalConfig.message}
          confirmText={modalConfig.confirmText}
          cancelText={modalConfig.cancelText}
          confirmAction={modalConfig.confirmAction}
          onConfirm={modalConfig.onConfirm}
        />
      )} */}
    </VStack>
  );
};



export default SettingsScreen;