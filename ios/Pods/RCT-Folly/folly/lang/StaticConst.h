/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

namespace folly {

//  StaticConst
//
//  A template for defining ODR-usable constexpr instances. Safe from ODR
//  violations and initialization-order problems.

template <typename T>
struct StaticConst {
  static constexpr T value{};
};

template <typename T>
constexpr T StaticConst<T>::value;

} // namespace folly
