/**
 * 关系添加/编辑表单组件
 * 用于创建和编辑联系人之间的关系
 */

import React, { useState } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Input,
  InputField,
  Textarea,
  TextareaInput,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Heading,
  Switch,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';
import {
  X,
  Users,
  ChevronDown,
} from 'lucide-react-native';

import { StandardButton, StandardIcon } from '../ui';
import { useRelationshipStore } from '../../store/relationshipStore';
import { 
  ContactRelationshipType,
  Contact
} from '../../types';

interface RelationshipFormProps {
  isOpen: boolean;
  onClose: () => void;
  fromContact: Contact;
  toContact?: Contact;
  contacts: Contact[];
  initialData?: any;
}

export const RelationshipForm: React.FC<RelationshipFormProps> = ({
  isOpen,
  onClose,
  fromContact,
  toContact,
  contacts,
  initialData,
}) => {
  const toast = useToast();
  const { addRelationship, updateRelationship } = useRelationshipStore();

  // 表单状态
  const [formData, setFormData] = useState({
    toContactId: toContact?.id || '',
    relationshipType: 'mutual_friend' as ContactRelationshipType,
    strength: '70',
    notes: '',
    isActive: true,
    establishedDate: new Date().toISOString().slice(0, 10), // YYYY-MM-DD
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // 更新表单数据
  const updateFormData = (updates: Partial<typeof formData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      toContactId: toContact?.id || '',
      relationshipType: 'mutual_friend',
      strength: '70',
      notes: '',
      isActive: true,
      establishedDate: new Date().toISOString().slice(0, 10),
    });
  };

  // 处理提交
  const handleSubmit = async () => {
    if (!formData.toContactId) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>请选择联系人</ToastTitle>
          </Toast>
        ),
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const relationshipData = {
        fromContactId: fromContact.id,
        toContactId: formData.toContactId,
        relationshipType: formData.relationshipType,
        strength: parseInt(formData.strength),
        notes: formData.notes || undefined,
        isActive: formData.isActive,
        establishedDate: formData.establishedDate ? new Date(formData.establishedDate) : undefined,
      };

      if (initialData) {
        await updateRelationship(initialData.id, relationshipData);
        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="success" variant="accent">
              <ToastTitle>关系已更新</ToastTitle>
            </Toast>
          ),
        });
      } else {
        await addRelationship(relationshipData);
        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="success" variant="accent">
              <ToastTitle>关系已添加</ToastTitle>
            </Toast>
          ),
        });
      }

      resetForm();
      onClose();
    } catch (error) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>操作失败</ToastTitle>
            <ToastDescription>请重试</ToastDescription>
          </Toast>
        ),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理关闭
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 获取关系类型选项
  const relationshipTypes = [
    { value: 'introduced_by', label: '我介绍的' },
    { value: 'introduced_to', label: '被介绍给我' },
    { value: 'mutual_friend', label: '共同朋友' },
    { value: 'colleague', label: '同事关系' },
    { value: 'family_friend', label: '家庭朋友' },
    { value: 'business_partner', label: '商业伙伴' },
    { value: 'mentor_mentee', label: '师生关系' },
    { value: 'client_vendor', label: '客户关系' },
    { value: 'other', label: '其他关系' },
  ];

  // 过滤可选联系人（排除自己）
  const availableContacts = contacts.filter(c => c.id !== fromContact.id);

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <ModalBackdrop />
      <ModalContent maxWidth="$96">
        <ModalHeader>
          <VStack space="sm" alignItems="center">
            <StandardIcon as={Users} size="lg" color="$primary600" />
            <Heading size="lg">
              {initialData ? '编辑关系' : '添加关系'}
            </Heading>
            <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }} textAlign="center">
              建立 {fromContact.name} 与其他联系人的关系
            </Text>
          </VStack>
          <ModalCloseButton>
            <StandardIcon as={X} />
          </ModalCloseButton>
        </ModalHeader>
        
        <ModalBody>
          <VStack space="lg">
            {/* 联系人选择 */}
            <VStack space="sm">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                选择联系人 *
              </Text>
              <Select 
                selectedValue={formData.toContactId} 
                onValueChange={(value) => updateFormData({ toContactId: value })}
                isDisabled={!!toContact} // 如果已指定联系人则禁用
              >
                <SelectTrigger variant="outline" size="md">
                  <SelectInput placeholder="选择要建立关系的联系人" />
                  <SelectIcon mr="$3">
                    <StandardIcon as={ChevronDown} />
                  </SelectIcon>
                </SelectTrigger>
                <SelectPortal>
                  <SelectBackdrop />
                  <SelectContent>
                    <SelectDragIndicatorWrapper>
                      <SelectDragIndicator />
                    </SelectDragIndicatorWrapper>
                    {availableContacts.map((contact) => (
                      <SelectItem 
                        key={contact.id} 
                        label={`${contact.name}${contact.company ? ` (${contact.company})` : ''}`} 
                        value={contact.id} 
                      />
                    ))}
                  </SelectContent>
                </SelectPortal>
              </Select>
            </VStack>

            {/* 关系类型 */}
            <VStack space="sm">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                关系类型 *
              </Text>
              <Select 
                selectedValue={formData.relationshipType} 
                onValueChange={(value) => updateFormData({ relationshipType: value as ContactRelationshipType })}
              >
                <SelectTrigger variant="outline" size="md">
                  <SelectInput placeholder="选择关系类型" />
                  <SelectIcon mr="$3">
                    <StandardIcon as={ChevronDown} />
                  </SelectIcon>
                </SelectTrigger>
                <SelectPortal>
                  <SelectBackdrop />
                  <SelectContent>
                    <SelectDragIndicatorWrapper>
                      <SelectDragIndicator />
                    </SelectDragIndicatorWrapper>
                    {relationshipTypes.map((type) => (
                      <SelectItem key={type.value} label={type.label} value={type.value} />
                    ))}
                  </SelectContent>
                </SelectPortal>
              </Select>
            </VStack>

            {/* 关系强度 */}
            <VStack space="sm">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                关系强度 (0-100)
              </Text>
              <Input variant="outline" size="md">
                <InputField
                  placeholder="70"
                  value={formData.strength}
                  onChangeText={(text) => updateFormData({ strength: text })}
                  keyboardType="numeric"
                />
              </Input>
              <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                数值越高表示关系越密切
              </Text>
            </VStack>

            {/* 建立日期 */}
            <VStack space="sm">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                建立日期
              </Text>
              <Input variant="outline" size="md">
                <InputField
                  placeholder="YYYY-MM-DD"
                  value={formData.establishedDate}
                  onChangeText={(text) => updateFormData({ establishedDate: text })}
                />
              </Input>
            </VStack>

            {/* 备注 */}
            <VStack space="sm">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                备注
              </Text>
              <Textarea h="$20">
                <TextareaInput
                  placeholder="关于这个关系的额外信息..."
                  value={formData.notes}
                  onChangeText={(text) => updateFormData({ notes: text })}
                />
              </Textarea>
            </VStack>

            {/* 激活状态 */}
            <HStack justifyContent="space-between" alignItems="center">
              <VStack>
                <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  激活状态
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  是否在关系图谱中显示此关系
                </Text>
              </VStack>
              <Switch
                value={formData.isActive}
                onValueChange={(value) => updateFormData({ isActive: value })}
                size="md"
                trackColor={{ false: '$coolGray300', true: '$primary600' }}
                thumbColor={formData.isActive ? '$primary600' : '$coolGray400'}
              />
            </HStack>
          </VStack>
        </ModalBody>
        
        <ModalFooter>
          <HStack space="md" flex={1}>
            <StandardButton
              variant="outline"
              onPress={handleClose}
              flex={1}
            >
              取消
            </StandardButton>
            
            <StandardButton
              variant="solid"
              action="primary"
              onPress={handleSubmit}
              flex={1}
              isDisabled={isSubmitting || !formData.toContactId || !formData.relationshipType}
            >
              {isSubmitting ? '保存中...' : (initialData ? '更新' : '添加')}
            </StandardButton>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default RelationshipForm;
