// Store types and interfaces
import {
  Contact as BaseContact,
  ContactGroup,
  ContactMethod,
  Address,
  SocialLink,
  ImportantDate,
  CustomField,
  RelationshipType,
  ContactPriority
} from '../types';

// 使用基础Contact类型，但确保向后兼容
export interface Contact extends BaseContact {
  // 向后兼容的简化字段
  tags: string[]; // 简化为字符串数组，而不是Tag对象数组
}

// 创建联系人的输入数据类型
export interface CreateContactInput {
  name: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  company?: string;
  position?: string;
  notes?: string;
  tags?: string[];
  avatar?: string;
  isFavorite?: boolean;
  groupIds?: string[];
  relationshipType?: RelationshipType;
  priority?: ContactPriority;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  position: string;
  notes: string;
  tags: string[];
  avatar?: string;
}

export interface SettingsState {
  // Notifications
  notificationsEnabled: boolean;
  emailNotifications: boolean;
  reminderNotifications: boolean;
  networkUpdates: boolean;
  
  // App Settings
  dataSync: boolean;
  darkMode: boolean;
  aiSuggestions: boolean;
  autoTagging: boolean;
  
  // Security
  biometricLogin: boolean;
  locationTracking: boolean;
  
  // Language & Locale
  language: string;
  
  // User Profile
  userProfile: {
    name: string;
    email: string;
    avatar: string;
    plan: string;
  };
}

export interface AppState {
  // Loading states
  isLoading: boolean;
  isContactsLoading: boolean;
  isSettingsLoading: boolean;
  
  // Error states
  error: string | null;
  contactsError: string | null;
  settingsError: string | null;
  
  // Network state
  isOnline: boolean;
  
  // Authentication
  isAuthenticated: boolean;
  
  // App info
  version: string;
  buildNumber: string;
}

export interface UIState {
  // Modal states
  isConfirmationModalVisible: boolean;
  confirmationModalConfig: {
    title: string;
    message: string;
    confirmText: string;
    cancelText: string;
    confirmAction: 'default' | 'destructive';
    onConfirm: () => void;
  } | null;
  
  // Navigation state
  activeTab: string;
  
  // Search state
  searchQuery: string;
  searchFilters: {
    tags: string[];
    isFavorite?: boolean;
  };
  
  // Form states
  isFormDirty: boolean;
  formErrors: Record<string, string>;
}

export interface ContactsState {
  contacts: Contact[];
  filteredContacts: Contact[];
  selectedContact: Contact | null;
  searchQuery: string;
  selectedTags: string[];
  availableTags: string[];
  sortBy: 'name' | 'createdAt' | 'updatedAt' | 'priority' | 'lastContactDate';
  sortOrder: 'asc' | 'desc';
  isLoading: boolean;
  error: string | null;

  // 分组相关状态
  selectedGroupId: string | null;
  groupFilter: 'all' | 'favorites' | 'recent' | string; // 'all', 'favorites', 'recent', 或具体分组ID

  // 高级过滤
  relationshipFilter: RelationshipType | null;
  priorityFilter: ContactPriority | null;

  // 视图设置
  viewMode: 'list' | 'grid' | 'card';
  showArchived: boolean;
}

// Store action types
export interface SettingsActions {
  updateSetting: <K extends keyof SettingsState>(key: K, value: SettingsState[K]) => void;
  resetSettings: () => void;
  updateUserProfile: (profile: Partial<SettingsState['userProfile']>) => void;
  toggleNotifications: (enabled: boolean) => void;
  setLanguage: (language: string) => void;
}

export interface ContactsActions {
  // CRUD operations
  addContact: (contact: CreateContactInput) => void;
  updateContact: (id: string, updates: Partial<Contact>) => void;
  deleteContact: (id: string) => void;
  toggleFavorite: (id: string) => void;
  archiveContact: (id: string) => void;
  unarchiveContact: (id: string) => void;

  // 联系人字段管理
  addPhoneNumber: (contactId: string, phone: ContactMethod) => void;
  updatePhoneNumber: (contactId: string, phoneId: string, updates: Partial<ContactMethod>) => void;
  removePhoneNumber: (contactId: string, phoneId: string) => void;

  addEmailAddress: (contactId: string, email: ContactMethod) => void;
  updateEmailAddress: (contactId: string, emailId: string, updates: Partial<ContactMethod>) => void;
  removeEmailAddress: (contactId: string, emailId: string) => void;

  addAddress: (contactId: string, address: Address) => void;
  updateAddress: (contactId: string, addressId: string, updates: Partial<Address>) => void;
  removeAddress: (contactId: string, addressId: string) => void;

  addSocialLink: (contactId: string, socialLink: SocialLink) => void;
  updateSocialLink: (contactId: string, linkId: string, updates: Partial<SocialLink>) => void;
  removeSocialLink: (contactId: string, linkId: string) => void;

  addImportantDate: (contactId: string, date: ImportantDate) => void;
  updateImportantDate: (contactId: string, dateId: string, updates: Partial<ImportantDate>) => void;
  removeImportantDate: (contactId: string, dateId: string) => void;

  addCustomField: (contactId: string, field: CustomField) => void;
  updateCustomField: (contactId: string, fieldId: string, updates: Partial<CustomField>) => void;
  removeCustomField: (contactId: string, fieldId: string) => void;

  // Search and filter
  setSearchQuery: (query: string) => void;
  setSelectedTags: (tags: string[]) => void;
  setSortBy: (sortBy: ContactsState['sortBy']) => void;
  setSortOrder: (order: ContactsState['sortOrder']) => void;

  // 分组和过滤
  setSelectedGroup: (groupId: string | null) => void;
  setGroupFilter: (filter: ContactsState['groupFilter']) => void;
  setRelationshipFilter: (relationship: RelationshipType | null) => void;
  setPriorityFilter: (priority: ContactPriority | null) => void;

  // 分组操作
  addContactToGroup: (contactId: string, groupId: string) => void;
  removeContactFromGroup: (contactId: string, groupId: string) => void;
  moveContactToGroup: (contactId: string, fromGroupId: string, toGroupId: string) => void;

  // 视图设置
  setViewMode: (mode: ContactsState['viewMode']) => void;
  setShowArchived: (show: boolean) => void;

  // Selection
  setSelectedContact: (contact: Contact | null) => void;

  // Bulk operations
  deleteMultipleContacts: (ids: string[]) => void;
  addTagToMultiple: (ids: string[], tag: string) => void;
  addToGroupMultiple: (ids: string[], groupId: string) => void;
  removeFromGroupMultiple: (ids: string[], groupId: string) => void;

  // 交互记录
  updateLastContactDate: (contactId: string, date?: Date) => void;
  setNextFollowUpDate: (contactId: string, date: Date) => void;
  incrementInteractionCount: (contactId: string) => void;

  // Data management
  loadContacts: () => Promise<void>;
  syncContacts: () => Promise<void>;
  exportContacts: () => Promise<string>;
  importContacts: (data: Contact[]) => Promise<void>;

  // 清理和重置
  clearFilters: () => void;
  resetContactsState: () => void;
}

export interface AppActions {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setOnlineStatus: (isOnline: boolean) => void;
  setAuthenticated: (isAuthenticated: boolean) => void;
  clearErrors: () => void;
}

export interface UIActions {
  showConfirmationModal: (config: UIState['confirmationModalConfig']) => void;
  hideConfirmationModal: () => void;
  setActiveTab: (tab: string) => void;
  setSearchQuery: (query: string) => void;
  setSearchFilters: (filters: UIState['searchFilters']) => void;
  setFormDirty: (isDirty: boolean) => void;
  setFormErrors: (errors: Record<string, string>) => void;
  clearFormErrors: () => void;
}

// Combined store types
export type SettingsStore = SettingsState & SettingsActions;
export type ContactsStore = ContactsState & ContactsActions;
export type AppStore = AppState & AppActions;
export type UIStore = UIState & UIActions;
