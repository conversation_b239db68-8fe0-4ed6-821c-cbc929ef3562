import { useState, useMemo } from 'react';
import { Alert } from 'react-native';
import { Tag } from 'lucide-react-native';
import { RouteProp, useNavigation, useRoute, NavigationProp } from '@react-navigation/native'; // Added NavigationProp
import { TagFormData } from '../components/TagModal'; // Import TagFormData
import { useAlert } from '../../../context/AlertContext'; // 导入 useAlert

// Define interfaces for better type safety
export interface Tag {
  id: string;
  name: string;
  type: string;
  color: string;
  projectId?: string;
  projectName?: string;
  opportunityId?: string;
  opportunityName?: string;
}

export interface ActionItem {
  id: string;
  description: string;
  assignedTo?: string;
  dueDate?: string;
  isCompleted?: boolean;
}

export interface Interaction {
  id: string;
  type: 'meeting' | 'email' | 'call' | 'note' | 'milestone' | 'personal_event'; // Example types
  title: string;
  date: string | Date; // Should be ISO string or Date object
  description: string; // Can be used for notes
  participants?: string[];
  outcome?: string;
  // Fields for detailed meeting information (PRD 3.4.1)
  agenda?: string;
  meetingSummary?: string;
  actionItems?: ActionItem[];
  // Fields for detailed communication attributes (PRD 3.4.2)
  contentClassification?: 'business' | 'personal' | 'follow-up' | 'logistics';
  sentiment?: 'positive' | 'neutral' | 'negative';
  userEvaluation?: 'productive' | 'unproductive' | 'needs_review';
}

export interface Reminder {
  id: string;
  title: string;
  dueDate: string;
  priority: 'high' | 'medium' | 'low';
  isCompleted: boolean;
}

export interface SocialLink {
  id: string;
  type: 'linkedin' | 'twitter' | 'github' | 'website' | 'other';
  url: string;
  username?: string;
  icon?: string; 
}

export interface Connection {
  id: string;
  name: string;
  position: string;
  avatar: string;
  relationship: string;
}

export interface ContactData {
  id: string;
  name: string;
  position: string;
  company: string;
  companyDetails?: {
    website?: string;
    websiteHasPendingUpdate?: boolean;
    size?: string;
    industry?: string;
    isVerified?: boolean;
  };
  avatar: string;
  email: string;
  emailHasPendingUpdate?: boolean;
  phone: string;
  phoneHasPendingUpdate?: boolean;
  location: string;
  lastContact: string;
  notes: string | null;
  tags: Tag[];
  interactions: Interaction[];
  reminders: Reminder[];
  connections: Connection[];
  socialLinks?: SocialLink[];
}

// Define a type for the navigation prop for type safety with navigate
type RootStackParamList = {
  ContactDetail: { contactId: string; contactName: string };
  NetworkVisualScreen: { contactId: string };
  // Add other screen names and their params here if needed
};

type ContactDetailNavigationProp = NavigationProp<RootStackParamList, 'ContactDetail'>;

const MOCK_CONTACTS: ContactData[] = [
  {
    id: '1',
    name: 'Sarah Chen',
    position: 'Product Manager',
    company: 'TechCorp Industries',
    companyDetails: {
      website: 'https://techcorp.example.com',
      websiteHasPendingUpdate: true,
      size: '501-1,000 employees',
      industry: 'Software Development',
      isVerified: true,
    },
    avatar: 'https://api.a0.dev/assets/image?text=SC&aspect=1:1&seed=123',
    email: '<EMAIL>',
    emailHasPendingUpdate: true,
    phone: '+****************',
    phoneHasPendingUpdate: false,
    location: 'San Francisco, CA',
    lastContact: '2023-06-05',
    notes: 'Met at TechConf 2023. Interested in collaboration on AI solutions.',
    tags: [
      { id: '1', name: 'Technology', type: 'industry', color: '#3498db' },
      { id: '2', name: 'Potential Client', type: 'relationship', color: '#2ecc71', opportunityId: 'opp123', opportunityName: 'Q3 Major Deal' },
      { id: '3', name: 'High Priority', type: 'custom', color: '#e74c3c' },
      { id: '4', name: 'Project Alpha Lead', type: 'project', color: '#9b59b6', projectId: 'projAbc', projectName: 'Project Alpha' },
    ],
    interactions: [
      {
        id: '1',
        type: 'meeting',
        title: 'Introduction Meeting',
        agenda: '1. Introductions\n2. Overview of Project Alpha\n3. Discuss potential collaboration areas\n4. Next steps',
        meetingSummary: 'Productive initial discussion. Sarah is enthusiastic about Project Alpha and sees clear alignment. Agreed to follow up with a proposal.',
        actionItems: [
          { id: 'ai1', description: 'Draft collaboration proposal for Project Alpha', assignedTo: 'Me', dueDate: '2023-07-15', isCompleted: false },
          { id: 'ai2', description: 'Sarah to share internal research document on AI integration challenges', assignedTo: 'Sarah Chen', dueDate: '2023-07-12', isCompleted: true },
        ],
        date: '2023-05-15',
        description: 'Discussed potential partnership opportunities.'
      },
      {
        id: '2',
        type: 'email',
        title: 'Follow-up Email',
        date: '2023-05-20',
        description: 'Sent project proposal and pricing details.',
        contentClassification: 'business',
        sentiment: 'positive',
        userEvaluation: 'productive'
      },
      {
        id: '3',
        type: 'call',
        title: 'Conference Call',
        date: '2023-06-05',
        description: 'Quick call to discuss the proposal.',
        contentClassification: 'follow-up',
        sentiment: 'neutral',
        outcome: 'Follow-up scheduled for next week.',
      },
      {
        id: 'interaction-sarah-4',
        type: 'milestone',
        title: 'Promotion to Senior VP',
        date: '2024-05-20',
        description: 'Sarah was promoted to Senior Vice President of Marketing. Sent a congratulatory message.',
      },
      {
        id: 'interaction-sarah-5',
        type: 'personal_event',
        title: 'Happy Birthday!',
        date: '2024-04-22',
        description: 'Sent a birthday wish via LinkedIn.',
      },
    ],
    reminders: [
      {
        id: '1',
        title: 'Send contract draft',
        dueDate: '2023-06-15',
        priority: 'high',
        isCompleted: false,
      },
      {
        id: '2',
        title: 'Schedule demo with engineering team',
        dueDate: '2023-06-20',
        priority: 'medium',
        isCompleted: true,
      }
    ],
    connections: [
      {
        id: 'c1',
        name: 'John Doe',
        position: 'CEO, Innovate Solutions',
        avatar: 'https://api.a0.dev/assets/image?text=JD&aspect=1:1&seed=456',
        relationship: 'Introduced by Jane Smith'
      },
      {
        id: 'c2',
        name: 'Alice Brown',
        position: 'Lead Developer, TechCorp Industries',
        avatar: 'https://api.a0.dev/assets/image?text=AB&aspect=1:1&seed=789',
        relationship: 'Colleague'
      }
    ],
    socialLinks: [
      {
        id: 'sl1',
        type: 'linkedin',
        url: 'https://linkedin.com/in/sarahchen',
        username: 'sarahchen',
        icon: 'linkedin'
      },
      {
        id: 'sl2',
        type: 'twitter',
        url: 'https://twitter.com/sarahchen_pm',
        username: '@sarahchen_pm',
        icon: 'twitter-square'
      },
      {
        id: 'sl3',
        type: 'github',
        url: 'https://github.com/sarahchen-techcorp',
        username: 'sarahchen-techcorp',
        icon: 'github-square'
      }
    ]
  },
  // Add more mock contacts if needed for testing different scenarios
];

// Define the types for your screen parameters
export type ContactDetailRouteParamList = {
  ContactDetail: { contactId: string };
  // Add other screens and their params if this hook/screen navigates elsewhere
};

// Define the type for the route prop specific to the ContactDetail screen
export type ContactDetailScreenRouteProp = RouteProp<ContactDetailRouteParamList, 'ContactDetail'>;

export const useContactDetail = (route: ContactDetailScreenRouteProp) => {
  const { showAlert } = useAlert(); // 获取 showAlert 函数 
  const navigation = useNavigation<NavigationProp<any>>();
  // const route = useRoute<ContactDetailScreenRouteProp>(); // Removed duplicate declaration, route is a prop
  const [activeTab, setActiveTab] = useState('profile');
  
  // Modal States
  const [isTagModalVisible, setTagModalVisible] = useState(false);
  const [isInteractionModalVisible, setInteractionModalVisible] = useState(false);
  const [isNotesModalVisible, setNotesModalVisible] = useState(false);
  const [isReminderModalVisible, setReminderModalVisible] = useState(false);

  // Data States
  const [selectedTag, setSelectedTag] = useState<Tag | null>(null);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [editingReminder, setEditingReminder] = useState<Reminder | null>(null);
  const [editingNotes, setEditingNotes] = useState('');

  // Filter States for Interactions
  const [interactionFilterType, setInteractionFilterType] = useState<Interaction['type'] | 'all'>('all');
  const [interactionFilterDateRange, setInteractionFilterDateRange] = useState<{ startDate: Date | null; endDate: Date | null }>({ startDate: null, endDate: null });
  const [interactionSearchText, setInteractionSearchText] = useState<string>('');

  const [contactData, setContactData] = useState<ContactData>(() => {
    const contactId = route.params?.contactId || '1'; // Default to '1' if no ID passed
    const initialContact = MOCK_CONTACTS.find(c => c.id === contactId) || MOCK_CONTACTS[0];
    return JSON.parse(JSON.stringify(initialContact)); // Deep copy
  });

  // Memoized filtered interactions
  const filteredInteractions = useMemo(() => {
    if (!contactData || !contactData.interactions) return [];
    let tempInteractions = [...contactData.interactions];
    if (interactionFilterType !== 'all') {
      tempInteractions = tempInteractions.filter(interaction => interaction.type === interactionFilterType);
    }
    const { startDate, endDate } = interactionFilterDateRange;
    if (startDate) {
      const startOfDay = new Date(startDate);
      startOfDay.setHours(0, 0, 0, 0);
      tempInteractions = tempInteractions.filter(interaction => new Date(interaction.date) >= startOfDay);
    }
    if (endDate) {
      const endOfDay = new Date(endDate);
      endOfDay.setHours(23, 59, 59, 999);
      tempInteractions = tempInteractions.filter(interaction => new Date(interaction.date) <= endOfDay);
    }
    if (interactionSearchText.trim() !== '') {
      const searchTextLower = interactionSearchText.toLowerCase();
      tempInteractions = tempInteractions.filter(interaction => 
        interaction.title?.toLowerCase().includes(searchTextLower) ||
        interaction.description?.toLowerCase().includes(searchTextLower) ||
        interaction.outcome?.toLowerCase().includes(searchTextLower) ||
        interaction.participants?.some(p => p.toLowerCase().includes(searchTextLower))
      );
    }
    return tempInteractions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [contactData, interactionFilterType, interactionFilterDateRange, interactionSearchText]);

  // --- Notes Modal Logic ---
  const handleEditNotes = () => {
    if (contactData) {
      setEditingNotes(contactData.notes || '');
      setNotesModalVisible(true);
    }
  };

  const saveNotesFromModal = (newNotes: string) => {
    setContactData(prev => ({ ...prev, notes: newNotes } as ContactData));
    // await api.updateContactNotes(contact.id, newNotes);
    
    showAlert({
      title: 'Notes Updated',
      message: 'Your notes have been saved.',
      buttons: [{ text: 'OK', onPress: () => {} }],
    });
    closeNotesModal();
  };

  const closeNotesModal = () => {
    setNotesModalVisible(false);
  };

  // --- Reminder Modal Logic ---
  const handleAddReminder = () => {
    setEditingReminder(null); // For creating a new reminder
    setReminderModalVisible(true);
  };

  const saveReminderFromModal = (reminderData: { title: string; dueDate?: string; priority?: 'high' | 'medium' | 'low' }) => {
    const newReminder: Reminder = {
      id: Date.now().toString(),
      title: reminderData.title,
      dueDate: reminderData.dueDate || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      priority: reminderData.priority || 'medium',
      isCompleted: false, // Corrected from 'completed'
    };
    setContactData(prev => {
      if (!prev) return prev;
      const updatedReminders = [...prev.reminders, newReminder].sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
      return { ...prev, reminders: updatedReminders } as ContactData;
    });
    setReminderModalVisible(false);
    showAlert({
      title: 'Reminder Added',
      message: `${newReminder.title} has been set.`,
      buttons: [{ text: 'OK', onPress: () => {} }],
    });
  };

  const handleEditReminder = (reminder: Reminder) => {
    setEditingReminder(reminder);
    setReminderModalVisible(true);
  };

  const closeReminderModal = () => {
    setReminderModalVisible(false);
  };

  // --- Other Handlers ---
  const handleCompleteReminder = (reminderId: string, reminderTitle: string) => {
    setContactData(prev => {
      if (!prev) return prev;
      const updatedReminders = prev.reminders.map(r =>
        r.id === reminderId ? { ...r, isCompleted: !r.isCompleted } : r
      );
      return { ...prev, reminders: updatedReminders } as ContactData;
    });
    showAlert({
      title: 'Reminder Updated',
      message: `Reminder "${reminderTitle}" status changed.`,
      buttons: [{ text: 'OK', onPress: () => {} }],
    });
  };

  const handleAddTag = () => {
    setSelectedTag(null); // For creating a new tag
    setTagModalVisible(true);
  };

  const handleTagPress = (tag: Tag) => {
    setSelectedTag(tag); // For editing an existing tag
    setTagModalVisible(true);
  };

  // --- Tag Management Functions ---
  const openCreateTagModal = () => {
    setEditingTag(null);
    setTagModalVisible(true);
  };

  const openEditTagModal = (tag: Tag) => {
    setEditingTag(tag);
    setTagModalVisible(true);
  };

  const closeTagModal = () => {
    setTagModalVisible(false);
    setEditingTag(null);
  };

  const handleSaveTag = (tagData: TagFormData & { id?: string }) => {
    if (!contactData) return;

    let updatedTags: Tag[];
    if (tagData.id) { // Edit mode
      updatedTags = contactData.tags.map(t => 
        t.id === tagData.id ? { ...t, ...tagData } : t
      );
      showAlert({
        title: 'Tag Updated',
        message: `Tag "${tagData.name}" has been updated.`,
        buttons: [{ text: 'OK', onPress: () => {} }],
      });
    } else { // Create mode
      const newTag: Tag = {
        ...tagData,
        id: Date.now().toString(), // Simple unique ID for client-side
        // Ensure all required fields from Tag interface are present
        // projectId, projectName, opportunityId, opportunityName might come from tagData or be undefined
      };
      updatedTags = [...contactData.tags, newTag];
      showAlert({
        title: 'Tag Created',
        message: `Tag "${tagData.name}" has been created.`,
        buttons: [{ text: 'OK', onPress: () => {} }],
      });
    }
    setContactData(prev => ({ ...prev!, tags: updatedTags })); // Use non-null assertion for prev
    closeTagModal();
  };

  const handleDeleteTag = (tagId: string) => {
    if (!contactData) return;
    const tagToDelete = contactData.tags.find(t => t.id === tagId);
    if (!tagToDelete) return;

    const updatedTags = contactData.tags.filter(t => t.id !== tagId);
    setContactData(prev => ({ ...prev!, tags: updatedTags })); // Use non-null assertion for prev
    showAlert({
      title: 'Tag Deleted',
      message: `Tag "${tagToDelete.name}" has been deleted.`,
      buttons: [{ text: 'OK', onPress: () => {} }],
    });
    closeTagModal(); // Close modal after deletion, assuming it was open for this tag
  };

  // Placeholder for future Interaction Management functions
  const handleLogInteraction = () => console.log('Log Interaction');
  const handleEditInteraction = (interactionId: string) => console.log('Edit Interaction:', interactionId);
  const handleDeleteInteraction = (interactionId: string) => console.log('Delete Interaction:', interactionId);

  // --- Interaction Modal Logic ---
  const openInteractionModal = () => setInteractionModalVisible(true);
  const closeInteractionModal = () => setInteractionModalVisible(false);

  const handleTriggerAddInteraction = () => {
    openInteractionModal();
  };

  const handleSaveNewInteraction = (newInteractionData: Omit<Interaction, 'id'>) => {
    const newId = String(Date.now() + Math.random());
    const interactionToAdd: Interaction = {
      id: newId,
      ...newInteractionData,
    };
    setContactData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        interactions: [interactionToAdd, ...prev.interactions], // Add to beginning and sort if needed
      } as ContactData;
    });
    showAlert({
      title: 'Interaction Logged',
      message: `Interaction "${interactionToAdd.title}" has been added.`,
      buttons: [{ text: 'OK', onPress: () => {} }],
    });
    closeInteractionModal();
  };

  const handleViewInteractionDetail = (interactionId: string, interactionTitle: string) => {
    showAlert({
      title: 'View Interaction',
      message: `Details for "${interactionTitle}" (ID: ${interactionId}) will appear here.`,
      buttons: [{ text: 'OK', onPress: () => {} }],
    });
  };

  // --- Navigation Handlers ---
  const handleViewConnection = (connectionId: string, connectionName: string) => {
    navigation.navigate('ContactDetail', { contactId: connectionId, contactName: connectionName });
  };

  const handleViewNetworkFromContact = () => {
    if (contactData) {
      navigation.navigate('NetworkVisualScreen', { contactId: contactData.id });
    }
  };

  return {
    navigation,
    activeTab,
    setActiveTab,
    
    // Contact Data
    contact: contactData,
    
    // Notes Modal
    isNotesModalVisible,
    editingNotes,
    handleEditNotes, // Opens modal
    saveNotesFromModal,
    closeNotesModal,
    
    // Reminder Modal
    isReminderModalVisible,
    handleAddReminder, // Opens modal
    saveReminderFromModal,
    closeReminderModal,
    handleCompleteReminder,
    editingReminder, // Added state
    handleEditReminder, // Added handler
    
    // Tag Modal
    isTagModalVisible,
    editingTag,
    openCreateTagModal,
    openEditTagModal,
    closeTagModal,
    handleSaveTag,
    handleDeleteTag,
    handleTagPress, // Added missing export

    // Interaction Modal
    isInteractionModalVisible,
    openInteractionModal, // Specific open action
    closeInteractionModal, // Specific close action
    handleTriggerAddInteraction, // Semantic action to add interaction
    handleSaveNewInteraction,
    handleViewInteractionDetail,

    // Interaction Filters
    filteredInteractions,
    interactionFilterType,
    setInteractionFilterType,
    interactionFilterDateRange,
    setInteractionFilterDateRange,
    interactionSearchText,
    setInteractionSearchText,

    // Navigation actions
    handleViewConnection,
    handleViewNetworkFromContact,
  };
};
