# ContactDetailScreen Refactor Tasks

- **重构状态:** ToDo
- **主要区域/视图:**
    - **1. 顶部导航/操作区 (Header)**
        - **描述:** 包含返回按钮、联系人姓名 (或 \"Contact Detail\")、编辑按钮、更多操作按钮。
        - **现有主要组件:** `View`, `TouchableOpacity`, `Ionicons`, `Text`.
        - **Gluestack UI 迁移建议:**
            - `View` (header container) -> `HStack` or `Box` with `justifyContent=\"space-between\"`, `alignItems=\"center\"`.
            - `TouchableOpacity` (back, edit, more) -> `Pressable` or `Button` (icon only or text, `variant=\"link\"` or `solid`).
            - `Ionicons` -> `lucide-react-native` icons (e.g., `ChevronLeft`, `Edit3`, `MoreVertical`).
            - `Text` (contact name) -> `Heading`.
        - **设计规范参考:** 页面顶部标题与内容间距, 主色 (`#007AFF`).

    - **2. 联系人核心信息区 (Profile Summary)**
        - **描述:** 显示联系人头像、姓名、公司、职位。
        - **现有主要组件:** `View`, `Image` (avatar), `Text` (name, company, position).
        - **Gluestack UI 迁移建议:**
            - `View` (container) -> `VStack` or `Box` with `alignItems=\"center\"`, `p=\"$4\"`.
            - `Image` (avatar) -> `Avatar` component (`size=\"xl\"` or `2xl\"`).
            - `Text` (name) -> `Heading` (`size=\"lg\"` or `xl\"`).
            - `Text` (company, position) -> `Text` (`size=\"md\"`).
        - **设计规范参考:** 头像尺寸, 文本层级和样式.

    - **3. Tab导航区域 (Tab Navigation)**
        - **描述:** \"Profile\", \"Interactions\", \"Network\" 三个Tab。
        - **现有主要组件:** `View` (tab container), `TouchableOpacity` (tab item), `Text` (tab label).
        - **Gluestack UI 迁移建议:**
            - `View` (tab container) -> `Tabs` component.
                - `Tabs.TabList` -> `HStack`.
                - `TouchableOpacity` (tab item) -> `Tabs.Tab`.
                - `Text` (tab label) -> `Tabs.TabTitle`.
            - Active tab styling handled by `Tabs` component props.
        - **设计规范参考:** Tab 样式 (active/inactive), 字体, 边框.

    - **4. Tab内容区域 - Profile Tab (`renderProfileTab`)**
        - **描述:** 显示详细的联系人信息 (电话、邮件、位置、社交媒体链接、标签、备注等)。
        - **现有主要组件:** `ScrollView`, `View` (section, item row), `Text` (label, value), `Ionicons`, `TouchableOpacity` (for actions like call, email, edit notes, manage tags).
        - **Gluestack UI 迁移建议:**
            - `ScrollView` -> `ScrollView`.
            - `View` (section) -> `Box` or `VStack` with `p=\"$3\"`, `mb=\"$3\"`.
            - `View` (item row) -> `HStack` with `alignItems=\"center\"`, `mb=\"$2\"`.
            - `Ionicons` -> `lucide-react-native` icons.
            - `Text` (label) -> `Text` (`fontWeight=\"$semibold\"` or `color=\"$textLight500\"`).
            - `Text` (value) -> `Text`.
            - `TouchableOpacity` (actions) -> `Button` (`variant=\"link\"` or `outline`) or `Pressable`.
            - Tags display: `HStack` of `Badge` components.
            - Notes section: `Text` or `Textarea` (if editable here).
        - **设计规范参考:** 列表项样式, 图标与文本间距, 标签样式.

    - **5. Tab内容区域 - Interactions Tab (`renderInteractionsTab`)**
        - **描述:** 显示与此联系人的互动记录列表 (会议、电话、邮件)。
        - **现有主要组件:** `FlatList`, `View` (item container), `Text` (interaction type, date, summary), `Ionicons`.
        - **Gluestack UI 迁移建议:**
            - `FlatList` -> `FlatList`.
            - `View` (item container) -> `Box` or `HStack` styled as a card/list item.
            - `Ionicons` -> `lucide-react-native` icon based on interaction type.
            - `Text` -> `Text` with appropriate styling.
            - \"Add Interaction\" button -> `Button` (`variant=\"solid\"`).
        - **设计规范参考:** 列表项样式, 图标, 文本.

    - **6. Tab内容区域 - Network Tab (`renderNetworkTab`)**
        - **描述:** 可视化此联系人在其关系网络中的位置和连接。
        - **现有主要组件:** Custom graph rendering logic (possibly using `react-native-svg` or a library).
        - **Gluestack UI 迁移建议:**
            - The core graph visualization will likely remain custom.
            - UI elements around it (e.g., filters, zoom controls if any) can use `gluestack-ui` components like `Button`, `Select`.
        - **设计规范参考:** N/A directly for graph, but surrounding controls should match.

    - **7. 模态框 (Modals - `TagModal`, `InteractionFormModal`, `NotesEditModal`, `ReminderModal`)**
        - **描述:** 用于管理标签、添加/编辑互动、编辑备注、设置提醒。
        - **现有主要组件:** `Modal`, `View` (modal content), `TextInput`, `Button`, `DateTimePicker`.
        - **Gluestack UI 迁移建议:**
            - `Modal` -> `Modal` component from `gluestack-ui`.
                - `Modal.Backdrop`, `Modal.Content`, `Modal.Header`, `Modal.Body`, `Modal.Footer`, `Modal.CloseButton`.
            - `TextInput` -> `Input` or `Textarea`.
            - `Button` (core RN) -> `Button` from `gluestack-ui`.
            - `DateTimePicker` -> Retain if no direct `gluestack-ui` equivalent, style wrapper if needed. Or use `gluestack-ui` `Select` for date/time parts if appropriate.
        - **设计规范参考:** 模态框样式, 输入框, 按钮.

- **整体注意事项:**
    - **图标替换:** All `Ionicons` and other icon sets to `lucide-react-native`.
    - **颜色和样式:** Migrate `StyleSheet` to `gluestack-ui` props/`sx`, using theme tokens.
    - **复杂状态和逻辑:** Custom hooks (`useContactInteractions`, `useContactNetwork`) will remain.
    - **导航参数:** Screen receives `contactId` via route params.
