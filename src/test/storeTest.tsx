import React from 'react';
import { VStack, Text, Button, ButtonText } from '@gluestack-ui/themed';
import { useSettingsStore, useContactsStore, useAppStore } from '../store';

/**
 * Store测试组件
 * 用于验证全局状态管理是否正常工作
 */
const StoreTest: React.FC = () => {
  const { darkMode, notificationsEnabled } = useSettingsStore();
  const { updateSetting } = useSettingsStore();
  const { contacts } = useContactsStore();
  const { addContact } = useContactsStore();
  const { isLoading, error } = useAppStore();
  const { setLoading, setError } = useAppStore();

  const testSettings = () => {
    updateSetting('darkMode', !darkMode);
    updateSetting('notificationsEnabled', !notificationsEnabled);
  };

  const testContacts = () => {
    addContact({
      name: 'Test Contact',
      email: '<EMAIL>',
      phone: '+1234567890',
      tags: ['test'],
      isFavorite: false,
    });
  };

  const testApp = () => {
    setLoading(!isLoading);
    setError(error ? null : 'Test error message');
  };

  return (
    <VStack p="$4" space="md">
      <Text fontSize="$xl" fontWeight="$bold">Store Test</Text>
      
      <VStack space="sm">
        <Text fontSize="$lg">Settings Store:</Text>
        <Text>Dark Mode: {darkMode ? 'ON' : 'OFF'}</Text>
        <Text>Notifications: {notificationsEnabled ? 'ON' : 'OFF'}</Text>
        <Button onPress={testSettings}>
          <ButtonText>Toggle Settings</ButtonText>
        </Button>
      </VStack>

      <VStack space="sm">
        <Text fontSize="$lg">Contacts Store:</Text>
        <Text>Total Contacts: {contacts.length}</Text>
        <Button onPress={testContacts}>
          <ButtonText>Add Test Contact</ButtonText>
        </Button>
      </VStack>

      <VStack space="sm">
        <Text fontSize="$lg">App Store:</Text>
        <Text>Loading: {isLoading ? 'YES' : 'NO'}</Text>
        <Text>Error: {error || 'None'}</Text>
        <Button onPress={testApp}>
          <ButtonText>Toggle App State</ButtonText>
        </Button>
      </VStack>
    </VStack>
  );
};

export default StoreTest;
