import React from 'react';
import {
  VStack,
  HStack,
  ScrollView,
  Pressable,
  Icon,
  Text,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription
} from '@gluestack-ui/themed';
import { Platform, Linking } from 'react-native';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import {
  ChevronLeft,
  Bell,
  Clock,
  Mail,
  MapPin,
  RefreshCw,
  Trash2,
  Users,
  HelpCircle,
  ShieldCheck,
  FileText,
  Key,
  Database
} from 'lucide-react-native';

// Import our new components
import {
  ProfileSection,
  StorageSection,
  SettingItem,
  SettingsGroup,
  VersionInfo
} from '../components/Settings';
import { ConfirmationModal } from '../components/ui';
// 临时注释掉store导入，避免无限循环
// import { useSettingsSelectors, useSettingsActions, useUISelectors, useUIActions } from '../store';
import { ICON_COLORS, SUPPORTED_LANGUAGES, APP_VERSION, COPYRIGHT, EXTERNAL_LINKS } from '../constants/settingsConfig';
import type { RootStackParamList } from '../types';



const SettingsScreen = () => {
  const toast = useToast();
  const { t, i18n } = useTranslation();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  // Global state - 临时注释掉store使用，避免无限循环
  // const settings = useSettingsSelectors.useNotificationSettings();
  // const appSettings = useSettingsSelectors.useAppSettings();
  // const securitySettings = useSettingsSelectors.useSecuritySettings();
  // const userProfile = useSettingsSelectors.useUserProfile();
  // const language = useSettingsSelectors.useLanguage();

  // const { updateSetting, resetSettings } = useSettingsActions();
  // const { showConfirmationModal } = useUIActions();
  // const { isVisible: isModalVisible, config: modalConfig } = useUISelectors.useConfirmationModal();

  // 使用静态数据进行测试
  const settings = {
    notificationsEnabled: true,
    emailNotifications: true,
    reminderNotifications: true,
    networkUpdates: false
  };
  const appSettings = {
    darkMode: false,
    aiSuggestions: true,
    autoTagging: true,
    dataSync: true
  };
  const securitySettings = {
    biometricLogin: false,
    locationTracking: true
  };
  const userProfile = {
    name: 'Demo User',
    email: '<EMAIL>',
    avatar: null
  };
  const language = 'en';

  const updateSetting = () => {};
  const resetSettings = () => {};
  const showConfirmationModal = () => {};
  const isModalVisible = false;
  const modalConfig = null;

  const currentLanguage = language || i18n.language;

  // User data from store with storage info
  const user = {
    ...userProfile,
    storageUsed: "2.3 GB",
    storageTotal: "10 GB",
  };

  const handleChangeLanguage = (langCode: string) => {
    if (langCode !== currentLanguage) {
      i18n.changeLanguage(langCode);
    }
  };



  const handleResetPreferences = () => {
    showConfirmationModal({
      title: 'Reset Preferences',
      message: 'Are you sure you want to reset all preferences to default settings?',
      confirmText: 'Reset',
      cancelText: 'Cancel',
      confirmAction: 'destructive',
      onConfirm: () => {
        resetSettings();
        toast.show({
          placement: "top",
          render: ({ id }: { id: string }) => (
            <Toast nativeID={`toast-${id}`} action="success" variant="accent">
              <ToastTitle>Success</ToastTitle>
              <ToastDescription>Preferences have been reset.</ToastDescription>
            </Toast>
          ),
        });
      }
    });
  };

  const handleExportData = () => {
    showConfirmationModal({
      title: 'Export Data',
      message: 'Your data will be prepared for export. You\'ll receive an email with your data file once it\'s ready.',
      confirmText: 'Export',
      cancelText: 'Cancel',
      confirmAction: 'default',
      onConfirm: () => {
        toast.show({
          placement: "top",
          render: ({ id }: { id: string }) => (
            <Toast nativeID={`toast-${id}`} action="success" variant="accent">
              <ToastTitle>Success</ToastTitle>
              <ToastDescription>Your data export has started and will be sent to your email.</ToastDescription>
            </Toast>
          ),
        });
      }
    });
  };

  const handleDeleteAccount = () => {
    showConfirmationModal({
      title: "Delete Account",
      message: "This action cannot be undone. All your data will be permanently deleted.",
      confirmText: "Delete Account",
      cancelText: "Cancel",
      confirmAction: 'destructive',
      onConfirm: () => {
        // Open the second, final confirmation modal
        showConfirmationModal({
          title: "Confirm Final Deletion",
          message: "Are you absolutely sure you want to permanently delete your account? This cannot be undone.",
          confirmText: "Yes, Delete My Account",
          cancelText: "Cancel",
          confirmAction: 'destructive',
          onConfirm: () => {
            console.log('Account deleted');
            toast.show({
              placement: "top",
              render: ({ id }: { id: string }) => (
                <Toast nativeID={`toast-${id}`} action="success" variant="accent">
                  <ToastTitle>Account Deleted</ToastTitle>
                  <ToastDescription>Your account has been successfully deleted.</ToastDescription>
                </Toast>
              ),
            });
          },
        });
      },
    });
  };

  const openExternalLink = (url: string) => {
    Linking.openURL(url).catch(err => {
      console.error('Failed to open URL:', err);
      toast.show({
        placement: "top",
        render: ({ id }: { id: string }) => (
          <Toast nativeID={`toast-${id}`} action="error" variant="accent">
            <ToastTitle>Error</ToastTitle>
            <ToastDescription>Failed to open link.</ToastDescription>
          </Toast>
        ),
      });
    });
  };



  return (
    <VStack
      flex={1}
      bg="$backgroundLight50"
      sx={{ _dark: { bg: '$backgroundDark900' } }}
    >
      {/* Header */}
      <HStack
        alignItems="center"
        justifyContent="space-between"
        bg="$backgroundLight0"
        pt={Platform.OS === 'ios' ? '$12' : '$5'}
        pb="$3"
        px="$4"
        borderBottomWidth={1}
        borderBottomColor="$borderLight200"
        sx={{
          _dark: {
            bg: '$backgroundDark900',
            borderBottomColor: '$borderDark700'
          }
        }}
      >
        <Pressable onPress={() => navigation.goBack()} p="$2">
          <Icon as={ChevronLeft} size="lg" color="$textLight900"
            sx={{ _dark: { color: '$textDark100' } }} />
        </Pressable>

        <Text
          fontSize="$lg"
          fontWeight="$semibold"
          color="$textLight900"
          sx={{ _dark: { color: '$textDark100' } }}
        >
          {t('settings.title')}
        </Text>

        <VStack w="$10" />
      </HStack>

      <ScrollView flex={1}>
        {/* Profile Section */}
        <ProfileSection
          user={user}
          onPress={() => {
            // TODO: Navigate to profile edit screen
            console.log('Navigate to profile edit');
          }}
        />

        {/* Storage Section */}
        <StorageSection
          storageUsed={user.storageUsed}
          storageTotal={user.storageTotal}
          usagePercentage={23}
        />

        {/* App Settings */}
        <SettingsGroup title={t('settings.appSettings', 'App Settings')}>
          <SettingItem
            icon={Bell}
            iconColor={ICON_COLORS.moon}
            title={t('darkMode')}
            description="Switch between light and dark themes"
            toggle={true}
            value={appSettings.darkMode}
            onValueChange={(value) => updateSetting('darkMode', value)}
          />
          <SettingItem
            icon={Clock}
            iconColor={ICON_COLORS.bulb}
            title="AI Suggestions"
            description="Get personalized recommendations"
            toggle={true}
            value={appSettings.aiSuggestions}
            onValueChange={(value) => updateSetting('aiSuggestions', value)}
          />
          <SettingItem
            icon={Mail}
            iconColor={ICON_COLORS.pricetag}
            title="Auto-Tagging"
            description="Automatically tag contacts based on info"
            toggle={true}
            value={appSettings.autoTagging}
            onValueChange={(value) => updateSetting('autoTagging', value)}
          />
          <SettingItem
            icon={MapPin}
            iconColor={ICON_COLORS.fingerPrint}
            title="Biometric Login"
            description="Use Face ID or Touch ID to secure your account"
            toggle={true}
            value={securitySettings.biometricLogin}
            onValueChange={(value) => updateSetting('biometricLogin', value)}
          />
          <SettingItem
            icon={Users}
            iconColor={ICON_COLORS.location}
            title="Location Tracking"
            description="Allow the app to use your location"
            toggle={true}
            value={securitySettings.locationTracking}
            onValueChange={(value) => updateSetting('locationTracking', value)}
          />
        </SettingsGroup>
        
        {/* App Preferences */}
        <SettingsGroup title={t('settings.preferences', 'Preferences')}>
          <SettingItem
            icon={Key}
            iconColor={ICON_COLORS.language}
            title={t('language')}
            value={SUPPORTED_LANGUAGES.find(l => l.code === currentLanguage)?.name || ''}
            showChevron={true}
            onPress={() => {
              // TODO: Implement language selection modal
              console.log('Show language selection');
            }}
          />
        </SettingsGroup>

        {/* Notifications */}
        <SettingsGroup title={t('settings.notifications.title', 'Notifications')}>
          <SettingItem
            icon={Bell}
            iconColor={ICON_COLORS.notifications}
            title={t('settings.notifications.title', 'Notifications')}
            description="Manage how you receive alerts"
            toggle={true}
            value={settings.notificationsEnabled}
            onValueChange={(value) => updateSetting('notificationsEnabled', value)}
          />

          {settings.notificationsEnabled && (
            <>
              <SettingItem
                icon={Mail}
                iconColor={ICON_COLORS.mail}
                title={t('settings.notifications.email', 'Email Notifications')}
                description="Receive updates via email"
                toggle={true}
                value={settings.emailNotifications}
                onValueChange={(value) => updateSetting('emailNotifications', value)}
              />
              <SettingItem
                icon={Clock}
                iconColor={ICON_COLORS.time}
                title={t('settings.notifications.reminders', 'Reminder Alerts')}
                description="Get notified for upcoming events"
                toggle={true}
                value={settings.reminderNotifications}
                onValueChange={(value) => updateSetting('reminderNotifications', value)}
              />
              <SettingItem
                icon={Users}
                iconColor={ICON_COLORS.people}
                title={t('settings.notifications.network', 'Network Updates')}
                description="Alerts from your connections"
                toggle={true}
                value={settings.networkUpdates}
                onValueChange={(value) => updateSetting('networkUpdates', value)}
              />
            </>
          )}
        </SettingsGroup>
        
        {/* Data & Privacy */}
        <SettingsGroup title={t('settings.dataPrivacy', 'Data & Privacy')}>
          <SettingItem
            icon={Database}
            iconColor={ICON_COLORS.cloudSync}
            title="Data Sync"
            description="Keep your data updated across devices"
            toggle={true}
            value={appSettings.dataSync}
            onValueChange={(value) => updateSetting('dataSync', value)}
          />
          <SettingItem
            icon={ShieldCheck}
            iconColor={ICON_COLORS.shieldCheckmark}
            title="Privacy Policy"
            showChevron={true}
            onPress={() => openExternalLink(EXTERNAL_LINKS.privacyPolicy)}
          />
          <SettingItem
            icon={FileText}
            iconColor={ICON_COLORS.documentText}
            title="Terms of Service"
            showChevron={true}
            onPress={() => openExternalLink(EXTERNAL_LINKS.termsOfService)}
          />
        </SettingsGroup>

        {/* Support */}
        <SettingsGroup title={t('settings.support', 'Support')}>
          <SettingItem
            icon={HelpCircle}
            iconColor={ICON_COLORS.helpCircle}
            title="Help Center"
            showChevron={true}
            onPress={() => openExternalLink(EXTERNAL_LINKS.helpCenter)}
          />
        </SettingsGroup>
        
        {/* Account */}
        <SettingsGroup title={t('settings.account', 'Account')} showSeparator={false}>
          <SettingItem
            icon={Key}
            iconColor={ICON_COLORS.key}
            title="Change Password"
            showChevron={true}
            onPress={() => {
              // TODO: Navigate to change password screen
              console.log('Navigate to change password');
            }}
          />
          <SettingItem
            icon={RefreshCw}
            iconColor={ICON_COLORS.refresh}
            title="Reset Preferences"
            description="Restore all settings to their default values"
            showChevron={true}
            onPress={handleResetPreferences}
          />
          <SettingItem
            icon={Database}
            iconColor={ICON_COLORS.databaseExport}
            title="Export Data"
            description="Download a copy of your data"
            showChevron={true}
            onPress={handleExportData}
          />
          <SettingItem
            icon={Trash2}
            iconColor={ICON_COLORS.trash}
            title="Delete Account"
            description="Permanently remove your account and data"
            showChevron={true}
            onPress={handleDeleteAccount}
          />
        </SettingsGroup>

        {/* App Version Info */}
        <VersionInfo
          version={APP_VERSION}
          copyright={COPYRIGHT}
        />
      </ScrollView>

      {/* Confirmation Modal */}
      {modalConfig && (
        <ConfirmationModal
          isVisible={isModalVisible}
          onClose={() => {}} // 临时注释掉store使用
          title={modalConfig.title}
          message={modalConfig.message}
          confirmText={modalConfig.confirmText}
          cancelText={modalConfig.cancelText}
          confirmAction={modalConfig.confirmAction}
          onConfirm={modalConfig.onConfirm}
        />
      )}
    </VStack>
  );
};



export default SettingsScreen;