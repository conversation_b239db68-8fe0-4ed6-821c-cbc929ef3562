# 智能提醒系统功能测试指南

## 功能概述

智能提醒系统是RelationHub的核心功能之一，能够自动分析联系人数据并生成智能提醒，帮助用户维护人际关系。

### 主要功能
1. **生日提醒**：自动识别联系人生日并提前提醒
2. **定期联系提醒**：基于关系类型和联系频率智能提醒
3. **会议提醒**：会议相关的提醒通知
4. **自定义提醒**：用户手动创建的提醒
5. **智能建议**：AI分析后的提醒建议

## 测试前准备

### 1. 环境配置
确保已安装expo-notifications依赖：
```bash
npm install expo-notifications@~0.27.6
```

### 2. 权限设置
- 确保应用有通知权限
- 在真机上测试通知功能（模拟器可能不支持推送通知）

### 3. 测试数据准备
创建一些包含生日信息的测试联系人：
```javascript
// 示例测试联系人
{
  name: "张三",
  birthday: new Date("1990-12-25"), // 设置一个即将到来的生日
  relationshipType: "friend",
  priority: "high",
  lastContactDate: new Date("2024-11-01"), // 设置较久未联系
}
```

## 测试用例

### 测试用例1：生日提醒生成

**测试目标**：验证系统能够自动生成生日提醒

**测试步骤**：
1. 打开RelationHub应用
2. 导航到"提醒"页面
3. 观察是否自动生成了生日提醒
4. 检查提醒的时间设置（应该提前3天、1天、当天）

**预期结果**：
- 系统自动为有生日信息的联系人生成提醒
- 生日提醒包含提前3天、1天、当天三个时间点
- 提醒标题包含生日emoji（🎂）
- 提醒描述清晰说明是谁的生日

### 测试用例2：定期联系提醒

**测试目标**：验证系统能够基于联系频率生成提醒

**测试步骤**：
1. 创建一个很久没有联系的重要联系人
2. 设置联系人的关系类型为"客户"或"朋友"
3. 等待系统生成定期联系提醒
4. 检查提醒的优先级和建议操作

**预期结果**：
- 系统根据最后联系时间和关系重要程度生成提醒
- 提醒优先级与联系人重要程度匹配
- 提供合适的联系方式建议（电话、邮件、消息等）

### 测试用例3：提醒中心界面

**测试目标**：验证提醒中心的完整功能

**测试步骤**：
1. 导航到"提醒"页面
2. 查看提醒概览统计
3. 测试过滤功能（按类型、状态筛选）
4. 测试提醒操作（完成、延期、忽略）
5. 测试下拉刷新功能

**预期结果**：
- 统计数据准确显示（待处理、已逾期、已完成等）
- 过滤功能正常工作
- 提醒操作响应及时，状态更新正确
- 刷新功能能够重新生成提醒

### 测试用例4：首页提醒概览

**测试目标**：验证首页提醒卡片显示

**测试步骤**：
1. 导航到首页
2. 查看统计卡片中的"待处理提醒"数量
3. 查看"提醒事项"部分显示的近期提醒
4. 点击"查看全部"链接

**预期结果**：
- 统计数据与提醒中心一致
- 显示最近3个待处理提醒
- 点击链接正确跳转到提醒中心
- 无提醒时显示友好的空状态

### 测试用例5：通知权限和推送

**测试目标**：验证通知系统功能

**测试步骤**：
1. 首次打开应用时观察权限请求
2. 创建一个即将触发的提醒（设置为几分钟后）
3. 将应用切换到后台
4. 等待通知推送
5. 点击通知查看应用响应

**预期结果**：
- 应用正确请求通知权限
- 权限被拒绝时显示友好提示
- 通知按时推送
- 点击通知能够正确打开应用并导航到相关页面

### 测试用例6：智能建议功能

**测试目标**：验证AI智能建议生成

**测试步骤**：
1. 确保有多个联系人数据
2. 在提醒中心查看是否有智能建议
3. 接受一个建议，观察提醒生成
4. 忽略一个建议，观察界面更新

**预期结果**：
- 系统根据联系人数据生成合理建议
- 建议包含置信度和原因说明
- 接受建议后正确生成对应提醒
- 忽略建议后从列表中移除

### 测试用例7：提醒操作功能

**测试目标**：验证提醒的各种操作

**测试步骤**：
1. 选择一个待处理提醒
2. 测试"完成"操作
3. 选择另一个提醒测试"延期"操作
4. 测试"忽略"操作
5. 查看操作后的状态变化

**预期结果**：
- 完成操作后提醒状态变为"已完成"
- 延期操作后提醒重新调度到指定时间
- 忽略操作后提醒状态变为"已忽略"
- 所有操作都有相应的成功提示

### 测试用例8：数据持久化

**测试目标**：验证提醒数据的持久化

**测试步骤**：
1. 创建几个提醒
2. 完全关闭应用
3. 重新打开应用
4. 检查提醒数据是否保持

**预期结果**：
- 提醒数据在应用重启后保持不变
- 提醒状态正确恢复
- 通知调度在应用重启后继续有效

## 性能测试

### 测试用例9：大量数据处理

**测试目标**：验证系统处理大量联系人和提醒的性能

**测试步骤**：
1. 导入大量联系人数据（100+）
2. 触发提醒生成
3. 观察生成时间和应用响应性
4. 测试提醒列表的滚动性能

**预期结果**：
- 提醒生成在合理时间内完成（<5秒）
- 应用在处理过程中保持响应
- 提醒列表滚动流畅
- 内存使用保持在合理范围

### 测试用例10：网络异常处理

**测试目标**：验证网络异常情况下的处理

**测试步骤**：
1. 断开网络连接
2. 尝试生成智能建议
3. 观察错误处理
4. 恢复网络连接后重试

**预期结果**：
- 网络异常时显示友好错误提示
- 本地功能（如查看已有提醒）不受影响
- 网络恢复后功能正常

## 边界条件测试

### 测试用例11：特殊日期处理

**测试数据**：
- 2月29日生日（闰年）
- 过去的日期
- 很久以后的日期

**预期结果**：
- 正确处理闰年生日
- 过去日期不生成提醒
- 未来日期正确计算

### 测试用例12：空数据处理

**测试步骤**：
1. 清空所有联系人
2. 查看提醒中心
3. 尝试生成提醒

**预期结果**：
- 显示友好的空状态
- 不产生错误或崩溃
- 提供创建联系人的引导

## 已知限制

1. **通知限制**：iOS和Android对后台通知有不同限制
2. **时区处理**：当前版本使用本地时区，跨时区使用可能有问题
3. **批量操作**：大量提醒的批量操作可能较慢
4. **智能建议**：需要足够的联系人数据才能生成有意义的建议

## 故障排除

### 常见问题

1. **通知不显示**
   - 检查通知权限设置
   - 确认在真机上测试
   - 检查系统勿扰模式设置

2. **提醒不自动生成**
   - 确认联系人有必要的数据（生日、最后联系时间等）
   - 检查提醒规则配置
   - 尝试手动刷新

3. **应用性能问题**
   - 检查联系人数量是否过多
   - 清理已完成的旧提醒
   - 重启应用

4. **数据不同步**
   - 检查数据持久化配置
   - 确认AsyncStorage权限
   - 尝试重新安装应用

## 反馈收集

测试过程中请记录：
1. 发现的bug和错误信息
2. 性能问题和响应时间
3. 用户体验改进建议
4. 功能完整性评估

这些反馈将帮助我们持续改进智能提醒系统的功能和性能。
