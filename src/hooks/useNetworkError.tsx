import React from 'react';
import { useToast, Toast, ToastTitle, ToastDescription } from '@gluestack-ui/themed';
import { useAppActions, useAppSelectors } from '../store';
import { NetworkError, handleApiError, withRetry, getErrorMessage } from '../utils/networkUtils';

/**
 * 网络错误处理Hook
 * 提供统一的网络错误处理和用户提示
 */
export const useNetworkError = () => {
  const toast = useToast();
  const { setError } = useAppActions();
  const { error } = useAppSelectors.useErrorStates();

  // 显示错误提示
  const showError = (error: NetworkError) => {
    const errorMessage = getErrorMessage(error);
    
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={id} action="error" variant="accent">
          <ToastTitle>Connection Error</ToastTitle>
          <ToastDescription>{errorMessage}</ToastDescription>
        </Toast>
      ),
    });

    // 更新全局错误状态
    setError(errorMessage);
  };

  // 清除错误
  const clearError = () => {
    setError(null);
  };

  // 带重试的网络请求包装器
  const withRetryAndErrorHandling = async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    try {
      return await withRetry(operation, maxRetries, delay);
    } catch (error) {
      const networkError = handleApiError(error);
      showError(networkError);
      throw error;
    }
  };

  return {
    showError,
    clearError,
    handleApiError,
    withRetry: withRetryAndErrorHandling,
    hasError: !!error,
    currentError: error,
  };
};
