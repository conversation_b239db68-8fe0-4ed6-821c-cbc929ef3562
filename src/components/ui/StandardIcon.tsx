import React from 'react';
import { Icon } from '@gluestack-ui/themed';
import { DESIGN_SYSTEM } from '../../constants/designSystem';

interface StandardIconProps {
  as: React.ComponentType<any>;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  color?: string;
  style?: any;
  testID?: string;
}

/**
 * 标准化图标组件
 * 确保所有图标遵循设计系统尺寸规范
 */
const StandardIcon: React.FC<StandardIconProps> = ({
  as,
  size = 'md',
  color,
  style,
  testID,
}) => {
  const iconSize = DESIGN_SYSTEM.ICON_SIZES[size];

  return (
    <Icon
      as={as}
      size={iconSize}
      color={color}
      style={style}
      testID={testID}
    />
  );
};

export default StandardIcon;
