/**
 * 图片处理服务
 * 用于拍照、图片选择和OCR文字识别
 */

import * as ImagePicker from 'expo-image-picker';
import { Alert } from 'react-native';

export interface ImagePickerResult {
  uri: string;
  width: number;
  height: number;
  type: 'image';
  base64?: string;
}

export interface OCRResult {
  text: string;
  confidence: number;
  blocks?: {
    text: string;
    boundingBox: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }[];
}

class ImageProcessingService {
  /**
   * 请求相机权限
   */
  async requestCameraPermission(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          '需要相机权限',
          '请在设置中允许RelationHub访问相机，以便拍照识别联系人信息。',
          [
            { text: '取消', style: 'cancel' },
            { text: '去设置', onPress: () => ImagePicker.requestCameraPermissionsAsync() },
          ]
        );
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('请求相机权限失败:', error);
      return false;
    }
  }

  /**
   * 请求相册权限
   */
  async requestMediaLibraryPermission(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          '需要相册权限',
          '请在设置中允许RelationHub访问相册，以便选择图片识别联系人信息。',
          [
            { text: '取消', style: 'cancel' },
            { text: '去设置', onPress: () => ImagePicker.requestMediaLibraryPermissionsAsync() },
          ]
        );
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('请求相册权限失败:', error);
      return false;
    }
  }

  /**
   * 拍照获取图片
   */
  async takePhoto(): Promise<ImagePickerResult | null> {
    try {
      const hasPermission = await this.requestCameraPermission();
      if (!hasPermission) {
        return null;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true, // 需要base64用于OCR
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return null;
      }

      const asset = result.assets[0];
      return {
        uri: asset.uri,
        width: asset.width,
        height: asset.height,
        type: 'image',
        base64: asset.base64,
      };
    } catch (error) {
      console.error('拍照失败:', error);
      Alert.alert('拍照失败', '请重试或选择其他方式添加联系人信息。');
      return null;
    }
  }

  /**
   * 从相册选择图片
   */
  async pickImage(): Promise<ImagePickerResult | null> {
    try {
      const hasPermission = await this.requestMediaLibraryPermission();
      if (!hasPermission) {
        return null;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true, // 需要base64用于OCR
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return null;
      }

      const asset = result.assets[0];
      return {
        uri: asset.uri,
        width: asset.width,
        height: asset.height,
        type: 'image',
        base64: asset.base64,
      };
    } catch (error) {
      console.error('选择图片失败:', error);
      Alert.alert('选择图片失败', '请重试或选择其他方式添加联系人信息。');
      return null;
    }
  }

  /**
   * 显示图片选择选项
   */
  async showImagePickerOptions(): Promise<ImagePickerResult | null> {
    return new Promise((resolve) => {
      Alert.alert(
        '选择图片',
        '请选择获取图片的方式',
        [
          {
            text: '拍照',
            onPress: async () => {
              const result = await this.takePhoto();
              resolve(result);
            },
          },
          {
            text: '从相册选择',
            onPress: async () => {
              const result = await this.pickImage();
              resolve(result);
            },
          },
          {
            text: '取消',
            style: 'cancel',
            onPress: () => resolve(null),
          },
        ]
      );
    });
  }

  /**
   * OCR文字识别
   * 注意：这里使用Google Vision API作为示例
   * 实际项目中可以选择其他OCR服务
   */
  async extractTextFromImage(imageBase64: string): Promise<OCRResult> {
    try {
      // 这里使用Google Cloud Vision API作为示例
      // 实际使用时需要配置API密钥
      const apiKey = process.env.EXPO_PUBLIC_GOOGLE_VISION_API_KEY;
      
      if (!apiKey) {
        // 如果没有配置OCR API，返回提示用户手动输入
        throw new Error('OCR服务未配置，请手动输入文本');
      }

      const response = await fetch(
        `https://vision.googleapis.com/v1/images:annotate?key=${apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            requests: [
              {
                image: {
                  content: imageBase64,
                },
                features: [
                  {
                    type: 'TEXT_DETECTION',
                    maxResults: 1,
                  },
                ],
              },
            ],
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`OCR API调用失败: ${response.status}`);
      }

      const data = await response.json();
      const textAnnotations = data.responses[0]?.textAnnotations;

      if (!textAnnotations || textAnnotations.length === 0) {
        return {
          text: '',
          confidence: 0,
          blocks: [],
        };
      }

      // 第一个结果是完整文本
      const fullText = textAnnotations[0].description || '';
      
      // 其他结果是文本块
      const blocks = textAnnotations.slice(1).map((annotation: any) => ({
        text: annotation.description,
        boundingBox: {
          x: annotation.boundingPoly.vertices[0].x || 0,
          y: annotation.boundingPoly.vertices[0].y || 0,
          width: (annotation.boundingPoly.vertices[2].x || 0) - (annotation.boundingPoly.vertices[0].x || 0),
          height: (annotation.boundingPoly.vertices[2].y || 0) - (annotation.boundingPoly.vertices[0].y || 0),
        },
      }));

      return {
        text: fullText,
        confidence: 0.8, // Google Vision API通常有较高的准确率
        blocks,
      };
    } catch (error) {
      console.error('OCR识别失败:', error);
      
      // 如果OCR失败，提供降级方案
      return {
        text: '',
        confidence: 0,
        blocks: [],
      };
    }
  }

  /**
   * 预处理图片（可选）
   * 提高OCR识别准确率
   */
  private preprocessImage(imageBase64: string): string {
    // 这里可以添加图片预处理逻辑
    // 例如：调整对比度、去噪、二值化等
    // 目前直接返回原图
    return imageBase64;
  }

  /**
   * 验证图片质量
   */
  validateImageQuality(image: ImagePickerResult): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // 检查分辨率
    if (image.width < 300 || image.height < 300) {
      issues.push('图片分辨率过低，可能影响识别效果');
    }

    // 检查宽高比
    const aspectRatio = image.width / image.height;
    if (aspectRatio > 3 || aspectRatio < 0.3) {
      issues.push('图片宽高比异常，建议重新拍照');
    }

    return {
      isValid: issues.length === 0,
      issues,
    };
  }

  /**
   * 压缩图片
   */
  async compressImage(uri: string, quality: number = 0.8): Promise<string> {
    try {
      // 使用ImagePicker的压缩功能
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality,
        base64: true,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        return result.assets[0].uri;
      }

      return uri;
    } catch (error) {
      console.error('图片压缩失败:', error);
      return uri;
    }
  }
}

// 导出单例实例
export const imageProcessingService = new ImageProcessingService();
export default imageProcessingService;
