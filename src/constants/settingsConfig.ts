// Settings configuration constants

export const ICON_COLORS = {
  notifications: '$warning500',
  mail: '$primary500', 
  time: '$info500',
  people: '$success500',
  cloudSync: '$secondary500',
  moon: '$textLight900',
  bulb: '$warning500',
  pricetag: '$success500',
  fingerPrint: '$primary500',
  location: '$info500',
  shieldCheckmark: '$success500',
  documentText: '$secondary500',
  informationCircle: '$info500',
  exit: '$error500',
  trash: '$error500',
  checkmarkCircleOutline: '$success500',
  ellipseOutline: '$textLight400',
  key: '$primary500',
  userShield: '$primary500',
  fileExport: '$secondary500',
  databaseExport: '$secondary500',
  bookOutline: '$info500',
  helpCircle: '$info500',
  codeSlash: '$textLight900',
  information: '$info500',
  lockClosed: '$primary500',
  barChart: '$info500',
  trendingUp: '$success500',
  colorPalette: '$primary500',
  language: '$primary500',
  list: '$secondary500',
  refresh: '$primary500',
  chevronForward: '$textLight400'
} as const;

export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' },
] as const;

export const APP_VERSION = 'RelationHub v1.0.0';
export const COPYRIGHT = '© 2025 RelationHub Inc.';

export const EXTERNAL_LINKS = {
  privacyPolicy: 'https://example.com/privacy-policy',
  termsOfService: 'https://example.com/terms-of-service',
  helpCenter: 'https://example.com/help-center',
} as const;
