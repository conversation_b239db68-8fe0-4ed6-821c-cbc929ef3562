import React from 'react';

import {
  Text,
  Box,
  Spinner,
  ScrollView,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription
} from '@gluestack-ui/themed';

import { RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types';

import ContactHeader from "../features/ContactDetail/components/ContactHeader";
import TabNavigator from '../features/ContactDetail/components/TabNavigator';
import ProfileTab from '../features/ContactDetail/components/ProfileTab';
import InteractionsTab from '../features/ContactDetail/components/InteractionsTab';
import NetworkTab from '../features/ContactDetail/components/NetworkTab';
import TagModal from '../features/ContactDetail/components/TagModal';
import InteractionFormModal from '../features/ContactDetail/components/InteractionFormModal';
import NotesEditModal from '../features/ContactDetail/components/NotesEditModal';
import { Tag as TagIcon, Network } from 'lucide-react-native';
import ReminderModal from '../features/ContactDetail/components/ReminderModal'; 

import {
  useContactDetail,
  ContactData,
  Tag,
  Interaction,
  Reminder,
  ContactDetailScreenRouteProp,
} from '../features/ContactDetail/hooks/useContactDetail';

interface ContactDetailScreenProps {
  navigation: NativeStackNavigationProp<RootStackParamList, 'ContactDetail'>;
  route: ContactDetailScreenRouteProp;
}



export default function ContactDetailScreen({ navigation, route }: ContactDetailScreenProps) {
  const toast = useToast();
  const {
    activeTab,
    setActiveTab,
    contact, 
    // Notes Modal
    isNotesModalVisible,
    editingNotes,
    handleEditNotes,
    saveNotesFromModal,
    closeNotesModal,
    // Reminder Modal
    isReminderModalVisible,
    editingReminder, // This should be returned by the hook
    handleAddReminder, 
    saveReminderFromModal,
    closeReminderModal,
    handleCompleteReminder,
    handleEditReminder, // Added for editing existing reminders
    // Tag Modal
    isTagModalVisible,
    editingTag,
    openCreateTagModal,
    openEditTagModal,
    closeTagModal,
    handleSaveTag,
    handleDeleteTag,
    // Interaction Modal
    isInteractionModalVisible,
    openInteractionModal, 
    closeInteractionModal, 
    handleTriggerAddInteraction, 
    handleSaveNewInteraction,
    handleViewInteractionDetail,
    // Interaction Filters
    filteredInteractions,
    interactionFilterType,
    setInteractionFilterType,
    interactionFilterDateRange,
    setInteractionFilterDateRange,
    interactionSearchText,
    setInteractionSearchText,
    // Navigation actions
    handleViewConnection,
    handleViewNetworkFromContact,
    handleTagPress, // Added for ProfileTab
  } = useContactDetail(route);

  const renderTabContent = () => {
    if (!contact) {
      return (<Spinner size="large" color={'$primary500'} mt={'$4'} />);
    }

    const tabComponents: { [key: string]: JSX.Element } = {
      profile: (
        <ProfileTab
          contact={contact}
          onAddTag={openCreateTagModal}
          onEditTag={openEditTagModal}
          onEditNotes={handleEditNotes}
          onAddReminder={handleAddReminder}
          onCompleteReminder={handleCompleteReminder}
          onTagPress={handleTagPress} // Added missing prop
          onEditReminder={handleEditReminder} // Added prop for editing reminders
        />
      ),
      interactions: (
        <InteractionsTab
          interactions={filteredInteractions}
          onAddInteraction={handleTriggerAddInteraction}
          onViewInteractionDetail={handleViewInteractionDetail}
          filterType={interactionFilterType}
          setFilterType={setInteractionFilterType}
          filterDateRange={interactionFilterDateRange}
          setFilterDateRange={setInteractionFilterDateRange}
          searchText={interactionSearchText}
          setSearchText={setInteractionSearchText}
        />
      ),
      network: (
        <NetworkTab
          contactName={contact.name} // Added missing prop
          connections={contact.connections || []}
          onViewConnection={handleViewConnection}
          onViewNetworkFromContact={handleViewNetworkFromContact} // Corrected prop name
        />
      ),
    };

    // Fallback to profile tab if activeTab is not a key in tabComponents or is undefined
    return tabComponents[activeTab] || tabComponents.profile;
  };

  return (
    <Box flex={1} bg="$backgroundLight50">
      <ScrollView flex={1}>
        {contact ? (
          <>
            <ContactHeader 
              contact={contact} 
              onNavigateBack={() => navigation.goBack()}
              onEditContact={() => toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                    <ToastTitle>Edit Contact</ToastTitle>
                    <ToastDescription>Edit contact form will appear here.</ToastDescription>
                  </Toast>
                )
              })}
              onMoreOptions={() => toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                    <ToastTitle>Contact Options</ToastTitle>
                    <ToastDescription>Additional options will appear here.</ToastDescription>
                  </Toast>
                )
              })}
              onCall={() => toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                    <ToastTitle>Call</ToastTitle>
                    <ToastDescription>{`Calling ${contact.name} at ${contact.phone}`}</ToastDescription>
                  </Toast>
                )
              })}
              onEmail={() => toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                    <ToastTitle>Email</ToastTitle>
                    <ToastDescription>{`Composing email to ${contact.email}`}</ToastDescription>
                  </Toast>
                )
              })}
              onMessage={() => toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                    <ToastTitle>Message</ToastTitle>
                    <ToastDescription>{`Messaging ${contact.name}`}</ToastDescription>
                  </Toast>
                )
              })}
              onSchedule={() => {
                if (contact?.id) {
                  navigation.navigate('MeetingRecord', { contactId: contact.id });
                } else {
                  toast.show({
                  placement: "top",
                  render: ({ id }) => (
                    <Toast nativeID={`toast-${id}`} action="error" variant="accent">
                      <ToastTitle>Error</ToastTitle>
                      <ToastDescription>Contact ID is missing, cannot schedule meeting.</ToastDescription>
                    </Toast>
                  ),
                });
                }
              }}
            />
            
            <TabNavigator activeTab={activeTab} setActiveTab={setActiveTab} />
            <Box p={"$4"}>
              {renderTabContent()}
            </Box>
          </>
        ) : (
          // TODO: Replace with Gluestack UI token for ActivityIndicator color and potentially style
          <Spinner size="large" color={'$primary500'} flex={1} justifyContent='center' alignItems='center' />
        )}
      </ScrollView>

      <TagModal
        visible={isTagModalVisible}
        tag={editingTag}
        onClose={closeTagModal}
        onSave={handleSaveTag}
        onDelete={handleDeleteTag}
      />

      <InteractionFormModal 
        visible={isInteractionModalVisible}
        onClose={closeInteractionModal}
        onSave={handleSaveNewInteraction} // Corrected prop
      />

      <NotesEditModal
        visible={isNotesModalVisible}
        initialNotes={editingNotes} 
        onSave={saveNotesFromModal}
        onClose={closeNotesModal}
      />

      <ReminderModal
        visible={isReminderModalVisible}
        reminder={editingReminder}
        onSave={saveReminderFromModal}
        onClose={closeReminderModal}
      />
    </Box>
  );
}