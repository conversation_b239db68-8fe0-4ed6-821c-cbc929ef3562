#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 图标映射表：将 @expo/vector-icons 图标映射到 lucide 图标
const iconMapping = {
  // Ionicons 映射
  'chevron-back': 'ChevronLeft',
  'chevron-forward': 'ChevronRight',
  'chevron-down': 'ChevronDown',
  'chevron-up': 'ChevronUp',
  'add': 'Plus',
  'add-circle': 'PlusCircle',
  'remove': 'Minus',
  'remove-circle': 'MinusCircle',
  'close': 'X',
  'close-circle': 'XCircle',
  'checkmark': 'Check',
  'checkmark-circle': 'CheckCircle',
  'heart': 'Heart',
  'heart-outline': 'Heart',
  'star': 'Star',
  'star-outline': 'Star',
  'call': 'Phone',
  'call-outline': 'Phone',
  'mail': 'Mail',
  'mail-outline': 'Mail',
  'person': 'User',
  'person-outline': 'User',
  'people': 'Users',
  'people-outline': 'Users',
  'location': 'MapPin',
  'location-outline': 'MapPin',
  'time': 'Clock',
  'time-outline': 'Clock',
  'calendar': 'Calendar',
  'calendar-outline': 'Calendar',
  'camera': 'Camera',
  'camera-outline': 'Camera',
  'image': 'Image',
  'image-outline': 'Image',
  'settings': 'Settings',
  'settings-outline': 'Settings',
  'search': 'Search',
  'search-outline': 'Search',
  'filter': 'Filter',
  'menu': 'Menu',
  'home': 'Home',
  'home-outline': 'Home',
  'bookmark': 'Bookmark',
  'bookmark-outline': 'Bookmark',
  'share': 'Share',
  'share-outline': 'Share',
  'trash': 'Trash2',
  'trash-outline': 'Trash2',
  'edit': 'Edit',
  'create': 'Edit',
  'create-outline': 'Edit',
  'eye': 'Eye',
  'eye-outline': 'Eye',
  'eye-off': 'EyeOff',
  'eye-off-outline': 'EyeOff',
  'information': 'Info',
  'information-outline': 'Info',
  'warning': 'AlertTriangle',
  'warning-outline': 'AlertTriangle',
  'alert': 'AlertCircle',
  'alert-outline': 'AlertCircle',
  'help': 'HelpCircle',
  'help-outline': 'HelpCircle',
  'refresh': 'RefreshCw',
  'refresh-outline': 'RefreshCw',
  'notifications': 'Bell',
  'notifications-outline': 'Bell',
  'notifications-off': 'BellOff',
  'map': 'Map',
  'map-outline': 'Map',
  'grid': 'Grid',
  'grid-outline': 'Grid',
  'list': 'List',
  'list-outline': 'List',
  'copy': 'Copy',
  'copy-outline': 'Copy',
  'download': 'Download',
  'download-outline': 'Download',
  'cloud': 'Cloud',
  'cloud-outline': 'Cloud',
  'document': 'FileText',
  'document-outline': 'FileText',
  'folder': 'Folder',
  'folder-outline': 'Folder',
  'library': 'Library',
  'library-outline': 'Library',
  'lock-closed': 'Lock',
  'lock-open': 'Unlock',
  'play': 'Play',
  'play-outline': 'Play',
  'pause': 'Pause',
  'pause-outline': 'Pause',
  'stop': 'Square',
  'stop-outline': 'Square',
  'volume-high': 'Volume2',
  'volume-low': 'Volume1',
  'volume-off': 'VolumeX',
  'wifi': 'Wifi',
  'wifi-outline': 'Wifi',
  'battery-full': 'Battery',
  'battery-half': 'Battery',
  'battery-dead': 'BatteryLow',

  // MaterialIcons 映射
  'add': 'Plus',
  'remove': 'Minus',
  'delete': 'Trash2',
  'edit': 'Edit',
  'save': 'Save',
  'cancel': 'X',
  'done': 'Check',
  'clear': 'X',
  'refresh': 'RefreshCw',
  'search': 'Search',
  'filter_list': 'Filter',
  'sort': 'ArrowUpDown',
  'more_vert': 'MoreVertical',
  'more_horiz': 'MoreHorizontal',
  'menu': 'Menu',
  'arrow_back': 'ArrowLeft',
  'arrow_forward': 'ArrowRight',
  'arrow_upward': 'ArrowUp',
  'arrow_downward': 'ArrowDown',
  'expand_more': 'ChevronDown',
  'expand_less': 'ChevronUp',
  'chevron_left': 'ChevronLeft',
  'chevron_right': 'ChevronRight',
  'home': 'Home',
  'person': 'User',
  'group': 'Users',
  'location_on': 'MapPin',
  'access_time': 'Clock',
  'date_range': 'Calendar',
  'phone': 'Phone',
  'email': 'Mail',
  'camera_alt': 'Camera',
  'photo': 'Image',
  'settings': 'Settings',
  'help': 'HelpCircle',
  'info': 'Info',
  'warning': 'AlertTriangle',
  'error': 'AlertCircle',
  'check_circle': 'CheckCircle',
  'favorite': 'Heart',
  'bookmark': 'Bookmark',
  'share': 'Share',
  'visibility': 'Eye',
  'visibility_off': 'EyeOff',
  'lock': 'Lock',
  'security': 'Shield',
  'cloud': 'Cloud',
  'file_download': 'Download',
  'file_upload': 'Upload',
  'folder': 'Folder',
  'description': 'FileText',
  'library_books': 'Library',
  'play_arrow': 'Play',
  'pause': 'Pause',
  'stop': 'Square',
  'volume_up': 'Volume2',
  'volume_down': 'Volume1',
  'volume_off': 'VolumeX',
  'wifi': 'Wifi',
  'battery_full': 'Battery',
  'notifications': 'Bell',
  'notifications_off': 'BellOff',

  // FontAwesome5 映射
  'user': 'User',
  'users': 'Users',
  'home': 'Home',
  'search': 'Search',
  'heart': 'Heart',
  'star': 'Star',
  'bookmark': 'Bookmark',
  'share': 'Share',
  'comment': 'MessageCircle',
  'message': 'MessageSquare',
  'phone': 'Phone',
  'envelope': 'Mail',
  'camera': 'Camera',
  'image': 'Image',
  'video': 'Video',
  'music': 'Music',
  'calendar': 'Calendar',
  'clock': 'Clock',
  'map': 'Map',
  'location': 'MapPin',
  'edit': 'Edit',
  'trash': 'Trash2',
  'save': 'Save',
  'download': 'Download',
  'upload': 'Upload',
  'copy': 'Copy',
  'cut': 'Scissors',
  'paste': 'Clipboard',
  'settings': 'Settings',
  'cog': 'Settings',
  'lock': 'Lock',
  'unlock': 'Unlock',
  'eye': 'Eye',
  'eye-slash': 'EyeOff',
  'plus': 'Plus',
  'minus': 'Minus',
  'times': 'X',
  'check': 'Check',
  'arrow-left': 'ArrowLeft',
  'arrow-right': 'ArrowRight',
  'arrow-up': 'ArrowUp',
  'arrow-down': 'ArrowDown',
  'chevron-left': 'ChevronLeft',
  'chevron-right': 'ChevronRight',
  'chevron-up': 'ChevronUp',
  'chevron-down': 'ChevronDown',
  'bars': 'Menu',
  'ellipsis-v': 'MoreVertical',
  'ellipsis-h': 'MoreHorizontal',
  'info': 'Info',
  'question': 'HelpCircle',
  'exclamation': 'AlertCircle',
  'bell': 'Bell',
  'folder': 'Folder',
  'file': 'File',
  'cloud': 'Cloud',
  'wifi': 'Wifi',
  'battery': 'Battery',
  'volume-up': 'Volume2',
  'volume-down': 'Volume1',
  'volume-off': 'VolumeX',
  'play': 'Play',
  'pause': 'Pause',
  'stop': 'Square',
  'refresh': 'RefreshCw',
  'sync': 'RefreshCw',
  'filter': 'Filter',
  'sort': 'ArrowUpDown',

  // MaterialCommunityIcons 映射
  'account': 'User',
  'account-multiple': 'Users',
  'home': 'Home',
  'magnify': 'Search',
  'heart': 'Heart',
  'star': 'Star',
  'bookmark': 'Bookmark',
  'share': 'Share',
  'message': 'MessageCircle',
  'phone': 'Phone',
  'email': 'Mail',
  'camera': 'Camera',
  'image': 'Image',
  'video': 'Video',
  'music': 'Music',
  'calendar': 'Calendar',
  'clock': 'Clock',
  'map': 'Map',
  'map-marker': 'MapPin',
  'pencil': 'Edit',
  'delete': 'Trash2',
  'content-save': 'Save',
  'download': 'Download',
  'upload': 'Upload',
  'content-copy': 'Copy',
  'settings': 'Settings',
  'lock': 'Lock',
  'lock-open': 'Unlock',
  'eye': 'Eye',
  'eye-off': 'EyeOff',
  'plus': 'Plus',
  'minus': 'Minus',
  'close': 'X',
  'check': 'Check',
  'arrow-left': 'ArrowLeft',
  'arrow-right': 'ArrowRight',
  'arrow-up': 'ArrowUp',
  'arrow-down': 'ArrowDown',
  'chevron-left': 'ChevronLeft',
  'chevron-right': 'ChevronRight',
  'chevron-up': 'ChevronUp',
  'chevron-down': 'ChevronDown',
  'menu': 'Menu',
  'dots-vertical': 'MoreVertical',
  'dots-horizontal': 'MoreHorizontal',
  'information': 'Info',
  'help': 'HelpCircle',
  'alert': 'AlertCircle',
  'bell': 'Bell',
  'folder': 'Folder',
  'file': 'File',
  'cloud': 'Cloud',
  'wifi': 'Wifi',
  'battery': 'Battery',
  'volume-high': 'Volume2',
  'volume-low': 'Volume1',
  'volume-off': 'VolumeX',
  'play': 'Play',
  'pause': 'Pause',
  'stop': 'Square',
  'refresh': 'RefreshCw',
  'filter': 'Filter',
  'sort': 'ArrowUpDown',
  'tag': 'Tag',
  'tag-multiple': 'Tags',
  'note': 'FileText',
  'notebook': 'Notebook',
  'reminder': 'Bell',
  'timeline': 'Timeline',
  'network': 'Network',
  'link': 'Link',
  'link-variant': 'ExternalLink',
};

// 扫描并替换文件
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  let newContent = content;
  let hasChanges = false;

  // 1. 移除所有 @expo/vector-icons 的导入
  const expoIconImportPattern = /import\s*{\s*[^}]+\s*}\s*from\s*['"]@expo\/vector-icons['"];?\s*/g;
  newContent = newContent.replace(expoIconImportPattern, '');
  if (expoIconImportPattern.test(content)) {
    hasChanges = true;
  }

  // 2. 移除相关的类型定义
  const typePatterns = [
    /type\s+IoniconsName\s*=.*?;/g,
    /type\s+MaterialIconsName\s*=.*?;/g,
    /type\s+MaterialCommunityIconsName\s*=.*?;/g,
    /type\s+FontAwesome5Name\s*=.*?;/g,
    /type\s+AntDesignName\s*=.*?;/g,
    /type\s+AnyIconName\s*=.*?;/g,
    /type\s+IconType\s*=.*?;/g,
  ];

  typePatterns.forEach(pattern => {
    if (pattern.test(newContent)) {
      newContent = newContent.replace(pattern, '');
      hasChanges = true;
    }
  });

  // 3. 收集需要的 Lucide 图标
  const lucideIconsUsed = new Set();

  // 替换图标使用并收集需要的图标
  Object.keys(iconMapping).forEach(originalIcon => {
    const lucideIcon = iconMapping[originalIcon];
    
    // 替换 Ionicons, MaterialIcons, FontAwesome5, MaterialCommunityIcons, AntDesign 使用
    const iconUsagePatterns = [
      new RegExp(`<Ionicons\\s+name=['"]${originalIcon}['"]([^>]*)(?:/>|>[^<]*</Ionicons>)`, 'g'),
      new RegExp(`<MaterialIcons\\s+name=['"]${originalIcon}['"]([^>]*)(?:/>|>[^<]*</MaterialIcons>)`, 'g'),
      new RegExp(`<FontAwesome5\\s+name=['"]${originalIcon}['"]([^>]*)(?:/>|>[^<]*</FontAwesome5>)`, 'g'),
      new RegExp(`<MaterialCommunityIcons\\s+name=['"]${originalIcon}['"]([^>]*)(?:/>|>[^<]*</MaterialCommunityIcons>)`, 'g'),
      new RegExp(`<AntDesign\\s+name=['"]${originalIcon}['"]([^>]*)(?:/>|>[^<]*</AntDesign>)`, 'g'),
    ];

    iconUsagePatterns.forEach(pattern => {
      newContent = newContent.replace(pattern, (match, props) => {
        hasChanges = true;
        lucideIconsUsed.add(lucideIcon);
        
        // 解析 props 并转换 size 属性
        let newProps = props;
        
        // 转换 size 属性值
        newProps = newProps.replace(/size\s*=\s*{?(\d+)}?/, 'size={$1}');
        newProps = newProps.replace(/size\s*=\s*["'](\d+)["']/, 'size={$1}');
        
        return `<${lucideIcon}${newProps} />`;
      });
    });
  });

  // 4. 替换复杂的图标属性映射
  // 处理动态图标 name 属性
  const dynamicIconPatterns = [
    /icon:\s*['"]([^'"]+)['"]/g,
    /iconName:\s*['"]([^'"]+)['"]/g,
    /name:\s*['"]([^'"]+)['"]/g,
  ];

  dynamicIconPatterns.forEach(pattern => {
    newContent = newContent.replace(pattern, (match, iconName) => {
      if (iconMapping[iconName]) {
        hasChanges = true;
        lucideIconsUsed.add(iconMapping[iconName]);
        return match.replace(iconName, iconMapping[iconName]);
      }
      return match;
    });
  });

  // 5. 替换接口中的图标类型定义
  newContent = newContent.replace(/icon:\s*AnyIconName;?/g, 'icon: React.ComponentType<any>;');
  newContent = newContent.replace(/iconType:\s*IconType;?/g, '');
  newContent = newContent.replace(/iconType\?:\s*IconType;?/g, '');

  if (/icon:\s*React\.ComponentType<any>/.test(newContent) && !newContent.includes('import React')) {
    // 添加 React 导入
    const firstImportMatch = newContent.match(/^import.*from.*;$/m);
    if (firstImportMatch) {
      newContent = newContent.replace(firstImportMatch[0], `import React from 'react';\n${firstImportMatch[0]}`);
      hasChanges = true;
    }
  }

  // 6. 添加 lucide-react-native 导入
  if (lucideIconsUsed.size > 0) {
    // 查找最后一个 import 语句的位置
    const lines = newContent.split('\n');
    let lastImportIndex = -1;
    
    lines.forEach((line, index) => {
      if (line.match(/^import.*from.*;$/)) {
        lastImportIndex = index;
      }
    });
    
    if (lastImportIndex >= 0) {
      const lucideImport = `import { ${Array.from(lucideIconsUsed).sort().join(', ')} } from 'lucide-react-native';`;
      lines.splice(lastImportIndex + 1, 0, lucideImport);
      newContent = lines.join('\n');
      hasChanges = true;
    }
  }

  // 7. 清理空行
  newContent = newContent.replace(/\n\n\n+/g, '\n\n');

  if (hasChanges) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`✅ Updated: ${filePath}`);
    console.log(`   📦 Added Lucide icons: ${Array.from(lucideIconsUsed).join(', ')}`);
  }
}

// 递归处理目录
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 跳过 node_modules 和其他不需要的目录
      if (!['node_modules', '.git', '.expo', 'dist', 'build'].includes(item)) {
        processDirectory(fullPath);
      }
    } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
      processFile(fullPath);
    }
  }
}

// 主函数
function main() {
  const srcPath = path.join(__dirname, '..', 'src');
  
  console.log('🚀 开始完整迁移图标到 Lucide...');
  console.log(`📂 处理目录: ${srcPath}`);
  
  if (!fs.existsSync(srcPath)) {
    console.error('❌ src 目录不存在');
    process.exit(1);
  }
  
  processDirectory(srcPath);
  
  console.log('✨ 图标迁移完成！');
  console.log('💡 提示：请检查迁移后的代码，可能需要手动调整一些图标的使用方式。');
  console.log('🔍 特别注意：');
  console.log('   - 检查是否有遗漏的图标引用');
  console.log('   - 验证图标在界面上是否正确显示');
  console.log('   - 某些复杂的动态图标可能需要手动处理');
}

if (require.main === module) {
  main();
}

module.exports = { processFile, processDirectory, iconMapping };
