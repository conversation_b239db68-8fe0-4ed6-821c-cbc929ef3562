import React from 'react';
import {
  Button as GluestackButton,
  ButtonText,
  ButtonIcon,
  ButtonSpinner,
} from '@gluestack-ui/themed';
import { DESIGN_SYSTEM } from '../../constants/designSystem';

interface StandardButtonProps {
  children?: React.ReactNode;
  variant?: 'solid' | 'outline' | 'link' | 'ghost';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  action?: 'primary' | 'secondary' | 'positive' | 'negative';
  isDisabled?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ComponentType<any>;
  rightIcon?: React.ComponentType<any>;
  onPress?: () => void;
  style?: any;
  testID?: string;
}

/**
 * 标准化按钮组件
 * 确保所有按钮遵循设计系统规范
 */
const StandardButton: React.FC<StandardButtonProps> = ({
  children,
  variant = 'solid',
  size = 'md',
  action = 'primary',
  isDisabled = false,
  isLoading = false,
  leftIcon,
  rightIcon,
  onPress,
  style,
  testID,
}) => {
  const buttonSize = DESIGN_SYSTEM.BUTTON.sizes[size];

  return (
    <GluestackButton
      variant={variant}
      size={size}
      action={action}
      isDisabled={isDisabled || isLoading}
      onPress={onPress}
      testID={testID}
      sx={{
        height: buttonSize.height,
        paddingHorizontal: buttonSize.paddingHorizontal,
        paddingVertical: buttonSize.paddingVertical,
        borderRadius: DESIGN_SYSTEM.BORDER_RADIUS.md,
        ...style,
      }}
    >
      {isLoading ? (
        <ButtonSpinner />
      ) : (
        <>
          {leftIcon && (
            <ButtonIcon 
              as={leftIcon} 
              size={DESIGN_SYSTEM.ICON_SIZES[size === 'xs' ? 'sm' : size === 'sm' ? 'md' : 'lg']}
              mr="$2"
            />
          )}
          
          {typeof children === 'string' ? (
            <ButtonText
              fontSize={buttonSize.fontSize}
              fontWeight={DESIGN_SYSTEM.TYPOGRAPHY.fontWeight.medium}
            >
              {children}
            </ButtonText>
          ) : (
            children
          )}
          
          {rightIcon && (
            <ButtonIcon 
              as={rightIcon} 
              size={DESIGN_SYSTEM.ICON_SIZES[size === 'xs' ? 'sm' : size === 'sm' ? 'md' : 'lg']}
              ml="$2"
            />
          )}
        </>
      )}
    </GluestackButton>
  );
};

export default StandardButton;
