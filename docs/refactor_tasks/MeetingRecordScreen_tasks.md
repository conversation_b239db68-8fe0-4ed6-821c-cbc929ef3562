# MeetingRecordScreen Refactor Tasks

- **重构状态:** ToDo
- **主要区域/视图:**
    - **1. 顶部导航/操作区 (Header)**
        - **描述:** 包含返回按钮、屏幕标题 (\"Meeting Record\" 或会议主题)、保存按钮。
        - **现有主要组件:** `View`, `TouchableOpacity`, `Ionicons`, `Text`.
        - **Gluestack UI 迁移建议:**
            - `View` (header container) -> `HStack` or `Box` with `justifyContent=\"space-between\"`, `alignItems=\"center\"`.
            - `TouchableOpacity` (back, save) -> `Pressable` or `Button` (icon only or text, `variant=\"link\"` for back, `variant=\"solid\"` for save).
            - `Ionicons` -> `lucide-react-native` icons (e.g., `ChevronLeft`, `Save`).
            - `Text` (screen title) -> `Heading`.
        - **设计规范参考:** 页面顶部标题与内容间距, 主色 (`#007AFF`).

    - **2. 会议基本信息输入区 (Basic Meeting Info)**
        - **描述:** 输入会议主题、选择日期和时间、选择参与的联系人。
        - **现有主要组件:** `View`, `TextInput` (title), `TouchableOpacity` (date/time picker trigger), `Text` (date/time display), `DateTimePickerModal`, `FlatList` or custom component for contact selection.
        - **Gluestack UI 迁移建议:**
            - `View` (section container) -> `VStack` or `Box` with `p=\"$4\"`.
            - `TextInput` (title) -> `Input` component with `InputField`.
            - `TouchableOpacity` (date/time picker trigger) -> `Button` (`variant=\"outline\"`) or `Pressable`.
            - `Text` (date/time display) -> `Text`.
            - `DateTimePickerModal` -> Retain if no direct `gluestack-ui` equivalent. Style wrapper if needed.
            - Contact selection:
                - If simple list: `Select` component (if contacts are few and pre-loaded).
                - If searchable/multi-select: Custom component using `Input` for search, `FlatList` for results, `Checkbox` for selection, all wrapped in `gluestack-ui` components.
        - **设计规范参考:** 输入框样式, 按钮样式, 列表选择样式.

    - **3. 会议议程/笔记区 (Agenda/Notes)**
        - **描述:** 多行文本输入框，用于记录会议议程或笔记。
        - **现有主要组件:** `TextInput` (multiline).
        - **Gluestack UI 迁移建议:**
            - `TextInput` (multiline) -> `Textarea` component with `TextareaInput`.
        - **设计规范参考:** 多行输入框样式, 边框, 占位符文本.

    - **4. 附件区 (Attachments)**
        - **描述:** 显示已附加的文件列表，提供添加附件的按钮。
        - **现有主要组件:** `View`, `TouchableOpacity` (add attachment), `FlatList` (for displaying attachments), `Text` (file name), `Ionicons` (file type icon, remove icon).
        - **Gluestack UI 迁移建议:**
            - `View` (section container) -> `VStack` or `Box`.
            - `TouchableOpacity` (add attachment) -> `Button` (`variant=\"outline\"` or `solid`) with `lucide-react-native` icon (e.g., `Paperclip`).
            - `FlatList` -> `FlatList`.
            - Attachment item: `HStack` with `Icon` (file type), `Text` (file name), `Button` (icon only, `variant=\"link\"` for remove, e.g., `X` icon).
            - `DocumentPicker` logic will remain.
        - **设计规范参考:** 按钮样式, 列表项样式, 图标.

    - **5. 关联任务/行动项区 (Action Items)**
        - **描述:** 列出会议相关的任务或行动项，提供添加按钮。
        - **现有主要组件:** `View`, `TouchableOpacity` (add action item), `FlatList` (displaying action items), `Text` (action item description, due date, assignee), `Checkbox` (for completion status).
        - **Gluestack UI 迁移建议:**
            - `View` (section container) -> `VStack` or `Box`.
            - `TouchableOpacity` (add action item) -> `Button` (`variant=\"outline\"` or `solid`) with `lucide-react-native` icon (e.g., `PlusCircle`).
            - `FlatList` -> `FlatList`.
            - Action item: `HStack` or `VStack` with `Checkbox`, `Text` (description), `Text` (due date, assignee). Consider using `Badge` for status or assignee.
        - **设计规范参考:** 按钮样式, 列表项样式, 复选框, 徽章.

    - **6. 标签区 (Tags)**
        - **描述:** 为会议记录添加或显示标签。
        - **现有主要组件:** `View`, `TouchableOpacity` (manage tags), `FlatList` or horizontal `ScrollView` for displaying tags.
        - **Gluestack UI 迁移建议:**
            - `View` (section container) -> `VStack` or `Box`.
            - `TouchableOpacity` (manage tags) -> `Button` (`variant=\"link\"` or `outline`).
            - Displayed tags: `HStack` of `Badge` components.
            - Tag selection/management might involve a `Modal` (see below).
        - **设计规范参考:** 按钮样式, 徽章样式.

    - **7. 模态框 (Modals - e.g., for Contact Selection, Tag Management)**
        - **描述:** 如果联系人选择或标签管理是通过模态框进行的。
        - **现有主要组件:** `Modal`, `View`, `TextInput`, `FlatList`, `Button`.
        - **Gluestack UI 迁移建议:**
            - `Modal` -> `Modal` component from `gluestack-ui`.
                - `Modal.Backdrop`, `Modal.Content`, `Modal.Header`, `Modal.Body`, `Modal.Footer`, `Modal.CloseButton`.
            - Components within modal: `Input`, `FlatList` (with `gluestack-ui` items), `Button`.
        - **设计规范参考:** 模态框样式, 内部组件样式.

- **整体注意事项:**
    - **图标替换:** All `Ionicons` and other icon sets to `lucide-react-native`.
    - **颜色和样式:** Migrate `StyleSheet` to `gluestack-ui` props/`sx`, using theme tokens.
    - **表单处理:** Consider form validation libraries if not already in place, integrate with `gluestack-ui` input states (e.g., error state).
    - **状态管理:** Existing state management for form data, attachments, action items will be preserved but UI components updated.
    - **外部库:** `DateTimePickerModal`, `DocumentPicker` will be retained. Ensure their UI triggers are styled with `gluestack-ui`.
