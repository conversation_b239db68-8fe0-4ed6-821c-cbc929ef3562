import React from 'react';

import {
  Text,
  Box,
  Spinner,
  ScrollView,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription
} from '@gluestack-ui/themed';

import { RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../types';

// Import global state and hooks
import { useContactsStore, useUIActions, useUISelectors, useAppActions, useRelationshipStore } from '../store';
import { useNetworkError, usePerformanceMonitor } from '../hooks';
import { ErrorBoundary } from '../components/ui';
import { CommunicationHistory } from '../components/communication/CommunicationHistory';
import { RelationshipAnalysisReport } from '../components/relationship/RelationshipAnalysisReport';
import { SimpleRelationshipGraph } from '../components/relationship/SimpleRelationshipGraph';
import { relationshipAnalyzer } from '../services/relationshipAnalyzer';

import ContactHeader from "../features/ContactDetail/components/ContactHeader";
import TabNavigator from '../features/ContactDetail/components/TabNavigator';
import ProfileTab from '../features/ContactDetail/components/ProfileTab';
import InteractionsTab from '../features/ContactDetail/components/InteractionsTab';
import NetworkTab from '../features/ContactDetail/components/NetworkTab';
import TagModal from '../features/ContactDetail/components/TagModal';
import InteractionFormModal from '../features/ContactDetail/components/InteractionFormModal';
import NotesEditModal from '../features/ContactDetail/components/NotesEditModal';
import { Tag as TagIcon, Network } from 'lucide-react-native';
import ReminderModal from '../features/ContactDetail/components/ReminderModal'; 

import {
  useContactDetail,
  ContactData,
  Tag,
  Interaction,
  Reminder,
  ContactDetailScreenRouteProp,
} from '../features/ContactDetail/hooks/useContactDetail';

interface ContactDetailScreenProps {
  navigation: NativeStackNavigationProp<RootStackParamList, 'ContactDetail'>;
  route: ContactDetailScreenRouteProp;
}



export default function ContactDetailScreen({ navigation, route }: ContactDetailScreenProps) {
  const toast = useToast();

  // Performance monitoring
  usePerformanceMonitor('ContactDetailScreen');

  // Global state
  const { contacts, updateContact, toggleFavorite } = useContactsStore();
  const { communications } = useCommunicationStore();
  const { relationships, initializeSampleData } = useRelationshipStore();
  const { setLoading } = useAppActions();
  const { showError, clearError } = useNetworkError();

  // 初始化示例关系数据
  React.useEffect(() => {
    initializeSampleData();
  }, [initializeSampleData]);

  // Get contact from global store instead of mock data
  const contactId = route.params?.contactId;
  const contact = contacts.find(c => c.id === contactId);

  // Use the existing hook for UI state management (for now)
  // TODO: Gradually migrate this to global state as well
  const {
    activeTab,
    setActiveTab,
    // Notes Modal
    isNotesModalVisible,
    editingNotes,
    handleEditNotes,
    saveNotesFromModal,
    closeNotesModal,
    // Reminder Modal
    isReminderModalVisible,
    editingReminder,
    handleAddReminder,
    saveReminderFromModal,
    closeReminderModal,
    handleCompleteReminder,
    handleEditReminder,
    // Tag Modal
    isTagModalVisible,
    editingTag,
    openCreateTagModal,
    openEditTagModal,
    closeTagModal,
    handleSaveTag,
    handleDeleteTag,
    // Interaction Modal
    isInteractionModalVisible,
    openInteractionModal,
    closeInteractionModal,
    handleTriggerAddInteraction,
    handleSaveNewInteraction,
    handleViewInteractionDetail,
    // Interaction Filters
    filteredInteractions,
    interactionFilterType,
    setInteractionFilterType,
    interactionFilterDateRange,
    setInteractionFilterDateRange,
    interactionSearchText,
    setInteractionSearchText,
    // Navigation actions
    handleViewConnection,
    handleViewNetworkFromContact,
    handleTagPress,
  } = useContactDetail(route);

  const renderTabContent = () => {
    if (!contact) {
      return (<Spinner size="large" color={'$primary500'} mt={'$4'} />);
    }

    const tabComponents: { [key: string]: JSX.Element } = {
      profile: (
        <ProfileTab
          contact={contact}
          onAddTag={openCreateTagModal}
          onEditTag={openEditTagModal}
          onEditNotes={handleEditNotes}
          onAddReminder={handleAddReminder}
          onCompleteReminder={handleCompleteReminder}
          onTagPress={handleTagPress} // Added missing prop
          onEditReminder={handleEditReminder} // Added prop for editing reminders
        />
      ),
      interactions: (
        <InteractionsTab
          interactions={filteredInteractions}
          onAddInteraction={handleTriggerAddInteraction}
          onViewInteractionDetail={handleViewInteractionDetail}
          filterType={interactionFilterType}
          setFilterType={setInteractionFilterType}
          filterDateRange={interactionFilterDateRange}
          setFilterDateRange={setInteractionFilterDateRange}
          searchText={interactionSearchText}
          setSearchText={setInteractionSearchText}
        />
      ),
      communications: (
        <CommunicationHistory
          contactId={contact.id}
          onAddCommunication={() => {
            // TODO: 导航到沟通记录创建页面
            toast.show({
              placement: "top",
              render: ({ id }) => (
                <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                  <ToastTitle>添加沟通记录</ToastTitle>
                  <ToastDescription>沟通记录创建功能即将推出</ToastDescription>
                </Toast>
              )
            });
          }}
          showAnalysis={true}
          maxItems={undefined}
        />
      ),
      analysis: (
        (() => {
          const contactCommunications = communications.filter(c => c.primaryContactId === contact.id);
          const analysis = relationshipAnalyzer.analyzeRelationship(contact, contactCommunications, []);

          return (
            <RelationshipAnalysisReport
              analysis={analysis}
              contactName={contact.name}
              communications={contactCommunications}
              showInsights={true}
              showTrend={true}
              compact={false}
            />
          );
        })()
      ),
      graph: (
        <SimpleRelationshipGraph
          centerContactId={contact.id}
          contacts={contacts}
          relationships={relationships}
          maxDepth={2}
          onContactPress={(contactId) => {
            if (contactId !== contact.id) {
              navigation.navigate('ContactDetail', { contactId });
            }
          }}
          onAddRelationship={(fromId, toId) => {
            // TODO: 导航到关系添加页面
            toast.show({
              placement: "top",
              render: ({ id }) => (
                <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                  <ToastTitle>添加关系</ToastTitle>
                  <ToastDescription>关系添加功能即将推出</ToastDescription>
                </Toast>
              )
            });
          }}
        />
      ),
      network: (
        <NetworkTab
          contactName={contact.name} // Added missing prop
          connections={contact.connections || []}
          onViewConnection={handleViewConnection}
          onViewNetworkFromContact={handleViewNetworkFromContact} // Corrected prop name
        />
      ),
    };

    // Fallback to profile tab if activeTab is not a key in tabComponents or is undefined
    return tabComponents[activeTab] || tabComponents.profile;
  };

  // Enhanced handlers using global state
  const handleEditContact = () => {
    if (contact) {
      // Navigate to ContactCreateScreen with contact data for editing
      navigation.navigate('ContactCreate', { contact });
    }
  };

  const handleToggleFavorite = () => {
    if (contact) {
      toggleFavorite(contact.id);
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={`toast-${id}`} action="success" variant="accent">
            <ToastTitle>{contact.isFavorite ? 'Removed from Favorites' : 'Added to Favorites'}</ToastTitle>
            <ToastDescription>
              {contact.name} has been {contact.isFavorite ? 'removed from' : 'added to'} your favorites.
            </ToastDescription>
          </Toast>
        )
      });
    }
  };

  return (
    <ErrorBoundary>
      <Box flex={1} bg="$backgroundLight50">
        <ScrollView flex={1}>
          {contact ? (
            <>
              <ContactHeader
                contact={contact}
                onNavigateBack={() => navigation.goBack()}
                onEditContact={handleEditContact}
              onMoreOptions={() => toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                    <ToastTitle>Contact Options</ToastTitle>
                    <ToastDescription>Additional options will appear here.</ToastDescription>
                  </Toast>
                )
              })}
              onCall={() => toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                    <ToastTitle>Call</ToastTitle>
                    <ToastDescription>{`Calling ${contact.name} at ${contact.phone}`}</ToastDescription>
                  </Toast>
                )
              })}
              onEmail={() => toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                    <ToastTitle>Email</ToastTitle>
                    <ToastDescription>{`Composing email to ${contact.email}`}</ToastDescription>
                  </Toast>
                )
              })}
              onMessage={() => toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={`toast-${id}`} action="info" variant="accent">
                    <ToastTitle>Message</ToastTitle>
                    <ToastDescription>{`Messaging ${contact.name}`}</ToastDescription>
                  </Toast>
                )
              })}
              onSchedule={() => {
                if (contact?.id) {
                  navigation.navigate('MeetingRecord', { contactId: contact.id });
                } else {
                  toast.show({
                  placement: "top",
                  render: ({ id }) => (
                    <Toast nativeID={`toast-${id}`} action="error" variant="accent">
                      <ToastTitle>Error</ToastTitle>
                      <ToastDescription>Contact ID is missing, cannot schedule meeting.</ToastDescription>
                    </Toast>
                  ),
                });
                }
              }}
            />
            
            <TabNavigator activeTab={activeTab} setActiveTab={setActiveTab} />
            <Box p={"$4"}>
              {renderTabContent()}
            </Box>
          </>
        ) : (
          // TODO: Replace with Gluestack UI token for ActivityIndicator color and potentially style
          <Spinner size="large" color={'$primary500'} flex={1} justifyContent='center' alignItems='center' />
        )}
      </ScrollView>

      <TagModal
        visible={isTagModalVisible}
        tag={editingTag}
        onClose={closeTagModal}
        onSave={handleSaveTag}
        onDelete={handleDeleteTag}
      />

      <InteractionFormModal 
        visible={isInteractionModalVisible}
        onClose={closeInteractionModal}
        onSave={handleSaveNewInteraction} // Corrected prop
      />

      <NotesEditModal
        visible={isNotesModalVisible}
        initialNotes={editingNotes} 
        onSave={saveNotesFromModal}
        onClose={closeNotesModal}
      />

      <ReminderModal
        visible={isReminderModalVisible}
        reminder={editingReminder}
        onSave={saveReminderFromModal}
        onClose={closeReminderModal}
      />
    </Box>
    </ErrorBoundary>
  );
}