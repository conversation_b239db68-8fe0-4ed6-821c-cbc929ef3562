import React, { useState, useEffect, useMemo } from 'react';
import { Platform, Dimensions } from 'react-native';
import {
  Box,
  Text,
  Heading,
  VStack,
  HStack,
  Button,
  ButtonText,
  ButtonIcon,
  Pressable,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  ScrollView,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
  Badge,
  BadgeText,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
} from '@gluestack-ui/themed';
import { useNavigation, RouteProp } from '@react-navigation/native';
import {
  ArrowLeft,
  Share,
  Settings,
  UserPlus,
  Plus,
  Minus,
  RotateCcw,
  Filter,
  Users,
  Network,
  X,
  Eye,
  MessageCircle,
  ChevronRight,
} from 'lucide-react-native';
import { PanGestureHandler, PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';

// Import global state and hooks
import { useContactsStore, useContactsSelectors } from '../store';
import { usePerformanceMonitor, useNetworkError } from '../hooks';
import { ErrorBoundary, StandardButton, StandardIcon, CircularButton } from '../components/ui';
import { Contact } from '../types';

// Types and interfaces
type NetworkVisualScreenNavigationProp = any; // TODO: 添加正确的导航类型

interface NetworkNode {
  id: string;
  name: string;
  type: 'self' | 'contact';
  x: number;
  y: number;
  connections: string[];
  industry?: string;
  avatar?: string;
  company?: string;
  position?: string;
  connectionStrength?: number; // 0-1, 连接强度
  lastInteraction?: Date;
}

interface ConnectionLink {
  from: string;
  to: string;
  strength: number; // 连接强度
  type: 'direct' | 'mutual'; // 直接连接或通过共同联系人
}

interface NetworkStats {
  totalContacts: number;
  directConnections: number;
  mutualConnections: number;
  industries: Record<string, number>;
  strongConnections: number; // 强连接数量
}

type GestureContext = {
  startX: number;
  startY: number;
};

const { width, height } = Dimensions.get('window');

// Industry colors mapping
const industryColors: Record<string, string> = {
  'Technology': '#3B82F6', // Blue
  'Finance': '#8B5CF6', // Purple
  'Marketing': '#F59E0B', // Orange
  'Sales': '#10B981', // Green
  'Healthcare': '#EF4444', // Red
  'Education': '#6366F1', // Indigo
  'Consulting': '#84CC16', // Lime
  'Media': '#EC4899', // Pink
  'self': '#1F2937', // Dark gray
  'default': '#6B7280', // Gray
};

// Connection strength colors
const connectionColors = {
  strong: '#10B981', // Green
  medium: '#F59E0B', // Orange
  weak: '#6B7280', // Gray
};

/**
 * 关系网络可视化屏幕 - 完全重构版本
 * 使用Gluestack UI和全局状态管理
 */
const NetworkVisualScreen: React.FC = () => {
  const navigation = useNavigation<NetworkVisualScreenNavigationProp>();
  const toast = useToast();

  // Performance monitoring
  usePerformanceMonitor('NetworkVisualScreen');

  // Global state
  const { contacts } = useContactsStore();
  const { showError, clearError } = useNetworkError();

  // Local state
  const [selectedNode, setSelectedNode] = useState<NetworkNode | null>(null);
  const [industryFilter, setIndustryFilter] = useState<string>('All');
  const [connectionFilter, setConnectionFilter] = useState<string>('All'); // All, Strong, Medium, Weak
  const [scale, setScale] = useState(1);
  const [showNodeModal, setShowNodeModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [layoutType, setLayoutType] = useState<'circular' | 'force' | 'hierarchical'>('circular');
  const [showConnectionLabels, setShowConnectionLabels] = useState(false);

  // Animated values for pan and zoom
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const animatedScale = useSharedValue(1);

  // Generate network data from contacts
  const networkData = useMemo(() => {
    const centerX = width / 2;
    const centerY = height / 2.5;

    // Create user node
    const userNode: NetworkNode = {
      id: 'user',
      name: '我',
      type: 'self',
      x: centerX,
      y: centerY,
      connections: contacts.map(c => c.id),
      industry: 'self',
    };

    // Create contact nodes in circular layout
    const contactNodes: NetworkNode[] = contacts.map((contact, index) => {
      const angle = (index / contacts.length) * 2 * Math.PI;
      const radius = 120 + Math.random() * 80; // Add some randomness

      return {
        id: contact.id,
        name: contact.name,
        type: 'contact',
        x: centerX + Math.cos(angle) * radius,
        y: centerY + Math.sin(angle) * radius,
        connections: ['user'], // For now, all contacts connect to user
        industry: contact.company || 'default',
        avatar: contact.avatar,
        company: contact.company,
        position: contact.position,
        connectionStrength: Math.random(), // Random for demo
        lastInteraction: contact.lastContactDate,
      };
    });

    return [userNode, ...contactNodes];
  }, [contacts, width, height]);

  // Generate connections
  const connections = useMemo((): ConnectionLink[] => {
    const links: ConnectionLink[] = [];

    networkData.forEach(node => {
      if (node.type === 'contact') {
        // Add connection to user
        links.push({
          from: 'user',
          to: node.id,
          strength: node.connectionStrength || 0.5,
          type: 'direct',
        });

        // Add some mutual connections (demo)
        if (Math.random() > 0.7) {
          const otherContacts = networkData.filter(n => n.type === 'contact' && n.id !== node.id);
          if (otherContacts.length > 0) {
            const randomContact = otherContacts[Math.floor(Math.random() * otherContacts.length)];
            links.push({
              from: node.id,
              to: randomContact.id,
              strength: Math.random() * 0.5,
              type: 'mutual',
            });
          }
        }
      }
    });

    return links;
  }, [networkData]);

  // Calculate network statistics
  const networkStats = useMemo((): NetworkStats => {
    const industries: Record<string, number> = {};
    let strongConnections = 0;

    networkData.forEach(node => {
      if (node.type === 'contact') {
        const industry = node.industry || 'default';
        industries[industry] = (industries[industry] || 0) + 1;

        if ((node.connectionStrength || 0) > 0.7) {
          strongConnections++;
        }
      }
    });

    return {
      totalContacts: contacts.length,
      directConnections: contacts.length,
      mutualConnections: connections.filter(c => c.type === 'mutual').length,
      industries,
      strongConnections,
    };
  }, [networkData, connections, contacts.length]);

  // Pan gesture handler
  const panGestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent, GestureContext>({
    onStart: (_, ctx) => {
      ctx.startX = translateX.value;
      ctx.startY = translateY.value;
    },
    onActive: (event, ctx) => {
      translateX.value = ctx.startX + event.translationX;
      translateY.value = ctx.startY + event.translationY;
    },
    onEnd: () => {
      // Optional: Add spring animation when releasing
    },
  });

  // Animated styles for the graph container
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: animatedScale.value }
      ],
    };
  });

  // Filter nodes based on industry and connection strength
  const filteredNodes = useMemo(() => {
    let nodes = networkData;

    // Industry filter
    if (industryFilter !== 'All') {
      nodes = nodes.filter(node =>
        node.type === 'self' || node.industry === industryFilter
      );
    }

    return nodes;
  }, [networkData, industryFilter]);

  // Filter connections based on strength and visible nodes
  const filteredConnections = useMemo(() => {
    const nodeIds = new Set(filteredNodes.map(n => n.id));
    let filteredConns = connections.filter(conn =>
      nodeIds.has(conn.from) && nodeIds.has(conn.to)
    );

    // Connection strength filter
    if (connectionFilter === 'Strong') {
      filteredConns = filteredConns.filter(conn => conn.strength > 0.7);
    } else if (connectionFilter === 'Medium') {
      filteredConns = filteredConns.filter(conn => conn.strength > 0.4 && conn.strength <= 0.7);
    } else if (connectionFilter === 'Weak') {
      filteredConns = filteredConns.filter(conn => conn.strength <= 0.4);
    }

    return filteredConns;
  }, [connections, filteredNodes, connectionFilter]);

  // Get available industries for filter
  const availableIndustries = useMemo(() => {
    const industries = new Set<string>();
    networkData.forEach(node => {
      if (node.type === 'contact' && node.industry) {
        industries.add(node.industry);
      }
    });
    return Array.from(industries).sort();
  }, [networkData]);

  // Event handlers
  const handleNodePress = (node: NetworkNode) => {
    runOnJS(setSelectedNode)(node);
    runOnJS(setShowNodeModal)(true);
  };

  const handleViewContactDetail = (contactId: string) => {
    if (contactId === 'user') {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="info" variant="accent">
            <ToastTitle>个人资料</ToastTitle>
            <ToastDescription>查看您的个人资料</ToastDescription>
          </Toast>
        )
      });
      return;
    }

    setShowNodeModal(false);
    navigation.navigate('ContactDetail', { contactId });
  };

  const handleAddConnection = () => {
    navigation.navigate('ContactCreate');
  };

  const handleMessageContact = (contactId: string) => {
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={id} action="info" variant="accent">
          <ToastTitle>发送消息</ToastTitle>
          <ToastDescription>消息功能即将推出</ToastDescription>
        </Toast>
      )
    });
  };

  const handleExportNetwork = () => {
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={id} action="info" variant="accent">
          <ToastTitle>导出网络</ToastTitle>
          <ToastDescription>网络导出功能即将推出</ToastDescription>
        </Toast>
      )
    });
  };

  // Zoom and pan controls
  const handleZoomIn = () => {
    if (animatedScale.value < 2) {
      animatedScale.value = withSpring(Math.min(2, animatedScale.value + 0.2));
    }
  };

  const handleZoomOut = () => {
    if (animatedScale.value > 0.5) {
      animatedScale.value = withSpring(Math.max(0.5, animatedScale.value - 0.2));
    }
  };

  const handleReset = () => {
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    animatedScale.value = withSpring(1);
  };

  // Utility functions
  const getNodeColor = (node: NetworkNode) => {
    if (node.type === 'self') {
      return industryColors.self;
    }
    return industryColors[node.industry || 'default'] || industryColors.default;
  };

  const getConnectionColor = (connection: ConnectionLink) => {
    if (connection.strength > 0.7) return connectionColors.strong;
    if (connection.strength > 0.4) return connectionColors.medium;
    return connectionColors.weak;
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Contact Info Modal
  const renderContactModal = () => {
    if (!selectedNode) return null;
    
    const nodeColor = selectedNode.type === 'self'
      ? industryColors.self
      : (selectedNode.industry && industryColors[selectedNode.industry]) || '#999';
    
    return (
      <Modal
        visible={isInfoModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setInfoModalVisible(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setInfoModalVisible(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <View style={[styles.modalAvatar, { backgroundColor: nodeColor }]}>
                <Text style={styles.modalAvatarText}>
                  {selectedNode.name.charAt(0)}
                </Text>
              </View>
              <Text style={styles.modalName}>{selectedNode.name}</Text>
              {selectedNode.type !== 'self' && (
                <Text style={styles.modalIndustry}>{selectedNode.industry}</Text>
              )}
            </View>
            
            <Text style={styles.modalInfoHeader}>Network Information</Text>
            <View style={styles.modalConnectionInfo}>
              <View style={styles.modalInfoItem}>
                <Text style={styles.modalInfoValue}>
                  {selectedNode.connections ? selectedNode.connections.length : 0}
                </Text>
                <Text style={styles.modalInfoLabel}>Connections</Text>
              </View>
              <View style={styles.divider} />
              <View style={styles.modalInfoItem}>
                <Text style={styles.modalInfoValue}>
                  {selectedNode.type === 'self' ? 'You' : selectedNode.id === '1' ? '1st' : '2nd'}
                </Text>
                <Text style={styles.modalInfoLabel}>Degree</Text>
              </View>
            </View>
            
            <View style={styles.modalActions}>
              {selectedNode.type !== 'self' && (
                <>
                  <TouchableOpacity 
                    style={[styles.modalButton, { backgroundColor: '#f0f0f0' }]}
                    onPress={() => {
                      setInfoModalVisible(false);
                      handleViewContactDetail(selectedNode.id);
                    }}
                  >
                    <Text style={styles.modalButtonText}>View Profile</Text>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={[styles.modalButton, { backgroundColor: nodeColor }]}
                    onPress={() => {
                      setInfoModalVisible(false);
                      toast.show({
                        placement: "top",
                        render: ({ id }) => (
                          <Toast nativeID={id} action="info" variant="accent">
                            <ToastTitle>Connect</ToastTitle>
                            <ToastDescription>
                              Messaging {selectedNode.name}.
                            </ToastDescription>
                          </Toast>
                        ),
                      });
                    }}
                  >
                    <Text style={[styles.modalButtonText, { color: 'white' }]}>Message</Text>
                  </TouchableOpacity>
                </>
              )}
              {selectedNode.type === 'self' && (
                <TouchableOpacity 
                  style={[styles.modalButton, { backgroundColor: nodeColor }]}
                  onPress={() => {
                    setInfoModalVisible(false);
                    handleAddConnection();
                  }}
                >
                  <Text style={[styles.modalButtonText, { color: 'white' }]}>Add New Connection</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Your Network</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity 
            style={styles.headerAction}
            onPress={handleCustomizeView}
          >
            <SlidersHorizontal size={22} color="#333" />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.headerAction}
            onPress={handleExportOrShare}
          >
            <Share size={22} color="#333" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Filter Options */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filtersContainer}
      >
        {['All', 'Technology', 'Sales', 'Finance', 'Marketing'].map(filterOption => (
          <TouchableOpacity 
            key={filterOption}
            style={[
              styles.filterChip,
              filter === filterOption && { 
                backgroundColor: Object.prototype.hasOwnProperty.call(industryColors, filterOption)
                  ? industryColors[filterOption as IndustryKey]
                  : (filterOption === 'All' ? '#3498db' : undefined) 
              }
            ]}
            onPress={() => handleFilterChange(filterOption)}
          >
            <Text 
              style={[
                styles.filterText,
                filter === filterOption && { color: 'white' }
              ]}
            >
              {filterOption}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Action Button - Add Connection */}
      <TouchableOpacity 
        style={styles.fabButton}
        onPress={handleAddConnection}
      >
        <UserPlus size={24} color="white" />
      </TouchableOpacity>

      {/* Zoom Controls */}
      <View style={styles.zoomControls}>
        <TouchableOpacity style={styles.zoomButton} onPress={handleZoomIn}>
          <Plus size={20} color="#555" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.zoomButton} onPress={handleZoomOut}>
          <Minus size={20} color="#555" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.zoomButton} onPress={handleReset}>
          <RefreshCw size={20} color="#555" />
        </TouchableOpacity>
      </View>

      {/* Selected Node Info Card (when a node is selected without modal) */}
      {selectedNode && !isInfoModalVisible && (
        <TouchableOpacity 
          style={styles.nodeInfoCard}
          onPress={() => setInfoModalVisible(true)}
        >
          <View style={[styles.nodeAvatar, { 
            backgroundColor: selectedNode.type === 'self' 
              ? industryColors.self 
              : (selectedNode.industry && industryColors[selectedNode.industry]) || '#999'
          }]}>
            <Text style={styles.nodeAvatarText}>{selectedNode.name.charAt(0)}</Text>
          </View>
          <View style={styles.nodeInfo}>
            <Text style={styles.nodeName}>{selectedNode.name}</Text>
            {selectedNode.type !== 'self' && (
              <Text style={styles.nodeIndustry}>{selectedNode.industry}</Text>
            )}
          </View>
          <TouchableOpacity 
            style={styles.nodeAction}
            onPress={() => {
              if (selectedNode.type !== 'self') {
                handleViewContactDetail(selectedNode.id);
              }
            }}
          >
            {selectedNode.type !== 'self' ? (
              <ChevronRight size={22} color="#3498db" />
            ) : (
              <UserCircle2 size={22} color="#3498db" />
            )}
          </TouchableOpacity>
        </TouchableOpacity>
      )}

      {/* Network Graph */}
      <PanGestureHandler onGestureEvent={panGestureHandler}>
        <Animated.View style={[styles.graphContainer, animatedStyle]}>
          {/* Connection Lines */}
          {visibleConnections.map((conn, index) => {
            const fromNode = networkNodes.find(n => n.id === conn.from);
            const toNode = networkNodes.find(n => n.id === conn.to);
            
            if (!fromNode || !toNode) return null;
            
            // Calculate line coordinates
            const fromX = fromNode.x;
            const fromY = fromNode.y;
            const toX = toNode.x;
            const toY = toNode.y;
            
            // Determine style based on relationship
            const isUserConnection = conn.from === 'user' || conn.to === 'user';
            
            return (
              <View 
                key={`line-${index}`} 
                style={[
                  styles.connectionLine,
                  {
                    left: Math.min(fromX, toX),
                    top: Math.min(fromY, toY),
                    width: Math.abs(toX - fromX),
                    height: Math.abs(toY - fromY),
                    backgroundColor: 'transparent',
                  }
                ]}
              >
                <View
                  style={{
                    position: 'absolute',
                    height: 2,
                    width: Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toY - fromY, 2)),
                    backgroundColor: isUserConnection ? '#3498db' : '#aaa',
                    left: 0,
                    top: (Math.abs(toY - fromY)) / 2,
                    transformOrigin: 'left center',
                    transform: [
                      { translateX: 0 },
                      { translateY: 0 },
                      { rotateZ: `${Math.atan2(toY - fromY, toX - fromX)}rad` },
                    ],
                  }}
                />
              </View>
            );
          })}
          
          {/* Network Nodes */}
          {filteredNodes.map(node => {
            const isSelected = selectedNode && selectedNode.id === node.id;
            const isCurrentUser = node.type === 'self';
            const nodeColor = node.type === 'self'
      ? industryColors.self
      : (node.industry && industryColors[node.industry]) || '#CCCCCC';
            
            return (
              <TouchableOpacity
                key={node.id}
                style={[
                  styles.networkNode,
                  { 
                    left: node.x - 30, 
                    top: node.y - 30,
                    backgroundColor: nodeColor,
                    borderWidth: isSelected ? 3 : 0,
                    width: isCurrentUser ? 70 : 60,
                    height: isCurrentUser ? 70 : 60,
                  }
                ]}
                onPress={() => handleNodePress(node)}
              >
                <Text style={styles.nodeLabel}>{node.name.split(' ')[0]}</Text>
              </TouchableOpacity>
            );
          })}
        </Animated.View>
      </PanGestureHandler>
      
      {/* Contact Info Modal */}
      {renderContactModal()}
    </View>
  );
};

export default NetworkVisualScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingHorizontal: 16,
    paddingBottom: 10,
    backgroundColor: 'white',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerAction: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    ...(Platform.OS === 'web' ? 
      { boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.05)' } 
    : 
      { shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 1, }
    ),
  },
  filterChip: {
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#f0f0f0',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
  },
  zoomControls: {
    position: 'absolute',
    right: 16,
    top: 120,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 4,
    ...(Platform.OS === 'web' ? 
      { boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.1)' } 
    : 
      { shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3, }
    ),
    zIndex: 10,
  },
  zoomButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  fabButton: {
    position: 'absolute',
    right: 16,
    bottom: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3498db',
    justifyContent: 'center',
    alignItems: 'center',
    ...(Platform.OS === 'web' ? 
      { boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.2)' } 
    : 
      { shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.2, shadowRadius: 4, elevation: 4, }
    ),
    zIndex: 10,
  },
  nodeInfoCard: {
    position: 'absolute',
    bottom: 24,
    left: 16,
    right: 88,
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 10,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    ...(Platform.OS === 'web' ? 
      { boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.1)' } 
    : 
      { shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3, }
    ),
    zIndex: 10,
  },
  nodeAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  nodeAvatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  nodeInfo: {
    flex: 1,
    marginLeft: 12,
  },
  nodeName: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
  },
  nodeIndustry: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
  nodeAction: {
    padding: 8,
  },
  graphContainer: {
    position: 'absolute',
    width: width * 2,
    height: height * 2,
    left: -width / 2,
    top: -height / 2,
  },
  connectionLine: {
    position: 'absolute',
    overflow: 'visible',
  },
  networkNode: {
    position: 'absolute',
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: 'white',
  },
  nodeLabel: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 13,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: width - 64,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalAvatarText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  modalName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 10,
  },
  modalIndustry: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  modalInfoHeader: {
    fontSize: 14,
    fontWeight: '500',
    color: '#777',
    marginBottom: 10,
  },
  modalConnectionInfo: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 12,
    marginBottom: 20,
  },
  modalInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  modalInfoValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalInfoLabel: {
    fontSize: 12,
    color: '#777',
    marginTop: 4,
  },
  divider: {
    width: 1,
    backgroundColor: '#ddd',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 6,
  },
  modalButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
});