#!/usr/bin/env node

/**
 * 修复项目中所有废弃的 shadow* 样式属性
 * 将它们替换为平台特定的样式实现
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 检查并修复 shadow 样式
function fixShadowStyles(content) {
  let modifiedContent = content;
  let hasChanges = false;
  let addedPlatformImport = false;

  // 检查是否已经有 Platform 导入
  const hasPlatformImport = /import.*Platform.*from\s+['"`]react-native['"`]/.test(content);
  
  // 查找所有 shadow 样式组合的正则表达式
  const shadowStyleRegex = /(\s*)(shadowColor:\s*[^,\n]+,?\s*\n?)([\s\S]*?)(shadowOffset:\s*\{[^}]+\},?\s*\n?)([\s\S]*?)(shadowOpacity:\s*[^,\n]+,?\s*\n?)([\s\S]*?)(shadowRadius:\s*[^,\n]+,?\s*\n?)/g;
  
  let match;
  const matches = [];
  
  // 找到所有的 shadow 样式组合
  while ((match = shadowStyleRegex.exec(content)) !== null) {
    matches.push({
      fullMatch: match[0],
      indent: match[1],
      shadowColor: match[2].trim(),
      shadowOffset: match[4].trim(),
      shadowOpacity: match[6].trim(),
      shadowRadius: match[8].trim(),
      startIndex: match.index
    });
  }
  
  if (matches.length === 0) {
    return { content, hasChanges: false };
  }

  // 从后往前替换，避免索引变化问题
  matches.reverse().forEach(shadowMatch => {
    // 提取样式值
    const shadowColor = shadowMatch.shadowColor.replace(/shadowColor:\s*/, '').replace(/,?\s*$/, '');
    const shadowOffsetMatch = shadowMatch.shadowOffset.match(/shadowOffset:\s*\{\s*width:\s*([^,]+),\s*height:\s*([^}]+)\s*\}/);
    const shadowOpacity = shadowMatch.shadowOpacity.replace(/shadowOpacity:\s*/, '').replace(/,?\s*$/, '');
    const shadowRadius = shadowMatch.shadowRadius.replace(/shadowRadius:\s*/, '').replace(/,?\s*$/, '');
    
    if (!shadowOffsetMatch) return;
    
    const offsetWidth = shadowOffsetMatch[1].trim();
    const offsetHeight = shadowOffsetMatch[2].trim();
    
    // 构建平台特定的样式
    const platformSpecificStyle = `${shadowMatch.indent}...(Platform.OS === 'web' ? {
${shadowMatch.indent}  boxShadow: \`\${${offsetWidth}}px \${${offsetHeight}}px \${${shadowRadius}}px 0px rgba(\${${shadowColor} === '#000000' || ${shadowColor} === '#000' ? '0, 0, 0' : '0, 0, 0'}, \${${shadowOpacity}})\`,
${shadowMatch.indent}} : {
${shadowMatch.indent}  ${shadowMatch.shadowColor}
${shadowMatch.indent}  ${shadowMatch.shadowOffset}
${shadowMatch.indent}  ${shadowMatch.shadowOpacity}
${shadowMatch.indent}  ${shadowMatch.shadowRadius}
${shadowMatch.indent}  elevation: Platform.OS === 'android' ? 5 : 0,
${shadowMatch.indent}}),`;
    
    // 替换原有的 shadow 样式
    const beforeShadow = content.substring(0, shadowMatch.startIndex);
    const afterShadow = content.substring(shadowMatch.startIndex + shadowMatch.fullMatch.length);
    
    modifiedContent = beforeShadow + platformSpecificStyle + afterShadow;
    hasChanges = true;
  });

  // 如果有修改且没有 Platform 导入，则添加
  if (hasChanges && !hasPlatformImport) {
    const reactNativeImportRegex = /(import\s*\{[^}]*)\}\s*from\s*['"`]react-native['"`]/;
    const importMatch = modifiedContent.match(reactNativeImportRegex);
    
    if (importMatch) {
      const importPart = importMatch[1];
      if (!importPart.includes('Platform')) {
        const newImport = `${importPart},\n  Platform,\n} from 'react-native'`;
        modifiedContent = modifiedContent.replace(reactNativeImportRegex, newImport);
        addedPlatformImport = true;
      }
    }
  }

  return { 
    content: modifiedContent, 
    hasChanges, 
    addedPlatformImport,
    shadowGroupsFound: matches.length
  };
}

// 处理单个文件
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { 
      content: newContent, 
      hasChanges, 
      addedPlatformImport,
      shadowGroupsFound 
    } = fixShadowStyles(content);
    
    if (hasChanges) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✅ 已修复: ${filePath} (${shadowGroupsFound} 组 shadow 样式${addedPlatformImport ? ', 添加了 Platform 导入' : ''})`);
      return { fixed: true, shadowGroupsFound };
    }
    
    return { fixed: false, shadowGroupsFound: 0 };
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return { fixed: false, shadowGroupsFound: 0 };
  }
}

// 主函数
function main() {
  console.log('🔧 修复项目中的 shadow 样式问题...\n');
  
  // 查找所有 TypeScript/JavaScript 文件
  const srcPattern = path.join(__dirname, '../src/**/*.{ts,tsx,js,jsx}');
  const files = glob.sync(srcPattern);
  
  let processedCount = 0;
  let modifiedCount = 0;
  let totalShadowGroups = 0;
  
  const results = [];
  
  files.forEach(filePath => {
    processedCount++;
    const result = processFile(filePath);
    
    if (result.fixed) {
      modifiedCount++;
      totalShadowGroups += result.shadowGroupsFound;
      results.push({
        file: path.relative(path.join(__dirname, '..'), filePath),
        shadowGroups: result.shadowGroupsFound
      });
    }
  });
  
  console.log(`\n📊 修复完成:`);
  console.log(`   - 扫描文件: ${processedCount}`);
  console.log(`   - 修复文件: ${modifiedCount}`);
  console.log(`   - 修复 shadow 样式组: ${totalShadowGroups}`);
  
  if (results.length > 0) {
    console.log('\n📋 修复详情:');
    results.forEach(result => {
      console.log(`   🔧 ${result.file} (${result.shadowGroups} 组)`);
    });
  }
  
  if (modifiedCount > 0) {
    console.log('\n✅ 所有 shadow 样式已修复为平台特定的实现！');
  } else {
    console.log('\n✅ 没有发现需要修复的 shadow 样式。');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { fixShadowStyles, processFile };
