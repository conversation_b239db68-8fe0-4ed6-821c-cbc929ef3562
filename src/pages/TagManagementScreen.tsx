import React, { useState, useEffect, useMemo } from 'react';
import { Platform } from 'react-native';
import {
  Box,
  Text,
  Heading,
  VStack,
  HStack,
  Button,
  ButtonText,
  ButtonIcon,
  Pressable,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  ScrollView,
  FlatList,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
  Badge,
  BadgeText,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  AlertDialog,
  AlertDialogBackdrop,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
} from '@gluestack-ui/themed';
import { useNavigation } from '@react-navigation/native';
import {
  Search,
  Plus,
  X,
  Check,
  Tag as TagIcon,
  ArrowLeft,
  Trash2,
  Edit,
  Filter,
  Hash,
  Users,
  Palette,
} from 'lucide-react-native';

// Import global state and hooks
import { useContactsStore, useContactsSelectors } from '../store';
import { usePerformanceMonitor, useNetworkError } from '../hooks';
import { ErrorBoundary, StandardButton, StandardIcon, CircularButton } from '../components/ui';

// Types and interfaces
type TagManagementScreenNavigationProp = any; // TODO: 添加正确的导航类型

interface TagItem {
  id: string;
  name: string;
  color: string;
  category: string;
  count: number;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface TagCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
}

interface TagStats {
  totalTags: number;
  totalUsage: number;
  categoryCounts: Record<string, number>;
  mostUsedTag: TagItem | null;
  recentlyCreated: TagItem[];
}

// Tag categories configuration
const TAG_CATEGORIES: TagCategory[] = [
  {
    id: 'industry',
    name: '行业',
    description: '按行业分类的标签',
    icon: Hash,
    color: '#3B82F6',
  },
  {
    id: 'relationship',
    name: '关系',
    description: '关系类型标签',
    icon: Users,
    color: '#10B981',
  },
  {
    id: 'personal',
    name: '个人',
    description: '个人兴趣和特征标签',
    icon: TagIcon,
    color: '#F59E0B',
  },
  {
    id: 'custom',
    name: '自定义',
    description: '用户自定义标签',
    icon: Palette,
    color: '#8B5CF6',
  },
];

// Available colors for tags
const TAG_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Orange
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6366F1', // Indigo
  '#F97316', // Orange-600
  '#14B8A6', // Teal
  '#A855F7', // Violet
  '#22C55E', // Green-500
  '#F43F5E', // Rose
  '#6B7280', // Gray
];

/**
 * 标签管理屏幕 - 完全重构版本
 * 使用Gluestack UI和全局状态管理
 */
const TagManagementScreen: React.FC = () => {
  const navigation = useNavigation<TagManagementScreenNavigationProp>();
  const toast = useToast();

  // Performance monitoring
  usePerformanceMonitor('TagManagementScreen');

  // Global state
  const { contacts, tags, addTag, updateTag, deleteTag } = useContactsStore();
  const { showError, clearError } = useNetworkError();

  // Local state
  const [activeCategory, setActiveCategory] = useState<string>('industry');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showTagModal, setShowTagModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editingTag, setEditingTag] = useState<TagItem | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: TAG_COLORS[0],
    category: 'industry',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Process tags data from contacts
  const processedTags = useMemo((): TagItem[] => {
    const tagMap = new Map<string, TagItem>();

    // Count tag usage from contacts
    contacts.forEach(contact => {
      contact.tags?.forEach(tagName => {
        const tagKey = tagName.toLowerCase();
        if (tagMap.has(tagKey)) {
          const existingTag = tagMap.get(tagKey)!;
          existingTag.count += 1;
        } else {
          // Create tag item from contact tag
          const category = determineTagCategory(tagName);
          const color = getTagColor(tagName, category);

          tagMap.set(tagKey, {
            id: `tag-${tagKey}`,
            name: tagName,
            color,
            category,
            count: 1,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
      });
    });

    // Add existing tags from store
    tags.forEach(tag => {
      const tagKey = tag.name.toLowerCase();
      if (!tagMap.has(tagKey)) {
        tagMap.set(tagKey, tag);
      }
    });

    return Array.from(tagMap.values());
  }, [contacts, tags]);

  // Filter tags based on category and search
  const filteredTags = useMemo(() => {
    let filtered = processedTags;

    // Category filter
    if (activeCategory !== 'all') {
      filtered = filtered.filter(tag => tag.category === activeCategory);
    }

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(tag =>
        tag.name.toLowerCase().includes(query) ||
        tag.description?.toLowerCase().includes(query)
      );
    }

    return filtered.sort((a, b) => b.count - a.count);
  }, [processedTags, activeCategory, searchQuery]);

  // Calculate tag statistics
  const tagStats = useMemo((): TagStats => {
    const categoryCounts: Record<string, number> = {};
    let totalUsage = 0;
    let mostUsedTag: TagItem | null = null;

    processedTags.forEach(tag => {
      categoryCounts[tag.category] = (categoryCounts[tag.category] || 0) + 1;
      totalUsage += tag.count;

      if (!mostUsedTag || tag.count > mostUsedTag.count) {
        mostUsedTag = tag;
      }
    });

    const recentlyCreated = processedTags
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);

    return {
      totalTags: processedTags.length,
      totalUsage,
      categoryCounts,
      mostUsedTag,
      recentlyCreated,
    };
  }, [processedTags]);

  // Utility functions
  const determineTagCategory = (tagName: string): string => {
    const name = tagName.toLowerCase();

    // Industry keywords
    if (['tech', 'technology', 'finance', 'healthcare', 'education', 'manufacturing'].some(keyword =>
      name.includes(keyword))) {
      return 'industry';
    }

    // Relationship keywords
    if (['client', 'partner', 'vendor', 'colleague', 'prospect', 'friend'].some(keyword =>
      name.includes(keyword))) {
      return 'relationship';
    }

    // Personal keywords
    if (['golf', 'coffee', 'travel', 'art', 'wine', 'music', 'sports'].some(keyword =>
      name.includes(keyword))) {
      return 'personal';
    }

    return 'custom';
  };

  const getTagColor = (tagName: string, category: string): string => {
    const categoryColors = {
      industry: '#3B82F6',
      relationship: '#10B981',
      personal: '#F59E0B',
      custom: '#8B5CF6',
    };

    // Use category color as base, but add some variation
    const baseColor = categoryColors[category as keyof typeof categoryColors] || '#6B7280';
    const hash = tagName.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);

    const colorIndex = Math.abs(hash) % TAG_COLORS.length;
    return TAG_COLORS[colorIndex];
  };

  const getCategoryInfo = (categoryId: string): TagCategory => {
    return TAG_CATEGORIES.find(cat => cat.id === categoryId) || TAG_CATEGORIES[0];
  };

  // Event handlers
  const handleTagPress = (tag: TagItem) => {
    setEditingTag(tag);
    setFormData({
      name: tag.name,
      description: tag.description || '',
      color: tag.color,
      category: tag.category,
    });
    setShowTagModal(true);
  };

  const handleViewContacts = (tag: TagItem) => {
    // Navigate to contacts screen with tag filter
    navigation.navigate('Contacts', { tagFilter: tag.name });
  };

  const openNewTagModal = () => {
    setEditingTag(null);
    setFormData({
      name: '',
      description: '',
      color: TAG_COLORS[0],
      category: activeCategory,
    });
    setErrors({});
    setShowTagModal(true);
  };

  const updateFormData = (updates: Partial<typeof formData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    // Clear related errors
    Object.keys(updates).forEach(key => {
      if (errors[key]) {
        setErrors(prev => ({ ...prev, [key]: undefined }));
      }
    });
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = '标签名称不能为空';
    } else if (formData.name.length < 2) {
      newErrors.name = '标签名称至少需要2个字符';
    } else if (formData.name.length > 20) {
      newErrors.name = '标签名称不能超过20个字符';
    }

    // Check for duplicate names (excluding current editing tag)
    const existingTag = processedTags.find(tag =>
      tag.name.toLowerCase() === formData.name.toLowerCase() &&
      tag.id !== editingTag?.id
    );
    if (existingTag) {
      newErrors.name = '该标签名称已存在';
    }

    if (formData.description && formData.description.length > 100) {
      newErrors.description = '描述不能超过100个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveTag = async () => {
    if (!validateForm()) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>表单验证失败</ToastTitle>
            <ToastDescription>请检查并修正表单中的错误</ToastDescription>
          </Toast>
        ),
      });
      return;
    }

    try {
      clearError();

      if (editingTag) {
        // Update existing tag
        const updatedTag: TagItem = {
          ...editingTag,
          name: formData.name.trim(),
          description: formData.description.trim(),
          color: formData.color,
          category: formData.category,
          updatedAt: new Date(),
        };

        updateTag(updatedTag);

        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="success" variant="accent">
              <ToastTitle>标签更新成功</ToastTitle>
              <ToastDescription>标签信息已成功更新</ToastDescription>
            </Toast>
          ),
        });
      } else {
        // Create new tag
        const newTag: TagItem = {
          id: `tag-${Date.now()}`,
          name: formData.name.trim(),
          description: formData.description.trim(),
          color: formData.color,
          category: formData.category,
          count: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        addTag(newTag);

        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="success" variant="accent">
              <ToastTitle>标签创建成功</ToastTitle>
              <ToastDescription>新标签已成功创建</ToastDescription>
            </Toast>
          ),
        });
      }

      setShowTagModal(false);
    } catch (error) {
      console.error('Error saving tag:', error);
      showError({
        message: '保存标签时出错，请重试',
        code: 'SAVE_ERROR'
      });
    }
  };

  const handleDeleteTag = async () => {
    if (!editingTag) return;

    try {
      clearError();

      deleteTag(editingTag.id);

      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>标签删除成功</ToastTitle>
            <ToastDescription>标签已从所有联系人中移除</ToastDescription>
          </Toast>
        ),
      });

      setShowTagModal(false);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting tag:', error);
      showError({
        message: '删除标签时出错，请重试',
        code: 'DELETE_ERROR'
      });
    }
  };

  const confirmDeleteTag = () => {
    setShowDeleteDialog(true);
  };

  // Render tag item
  const renderTag = ({ item }: { item: TagItem }) => {
    const categoryInfo = getCategoryInfo(item.category);

    return (
      <Pressable
        onPress={() => handleTagPress(item)}
        mb="$3"
        sx={{
          _pressed: { opacity: 0.8 }
        }}
      >
        <Box
          p="$4"
          bg="$backgroundLight0"
          borderRadius="$lg"
          borderWidth="$1"
          borderColor={item.color}
          sx={{
            _dark: {
              bg: '$backgroundDark900',
            },
            shadowColor: '$neutral900',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
          }}
        >
          <HStack justifyContent="space-between" alignItems="center">
            <HStack space="md" alignItems="center" flex={1}>
              {/* Tag color indicator */}
              <Box
                w="$4"
                h="$4"
                borderRadius="$full"
                bg={item.color}
              />

              <VStack flex={1}>
                <HStack space="sm" alignItems="center">
                  <Text
                    fontWeight="$medium"
                    color="$textLight900"
                    sx={{ _dark: { color: '$textDark100' } }}
                    flex={1}
                  >
                    {item.name}
                  </Text>
                  <Badge size="sm" variant="outline" borderColor={categoryInfo.color}>
                    <BadgeText color={categoryInfo.color}>
                      {categoryInfo.name}
                    </BadgeText>
                  </Badge>
                </HStack>

                {item.description && (
                  <Text
                    size="sm"
                    color="$textLight600"
                    sx={{ _dark: { color: '$textDark400' } }}
                    numberOfLines={1}
                  >
                    {item.description}
                  </Text>
                )}
              </VStack>
            </HStack>

            {/* Usage count */}
            <VStack space="xs" alignItems="center">
              <Box
                bg={item.color}
                borderRadius="$full"
                px="$2"
                py="$1"
                minWidth="$8"
                alignItems="center"
              >
                <Text
                  size="sm"
                  fontWeight="$bold"
                  color="white"
                >
                  {item.count}
                </Text>
              </Box>
              <Text
                size="xs"
                color="$textLight500"
                sx={{ _dark: { color: '$textDark500' } }}
              >
                使用次数
              </Text>
            </VStack>
          </HStack>

          {/* Action buttons */}
          <HStack space="sm" mt="$3" justifyContent="flex-end">
            <Pressable
              onPress={() => handleViewContacts(item)}
              p="$2"
              borderRadius="$md"
              bg="$backgroundLight100"
              sx={{ _dark: { bg: '$backgroundDark800' } }}
            >
              <HStack space="xs" alignItems="center">
                <StandardIcon as={Users} size="sm" color="$textLight600" />
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  查看联系人
                </Text>
              </HStack>
            </Pressable>

            <Pressable
              onPress={() => handleTagPress(item)}
              p="$2"
              borderRadius="$md"
              bg="$backgroundLight100"
              sx={{ _dark: { bg: '$backgroundDark800' } }}
            >
              <StandardIcon as={Edit} size="sm" color="$textLight600" />
            </Pressable>
          </HStack>
        </Box>
      </Pressable>
    );
  };

  return (
    <ErrorBoundary>
      <Box flex={1} bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark950' } }}>
        {/* Header */}
        <Box
          bg="$backgroundLight0"
          pt={Platform.OS === 'ios' ? '$12' : '$6'}
          pb="$4"
          px="$4"
          borderBottomWidth="$1"
          borderBottomColor="$borderLight200"
          sx={{
            _dark: {
              bg: '$backgroundDark900',
              borderBottomColor: '$borderDark700',
            },
          }}
        >
          <HStack justifyContent="space-between" alignItems="center">
            <Pressable onPress={() => navigation.goBack()} p="$1">
              <StandardIcon as={ArrowLeft} size="lg" color="$primary600" />
            </Pressable>

            <Heading size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              标签管理
            </Heading>

            <Box w="$8" />
          </HStack>
        </Box>

        {/* Statistics Overview */}
        <Box p="$4" bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark900' } }}>
          <VStack space="md">
            <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              标签概览
            </Text>
            <HStack space="lg" justifyContent="space-around">
              <VStack space="xs" alignItems="center">
                <Text fontWeight="$bold" size="xl" color="$primary600">
                  {tagStats.totalTags}
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  总标签数
                </Text>
              </VStack>

              <VStack space="xs" alignItems="center">
                <Text fontWeight="$bold" size="xl" color="$success600">
                  {tagStats.totalUsage}
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  总使用次数
                </Text>
              </VStack>

              <VStack space="xs" alignItems="center">
                <Text fontWeight="$bold" size="xl" color="$warning600">
                  {Object.keys(tagStats.categoryCounts).length}
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  分类数量
                </Text>
              </VStack>

              {tagStats.mostUsedTag && (
                <VStack space="xs" alignItems="center">
                  <Text fontWeight="$bold" size="xl" color="$info600">
                    {tagStats.mostUsedTag.count}
                  </Text>
                  <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                    最高使用
                  </Text>
                </VStack>
              )}
            </HStack>
          </VStack>
        </Box>

        {/* Search Bar */}
        <Box p="$4">
          <Input variant="outline" size="md">
            <InputSlot pl="$3">
              <InputIcon as={Search} />
            </InputSlot>
            <InputField
              placeholder="搜索标签..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </Input>
        </Box>

        {/* Category Tabs */}
        {!searchQuery.trim() && (
          <Box px="$4" pb="$4">
            <VStack space="sm">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                标签分类
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <HStack space="sm" px="$1">
                  {/* All categories option */}
                  <Pressable
                    onPress={() => setActiveCategory('all')}
                    px="$4"
                    py="$3"
                    borderRadius="$lg"
                    bg={activeCategory === 'all' ? '$primary600' : '$backgroundLight100'}
                    borderWidth="$1"
                    borderColor={activeCategory === 'all' ? '$primary600' : '$borderLight200'}
                    sx={{
                      _dark: {
                        bg: activeCategory === 'all' ? '$primary500' : '$backgroundDark800',
                        borderColor: activeCategory === 'all' ? '$primary500' : '$borderDark600',
                      }
                    }}
                  >
                    <HStack space="sm" alignItems="center">
                      <StandardIcon
                        as={Filter}
                        size="sm"
                        color={activeCategory === 'all' ? 'white' : '$textLight600'}
                      />
                      <Text
                        fontWeight="$medium"
                        color={activeCategory === 'all' ? 'white' : '$textLight700'}
                        sx={{
                          _dark: {
                            color: activeCategory === 'all' ? 'white' : '$textDark300',
                          }
                        }}
                      >
                        全部 ({tagStats.totalTags})
                      </Text>
                    </HStack>
                  </Pressable>

                  {/* Category tabs */}
                  {TAG_CATEGORIES.map((category) => (
                    <Pressable
                      key={category.id}
                      onPress={() => setActiveCategory(category.id)}
                      px="$4"
                      py="$3"
                      borderRadius="$lg"
                      bg={activeCategory === category.id ? category.color : '$backgroundLight100'}
                      borderWidth="$1"
                      borderColor={activeCategory === category.id ? category.color : '$borderLight200'}
                      sx={{
                        _dark: {
                          bg: activeCategory === category.id ? category.color : '$backgroundDark800',
                          borderColor: activeCategory === category.id ? category.color : '$borderDark600',
                        }
                      }}
                    >
                      <HStack space="sm" alignItems="center">
                        <StandardIcon
                          as={category.icon}
                          size="sm"
                          color={activeCategory === category.id ? 'white' : category.color}
                        />
                        <Text
                          fontWeight="$medium"
                          color={activeCategory === category.id ? 'white' : '$textLight700'}
                          sx={{
                            _dark: {
                              color: activeCategory === category.id ? 'white' : '$textDark300',
                            }
                          }}
                        >
                          {category.name} ({tagStats.categoryCounts[category.id] || 0})
                        </Text>
                      </HStack>
                    </Pressable>
                  ))}
                </HStack>
              </ScrollView>
            </VStack>
          </Box>
        )}

        {/* Tag List */}
        <Box flex={1} px="$4">
          {searchQuery.trim() && (
            <Box mb="$4">
              <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                找到 {filteredTags.length} 个标签包含 "{searchQuery}"
              </Text>
            </Box>
          )}

          {filteredTags.length > 0 ? (
            <FlatList
              data={filteredTags}
              renderItem={renderTag}
              keyExtractor={item => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: 100 }}
            />
          ) : (
            <Box flex={1} justifyContent="center" alignItems="center">
              <VStack space="lg" alignItems="center">
                <StandardIcon as={TagIcon} size="4xl" color="$textLight400" />
                <VStack space="sm" alignItems="center">
                  <Text
                    textAlign="center"
                    fontWeight="$medium"
                    color="$textLight500"
                    sx={{ _dark: { color: '$textDark500' } }}
                  >
                    {searchQuery.trim()
                      ? '没有找到匹配的标签'
                      : activeCategory === 'all'
                        ? '还没有任何标签'
                        : `${getCategoryInfo(activeCategory).name}分类下还没有标签`
                    }
                  </Text>
                  <Text
                    textAlign="center"
                    color="$textLight400"
                    sx={{ _dark: { color: '$textDark600' } }}
                  >
                    {searchQuery.trim()
                      ? '尝试使用不同的关键词搜索'
                      : '创建第一个标签来开始组织联系人'
                    }
                  </Text>
                </VStack>
                <StandardButton
                  variant="outline"
                  onPress={openNewTagModal}
                  leftIcon={Plus}
                >
                  创建标签
                </StandardButton>
              </VStack>
            </Box>
          )}
        </Box>

        {/* Floating Action Button */}
        <Box position="absolute" bottom="$6" right="$4">
          <CircularButton
            icon={Plus}
            size="xl"
            variant="solid"
            action="primary"
            onPress={openNewTagModal}
          />
        </Box>

        {/* Tag Creation/Editing Modal */}
        <Modal isOpen={showTagModal} onClose={() => setShowTagModal(false)}>
          <ModalBackdrop />
          <ModalContent maxWidth="$96">
            <ModalHeader>
              <Heading size="lg">
                {editingTag ? '编辑标签' : '创建新标签'}
              </Heading>
              <ModalCloseButton>
                <StandardIcon as={X} />
              </ModalCloseButton>
            </ModalHeader>

            <ModalBody>
              <VStack space="lg">
                {/* Tag Name */}
                <VStack space="sm">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    标签名称 *
                  </Text>
                  <Input
                    variant={errors.name ? 'outline' : 'outline'}
                    size="md"
                    isInvalid={!!errors.name}
                  >
                    <InputField
                      placeholder="输入标签名称"
                      value={formData.name}
                      onChangeText={(text) => updateFormData({ name: text })}
                      autoFocus
                    />
                  </Input>
                  {errors.name && (
                    <Text size="sm" color="$error600">{errors.name}</Text>
                  )}
                </VStack>

                {/* Tag Description */}
                <VStack space="sm">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    标签描述
                  </Text>
                  <Input
                    variant={errors.description ? 'outline' : 'outline'}
                    size="md"
                    isInvalid={!!errors.description}
                  >
                    <InputField
                      placeholder="输入标签描述（可选）"
                      value={formData.description}
                      onChangeText={(text) => updateFormData({ description: text })}
                    />
                  </Input>
                  {errors.description && (
                    <Text size="sm" color="$error600">{errors.description}</Text>
                  )}
                </VStack>

                {/* Tag Color */}
                <VStack space="sm">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    标签颜色
                  </Text>
                  <Box>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                      <HStack space="sm" px="$1">
                        {TAG_COLORS.map(color => (
                          <Pressable
                            key={color}
                            onPress={() => updateFormData({ color })}
                            w="$10"
                            h="$10"
                            borderRadius="$full"
                            bg={color}
                            borderWidth="$2"
                            borderColor={formData.color === color ? '$neutral900' : 'transparent'}
                            justifyContent="center"
                            alignItems="center"
                            sx={{
                              _dark: {
                                borderColor: formData.color === color ? '$neutral100' : 'transparent'
                              }
                            }}
                          >
                            {formData.color === color && (
                              <StandardIcon as={Check} size="md" color="white" />
                            )}
                          </Pressable>
                        ))}
                      </HStack>
                    </ScrollView>
                  </Box>
                </VStack>

                {/* Tag Category */}
                <VStack space="sm">
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    标签分类
                  </Text>
                  <VStack space="sm">
                    {TAG_CATEGORIES.map(category => (
                      <Pressable
                        key={category.id}
                        onPress={() => updateFormData({ category: category.id })}
                        p="$3"
                        borderRadius="$md"
                        borderWidth="$1"
                        borderColor={formData.category === category.id ? category.color : '$borderLight200'}
                        bg={formData.category === category.id ? `${category.color}20` : 'transparent'}
                        sx={{
                          _dark: {
                            borderColor: formData.category === category.id ? category.color : '$borderDark700',
                            bg: formData.category === category.id ? `${category.color}30` : 'transparent',
                          }
                        }}
                      >
                        <HStack space="md" alignItems="center">
                          <StandardIcon
                            as={category.icon}
                            size="md"
                            color={formData.category === category.id ? category.color : '$textLight600'}
                          />
                          <VStack flex={1}>
                            <Text
                              fontWeight="$medium"
                              color={formData.category === category.id ? category.color : '$textLight900'}
                              sx={{ _dark: { color: formData.category === category.id ? category.color : '$textDark100' } }}
                            >
                              {category.name}
                            </Text>
                            <Text
                              size="sm"
                              color="$textLight600"
                              sx={{ _dark: { color: '$textDark400' } }}
                            >
                              {category.description}
                            </Text>
                          </VStack>
                          {formData.category === category.id && (
                            <StandardIcon as={Check} size="md" color={category.color} />
                          )}
                        </HStack>
                      </Pressable>
                    ))}
                  </VStack>
                </VStack>
              </VStack>
            </ModalBody>

            <ModalFooter>
              <HStack space="md" flex={1}>
                {editingTag && (
                  <StandardButton
                    variant="outline"
                    action="negative"
                    onPress={confirmDeleteTag}
                    leftIcon={Trash2}
                  >
                    删除
                  </StandardButton>
                )}

                <StandardButton
                  variant="outline"
                  onPress={() => setShowTagModal(false)}
                  flex={editingTag ? 0 : 1}
                >
                  取消
                </StandardButton>

                <StandardButton
                  variant="solid"
                  action="primary"
                  onPress={handleSaveTag}
                  flex={1}
                  sx={{ bg: formData.color }}
                >
                  {editingTag ? '更新标签' : '创建标签'}
                </StandardButton>
              </HStack>
            </ModalFooter>
          </ModalContent>
        </Modal>
        {/* Delete Confirmation Dialog */}
        <AlertDialog isOpen={showDeleteDialog} onClose={() => setShowDeleteDialog(false)}>
          <AlertDialogBackdrop />
          <AlertDialogContent>
            <AlertDialogHeader>
              <Heading size="lg">确认删除</Heading>
              <AlertDialogCloseButton>
                <StandardIcon as={X} />
              </AlertDialogCloseButton>
            </AlertDialogHeader>

            <AlertDialogBody>
              <VStack space="md">
                <Text color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  确定要删除标签 "{editingTag?.name}" 吗？
                </Text>
                <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  此操作将从所有 {editingTag?.count || 0} 个联系人中移除该标签，且无法撤销。
                </Text>
              </VStack>
            </AlertDialogBody>

            <AlertDialogFooter>
              <HStack space="md">
                <StandardButton
                  variant="outline"
                  onPress={() => setShowDeleteDialog(false)}
                  flex={1}
                >
                  取消
                </StandardButton>
                <StandardButton
                  variant="solid"
                  action="negative"
                  onPress={handleDeleteTag}
                  flex={1}
                >
                  删除
                </StandardButton>
              </HStack>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </Box>
    </ErrorBoundary>
  );
};

export default TagManagementScreen;

// TagManagementScreen 组件完全重构完成
// 使用 Gluestack UI 和全局状态管理，移除了所有 StyleSheet 样式