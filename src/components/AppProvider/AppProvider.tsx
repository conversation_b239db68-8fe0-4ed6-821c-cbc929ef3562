import React, { ReactNode, useEffect } from 'react';
import { ErrorBoundary } from '../ErrorBoundary';
import { useNetworkStatus, useMemoryMonitor } from '../../hooks';

interface AppProviderProps {
  children: ReactNode;
}

/**
 * 应用提供者组件
 * 集成错误边界、网络监控、性能监控等全局功能
 */
const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  // 初始化网络状态监听
  useNetworkStatus();
  
  // 初始化内存监控（仅开发环境）
  useMemoryMonitor();

  // 全局错误处理
  const handleGlobalError = (error: Error, errorInfo: any) => {
    // 在生产环境中，这里可以发送错误到错误报告服务
    console.error('Global error caught:', error, errorInfo);
    
    // TODO: 集成错误报告服务 (如 Sentry, Bugsnag 等)
    // errorReportingService.captureException(error, {
    //   extra: errorInfo,
    //   tags: {
    //     component: 'AppProvider',
    //     environment: __DEV__ ? 'development' : 'production'
    //   }
    // });
  };

  // 应用启动时的初始化逻辑
  useEffect(() => {
    // 初始化应用
    console.log('🚀 App initialized');
    
    // 在开发环境中显示性能提示
    if (__DEV__) {
      console.log('🔧 Development mode - Performance monitoring enabled');
    }

    return () => {
      console.log('🔄 App cleanup');
    };
  }, []);

  return (
    <ErrorBoundary onError={handleGlobalError}>
      {children}
    </ErrorBoundary>
  );
};

export default AppProvider;
