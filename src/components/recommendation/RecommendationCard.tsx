/**
 * 推荐卡片组件
 * 显示单个推荐项目的详细信息和操作按钮
 */

import React from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Badge,
  BadgeText,
  Pressable,
  Button,
  ButtonText,
  ButtonIcon,
  Divider,
} from '@gluestack-ui/themed';
import {
  Users,
  Calendar,
  Heart,
  TrendingUp,
  Network,
  MessageCircle,
  CheckCircle,
  X,
  ThumbsUp,
  ThumbsDown,
  ExternalLink,
  Clock,
} from 'lucide-react-native';

import { StandardIcon, StandardButton } from '../ui';
import { RecommendationItem, RecommendationType } from '../../services/aiRecommendationService';

interface RecommendationCardProps {
  recommendation: RecommendationItem;
  onAction?: (recommendationId: string, action: string) => void;
  onDismiss?: (recommendationId: string) => void;
  onFeedback?: (recommendationId: string, feedback: 'positive' | 'negative' | 'neutral') => void;
  compact?: boolean;
}

export const RecommendationCard: React.FC<RecommendationCardProps> = ({
  recommendation,
  onAction,
  onDismiss,
  onFeedback,
  compact = false
}) => {
  const getTypeIcon = (type: RecommendationType) => {
    switch (type) {
      case 'contact_connection':
        return Users;
      case 'event_suggestion':
        return Calendar;
      case 'relationship_maintenance':
        return Heart;
      case 'business_opportunity':
        return TrendingUp;
      case 'network_expansion':
        return Network;
      case 'follow_up_reminder':
        return MessageCircle;
      default:
        return Users;
    }
  };

  const getTypeLabel = (type: RecommendationType) => {
    switch (type) {
      case 'contact_connection':
        return '联系人推荐';
      case 'event_suggestion':
        return '活动推荐';
      case 'relationship_maintenance':
        return '关系维护';
      case 'business_opportunity':
        return '商务机会';
      case 'network_expansion':
        return '网络扩展';
      case 'follow_up_reminder':
        return '跟进提醒';
      default:
        return '推荐';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'muted';
      default:
        return 'muted';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return '紧急';
      case 'high':
        return '高';
      case 'medium':
        return '中';
      case 'low':
        return '低';
      default:
        return '中';
    }
  };

  const formatConfidence = (confidence: number) => {
    return `${Math.round(confidence * 100)}%`;
  };

  const isExpiringSoon = () => {
    if (!recommendation.expiresAt) return false;
    const now = new Date();
    const expiresAt = new Date(recommendation.expiresAt);
    const hoursUntilExpiry = (expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60);
    return hoursUntilExpiry <= 24 && hoursUntilExpiry > 0;
  };

  const handleAction = (action: string) => {
    if (onAction) {
      onAction(recommendation.id, action);
    }
  };

  const handleDismiss = () => {
    if (onDismiss) {
      onDismiss(recommendation.id);
    }
  };

  const handleFeedback = (feedback: 'positive' | 'negative' | 'neutral') => {
    if (onFeedback) {
      onFeedback(recommendation.id, feedback);
    }
  };

  const TypeIcon = getTypeIcon(recommendation.type);

  return (
    <Box
      bg="$backgroundLight0"
      borderRadius="$lg"
      borderWidth="$1"
      borderColor="$borderLight200"
      p="$4"
      mb="$3"
      sx={{
        _dark: {
          bg: '$backgroundDark900',
          borderColor: '$borderDark700',
        }
      }}
    >
      <VStack space="md">
        {/* Header */}
        <HStack justifyContent="space-between" alignItems="flex-start">
          <HStack space="sm" alignItems="center" flex={1}>
            <Box
              p="$2"
              borderRadius="$md"
              bg="$primary100"
              sx={{ _dark: { bg: '$primary900' } }}
            >
              <StandardIcon as={TypeIcon} size="sm" color="$primary600" />
            </Box>
            
            <VStack flex={1} space="xs">
              <HStack justifyContent="space-between" alignItems="center">
                <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  {getTypeLabel(recommendation.type)}
                </Text>
                <HStack space="xs" alignItems="center">
                  <Badge size="sm" variant="solid" action={getPriorityColor(recommendation.priority)}>
                    <BadgeText>{getPriorityLabel(recommendation.priority)}</BadgeText>
                  </Badge>
                  {isExpiringSoon() && (
                    <Badge size="sm" variant="outline" action="warning">
                      <BadgeText>即将过期</BadgeText>
                    </Badge>
                  )}
                </HStack>
              </HStack>
              
              <Text fontWeight="$semibold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                {recommendation.title}
              </Text>
            </VStack>
          </HStack>

          {/* Dismiss Button */}
          <Pressable onPress={handleDismiss} p="$1">
            <StandardIcon as={X} size="sm" color="$textLight400" />
          </Pressable>
        </HStack>

        {/* Description */}
        <Text size="sm" color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
          {recommendation.description}
        </Text>

        {/* Metadata */}
        {!compact && recommendation.metadata && (
          <VStack space="sm">
            {/* Suggested Actions */}
            {recommendation.metadata.suggestedActions && (
              <VStack space="xs">
                <Text size="xs" fontWeight="$medium" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  建议操作：
                </Text>
                {recommendation.metadata.suggestedActions.map((action: string, index: number) => (
                  <HStack key={index} space="xs" alignItems="center">
                    <Box w="$1" h="$1" borderRadius="$full" bg="$primary500" />
                    <Text size="xs" color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                      {action}
                    </Text>
                  </HStack>
                ))}
              </VStack>
            )}

            {/* Additional Info */}
            {recommendation.metadata.daysSinceLastContact && (
              <HStack space="xs" alignItems="center">
                <StandardIcon as={Clock} size="xs" color="$textLight500" />
                <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  {recommendation.metadata.daysSinceLastContact} 天未联系
                </Text>
              </HStack>
            )}
          </VStack>
        )}

        {/* Confidence Score */}
        <HStack justifyContent="space-between" alignItems="center">
          <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
            置信度: {formatConfidence(recommendation.confidence)}
          </Text>
          
          {recommendation.expiresAt && (
            <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
              {new Date(recommendation.expiresAt).toLocaleDateString('zh-CN')} 过期
            </Text>
          )}
        </HStack>

        {/* Actions */}
        {recommendation.actionable && (
          <>
            <Divider />
            <VStack space="sm">
              {/* Primary Actions */}
              <HStack space="sm">
                <StandardButton
                  size="sm"
                  variant="solid"
                  action="primary"
                  onPress={() => handleAction('primary')}
                  flex={1}
                >
                  立即执行
                </StandardButton>
                
                <StandardButton
                  size="sm"
                  variant="outline"
                  onPress={() => handleAction('later')}
                  flex={1}
                >
                  稍后处理
                </StandardButton>
              </HStack>

              {/* Feedback */}
              {!compact && (
                <HStack justifyContent="center" space="md">
                  <Pressable onPress={() => handleFeedback('positive')} p="$2">
                    <HStack space="xs" alignItems="center">
                      <StandardIcon as={ThumbsUp} size="xs" color="$success600" />
                      <Text size="xs" color="$success600">有用</Text>
                    </HStack>
                  </Pressable>
                  
                  <Pressable onPress={() => handleFeedback('negative')} p="$2">
                    <HStack space="xs" alignItems="center">
                      <StandardIcon as={ThumbsDown} size="xs" color="$error600" />
                      <Text size="xs" color="$error600">无用</Text>
                    </HStack>
                  </Pressable>
                </HStack>
              )}
            </VStack>
          </>
        )}
      </VStack>
    </Box>
  );
};

export default RecommendationCard;
