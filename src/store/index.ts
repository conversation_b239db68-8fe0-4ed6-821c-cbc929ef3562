// Export all stores and their selectors
export * from './types';
export * from './settingsStore';
export * from './contactsStore';
export * from './contactGroupsStore';
export * from './appStore';
export * from './uiStore';
export * from './meetingsStore';
export * from './reminderStore';
export * from './communicationStore';

// Re-export commonly used hooks for convenience
export { useSettingsStore, useSettingsSelectors, useSettingsActions } from './settingsStore';
export { useContactsStore } from './contactsStore';
export { useContactGroupsStore, useContactGroupsSelectors } from './contactGroupsStore';
export { useAppStore, useAppSelectors, useAppActions } from './appStore';
export { useUIStore, useUISelectors, useUIActions } from './uiStore';
export { useReminderStore, useReminderSelectors } from './reminderStore';
export { useCommunicationStore, useCommunicationSelectors } from './communicationStore';
