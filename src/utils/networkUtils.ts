export interface NetworkError {
  message: string;
  status?: number;
  code?: string;
  retry?: () => void;
}

// 处理API错误
export const handleApiError = (error: any): NetworkError => {
  if (error.response) {
    // 服务器响应错误
    const status = error.response.status;
    const message = error.response.data?.message || getStatusMessage(status);
    
    return {
      message,
      status,
      code: error.response.data?.code,
    };
  } else if (error.request) {
    // 网络连接错误
    return {
      message: 'Unable to connect to the server. Please check your internet connection.',
      code: 'NETWORK_ERROR',
    };
  } else {
    // 其他错误
    return {
      message: error.message || 'An unexpected error occurred.',
      code: 'UNKNOWN_ERROR',
    };
  }
};

// 重试机制
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError;
};

// 根据HTTP状态码获取错误消息
const getStatusMessage = (status: number): string => {
  switch (status) {
    case 400:
      return 'Invalid request. Please check your input.';
    case 401:
      return 'Authentication required. Please log in again.';
    case 403:
      return 'Access denied. You don\'t have permission to perform this action.';
    case 404:
      return 'The requested resource was not found.';
    case 408:
      return 'Request timeout. Please try again.';
    case 429:
      return 'Too many requests. Please wait a moment and try again.';
    case 500:
      return 'Server error. Please try again later.';
    case 502:
      return 'Bad gateway. The server is temporarily unavailable.';
    case 503:
      return 'Service unavailable. Please try again later.';
    case 504:
      return 'Gateway timeout. The server took too long to respond.';
    default:
      return `Server error (${status}). Please try again later.`;
  }
};

// 获取用户友好的错误消息
export const getErrorMessage = (error: NetworkError): string => {
  if (error.code === 'NETWORK_ERROR') {
    return 'Please check your internet connection and try again.';
  }
  
  if (error.status && error.status >= 500) {
    return 'Server is temporarily unavailable. Please try again later.';
  }
  
  return error.message;
};
