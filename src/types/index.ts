// src/types/index.ts

// 联系人分组
export interface ContactGroup {
  id: string;
  name: string;
  description?: string;
  color: string; // 分组颜色
  icon: string; // 分组图标名称
  isDefault: boolean; // 是否为默认分组
  contactCount: number; // 分组内联系人数量
  createdAt: Date;
  updatedAt: Date;
}

// 社交媒体链接
export interface SocialLink {
  id: string;
  platform: 'linkedin' | 'twitter' | 'facebook' | 'instagram' | 'github' | 'website' | 'other';
  url: string;
  username?: string;
  displayName?: string;
}

// 联系方式
export interface ContactMethod {
  id: string;
  type: 'phone' | 'email' | 'address';
  label: 'work' | 'home' | 'mobile' | 'other';
  value: string;
  isPrimary: boolean;
}

// 地址信息
export interface Address {
  id: string;
  label: 'work' | 'home' | 'other';
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  isPrimary: boolean;
}

// 重要日期
export interface ImportantDate {
  id: string;
  type: 'birthday' | 'anniversary' | 'work_anniversary' | 'other';
  date: string; // ISO date string
  label?: string;
  reminder?: boolean; // 是否设置提醒
}

// 自定义字段
export interface CustomField {
  id: string;
  name: string;
  value: string;
  type: 'text' | 'number' | 'date' | 'url' | 'email';
}

export interface Tag {
  id: string;
  name: string;
  color?: string; // Color for UI display, can be optional
}

export type TagCategory = 'industry' | 'relationship' | 'personal';

// 关系类型
export type RelationshipType =
  | 'colleague'
  | 'friend'
  | 'family'
  | 'client'
  | 'vendor'
  | 'mentor'
  | 'mentee'
  | 'acquaintance'
  | 'other';

// 联系人重要程度
export type ContactPriority = 'low' | 'medium' | 'high' | 'critical';

// 商务活动/事件相关类型 (evolved from Meeting)
export interface BusinessEvent {
  id: string;
  title: string;
  description?: string;

  // 时间信息
  date: Date;
  startTime: Date;
  endTime?: Date;

  // 地点信息
  location?: string;
  isVirtual: boolean;
  eventLink?: string; // 线上活动链接

  // 活动分类
  eventType: BusinessEventType;
  category: string; // 行业分类
  tags: string[]; // 活动标签
  scale: BusinessEventScale; // 活动规模

  // 参与者信息（增强版）
  participants: EventParticipant[];
  organizerId: string;

  // 内容信息
  notes?: string;
  agenda?: string; // 议程
  objectives?: string[]; // 活动目标

  // 任务和跟进
  tasks: EventTask[];
  followUps: EventFollowUp[];

  // 状态和元数据
  status: BusinessEventStatus;
  isArchived: boolean;
  createdAt: Date;
  updatedAt: Date;

  // 关系建立相关
  relationshipsEstablished: number; // 新建立的关系数量
  relationshipsStrengthened: number; // 强化的关系数量
  effectivenessScore?: number; // 活动效果评分 (1-10)
}

// 商务活动类型枚举
export type BusinessEventType =
  | 'business_meeting'     // 商务会议
  | 'industry_conference'  // 行业会议
  | 'exhibition'          // 展会
  | 'seminar'            // 研讨会
  | 'networking'         // 社交活动
  | 'client_visit'       // 客户拜访
  | 'training'           // 培训
  | 'lecture'            // 讲座
  | 'product_launch'     // 产品发布会
  | 'business_dinner'    // 商务晚宴
  | 'workshop'           // 工作坊
  | 'other';             // 其他

// 活动规模
export type BusinessEventScale = 'small' | 'medium' | 'large' | 'massive';

// 活动状态
export type BusinessEventStatus = 'planned' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled' | 'postponed';

// 活动参与者（增强版）
export interface EventParticipant {
  contactId: string;
  name: string; // 冗余存储，便于显示
  status: 'invited' | 'confirmed' | 'attended' | 'declined' | 'no-show';
  responseDate?: Date;

  // 关系建立上下文
  relationshipContext: EventRelationshipContext;
  introducedBy?: string; // 介绍人联系人ID
  notes?: string; // 关于此参与者的备注

  // 互动信息
  interactionLevel: 'none' | 'brief' | 'moderate' | 'extensive';
  businessCardExchanged: boolean;
  followUpRequired: boolean;
}

// 关系建立上下文
export type EventRelationshipContext =
  | 'first_met'      // 初次见面
  | 'reconnected'    // 重新联系
  | 'introduced_by'  // 被介绍认识
  | 'existing'       // 已有关系
  | 'strengthened';  // 关系强化

// 活动任务（从MeetingTask演进）
export interface EventTask {
  id: string;
  title: string;
  description?: string;
  assigneeId?: string;
  assigneeName?: string; // 冗余存储
  dueDate?: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  isCompleted: boolean;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 活动跟进
export interface EventFollowUp {
  id: string;
  contactId: string;
  contactName: string; // 冗余存储
  type: 'thank_you' | 'business_proposal' | 'meeting_request' | 'information_sharing' | 'other';
  content?: string;
  scheduledDate?: Date;
  isCompleted: boolean;
  completedAt?: Date;
  createdAt: Date;
}

// 向后兼容的类型别名
export type Meeting = BusinessEvent;
export type MeetingTask = EventTask;
export type MeetingStatus = BusinessEventStatus;
export type MeetingParticipant = EventParticipant;

export interface Contact {
  id: string;

  // 基本信息
  name: string; // Combined first and last name, or primary display name
  firstName?: string;
  lastName?: string;
  middleName?: string;
  nickname?: string;
  title?: string; // Mr., Ms., Dr., etc.

  // 头像和照片
  avatar: string; // URL or local path for display
  photo?: string; // Potentially raw URI from image picker

  // 工作信息
  company?: string;
  position?: string; // Job title
  department?: string;
  role?: string; // Role in relation to user

  // 联系方式 - 使用新的结构化方式
  phones: ContactMethod[]; // 多个电话号码
  emails: ContactMethod[]; // 多个邮箱地址
  addresses: Address[]; // 多个地址

  // 兼容性字段（逐步迁移）
  email?: string; // 主要邮箱，向后兼容
  phone?: string; // 主要电话，向后兼容
  address?: string; // 主要地址，向后兼容
  location?: string; // 位置信息，向后兼容

  // 社交媒体和网站
  socialLinks: SocialLink[];
  website?: string;

  // 重要日期
  importantDates: ImportantDate[];
  birthday?: string; // ISO date string, 向后兼容

  // 关系和分类
  relationshipType: RelationshipType;
  priority: ContactPriority;
  groupIds: string[]; // 所属分组ID列表
  tags?: Tag[]; // 标签

  // 交互信息
  lastContactDate?: string; // 最后联系时间
  nextFollowUpDate?: string; // 下次跟进时间
  contactFrequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'rarely';

  // 自定义字段
  customFields: CustomField[];

  // 备注和状态
  notes?: string;
  isFavorite?: boolean;
  isArchived?: boolean; // 是否归档
  isBlocked?: boolean; // 是否屏蔽

  // 元数据
  source?: string; // 联系人来源（手动添加、导入、名片扫描等）
  createdAt: Date;
  updatedAt: Date;
  lastViewedAt?: Date; // 最后查看时间

  // 统计信息
  interactionCount?: number; // 交互次数
  connectionStrength?: number; // 连接强度 (0-100)

  // 关系图谱信息
  introducedBy?: string; // 介绍人联系人ID
  introducedContacts?: string[]; // 我介绍的联系人ID列表
  mutualConnections?: string[]; // 共同联系人ID列表
  relationshipPath?: ContactRelationshipPath[]; // 关系路径
}

// 关系图谱相关类型
export interface ContactRelationshipPath {
  contactId: string;
  path: string[]; // 关系路径，包含中间联系人ID
  strength: number; // 路径强度
  hops: number; // 跳数
}

export interface ContactRelationship {
  id: string;
  fromContactId: string;
  toContactId: string;
  relationshipType: ContactRelationshipType;
  strength: number; // 关系强度 (0-100)
  establishedDate?: Date;
  notes?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type ContactRelationshipType =
  | 'introduced_by' // A介绍了B
  | 'introduced_to' // A被B介绍
  | 'mutual_friend' // 共同朋友
  | 'colleague' // 同事关系
  | 'family_friend' // 家庭朋友
  | 'business_partner' // 商业伙伴
  | 'mentor_mentee' // 师生关系
  | 'client_vendor' // 客户供应商
  | 'other'; // 其他关系

export interface NetworkGraph {
  nodes: NetworkGraphNode[];
  edges: NetworkGraphEdge[];
  centerNodeId: string;
  maxDepth: number;
}

export interface NetworkGraphNode {
  id: string;
  name: string;
  type: 'self' | 'contact' | 'external';
  x?: number;
  y?: number;
  level: number; // 距离中心节点的层级
  avatar?: string;
  company?: string;
  position?: string;
  relationshipType?: RelationshipType;
  connectionStrength?: number;
  isHighlighted?: boolean;
}

export interface NetworkGraphEdge {
  id: string;
  fromId: string;
  toId: string;
  relationshipType: ContactRelationshipType;
  strength: number;
  isDirected: boolean; // 是否有方向性
  label?: string;
  isHighlighted?: boolean;
}

// Example of centralizing other types if needed in the future

// export interface Reminder {
//   id: string;
//   title: string;
//   dueDate: string; // ISO date string
//   priority: 'high' | 'medium' | 'low';
//   isCompleted: boolean;
// }


export type BottomTabParamList = {
  HomeTab: undefined;
  ContactsTab: undefined;
  CreateTab: undefined; // This is for the button, not a screen
  GraphTab: undefined;
  TasksTab: undefined;
};

export type RootStackParamList = {
  MainTabs: { screen: keyof BottomTabParamList };
  Home: undefined;
  Settings: undefined;
  ContactDetail: { contactId?: string; contact?: Contact; isEditedJustNow?: boolean };
  ContactCreate: { contact?: Contact };
  Contacts: { newContact?: Contact }; // Added for passing new contact data
  BusinessEventRecord: { contactId?: string; eventId?: string };
  NetworkVisual: { focusedContactId?: string };
  ReminderCenter: undefined;
  ReminderDetail: { reminderId: string };
  ReminderCreate: { contactId?: string; type?: string };
};

// Export reminder types
export * from './reminder';

// Export communication types
export * from './communication';

