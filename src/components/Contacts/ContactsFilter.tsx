import React, { useState } from 'react';
import {
  VStack,
  HStack,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  Button,
  ButtonText,
  Badge,
  BadgeText,
  ScrollView,
  Pressable,
  Icon,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  Heading,
} from '@gluestack-ui/themed';
import { Search, Filter, X, Star, Users, Settings } from 'lucide-react-native';
// 临时注释掉store导入，避免无限循环
// import { useContactsStore } from '../../store';
// import { ContactGroupSelector } from './ContactGroupSelector';
// import { ContactGroupManager } from './ContactGroupManager';

interface ContactsFilterProps {
  onFilterChange?: () => void;
}

/**
 * 联系人搜索和过滤组件
 * 提供搜索、标签过滤、收藏过滤等功能
 */
const ContactsFilter: React.FC<ContactsFilterProps> = ({ onFilterChange }) => {
  const [showGroupSelector, setShowGroupSelector] = useState(false);
  const [showGroupManager, setShowGroupManager] = useState(false);

  // 临时注释掉store使用，避免无限循环
  // const {
  //   searchQuery,
  //   setSearchQuery,
  //   selectedTags,
  //   setSelectedTags,
  //   availableTags,
  //   contacts,
  //   selectedGroupId,
  //   setSelectedGroup
  // } = useContactsStore();

  // 使用本地状态替代store
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null);

  // 静态数据
  const availableTags: string[] = [];
  const contacts: any[] = [];
  const setSelectedGroup = (groupId: string | null) => setSelectedGroupId(groupId);

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    onFilterChange?.();
  };

  const handleTagToggle = (tag: string) => {
    const newSelectedTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    
    setSelectedTags(newSelectedTags);
    onFilterChange?.();
  };

  const clearAllFilters = () => {
    setSearchQuery('');
    setSelectedTags([]);
    setSelectedGroup(null);
    onFilterChange?.();
  };

  const handleGroupSelect = (groupId: string | null) => {
    setSelectedGroup(groupId);
    setShowGroupSelector(false);
    onFilterChange?.();
  };

  const hasActiveFilters = searchQuery.length > 0 || selectedTags.length > 0 || selectedGroupId;
  const favoriteCount = contacts.filter(c => c.isFavorite).length;

  return (
    <VStack space="md" p="$4" bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark900' } }}>
      {/* Search Input */}
      <Input>
        <InputSlot pl="$3">
          <InputIcon as={Search} />
        </InputSlot>
        <InputField 
          placeholder="Search contacts..." 
          value={searchQuery}
          onChangeText={handleSearchChange}
        />
        {searchQuery.length > 0 && (
          <InputSlot pr="$3">
            <Pressable onPress={() => handleSearchChange('')}>
              <InputIcon as={X} />
            </Pressable>
          </InputSlot>
        )}
      </Input>

      {/* Quick Filters */}
      <HStack space="sm" alignItems="center" flexWrap="wrap">
        {/* 分组选择按钮 */}
        <Pressable
          onPress={() => setShowGroupSelector(true)}
          bg={selectedGroupId ? '$primary100' : '$backgroundLight100'}
          px="$3"
          py="$2"
          borderRadius="$full"
          sx={{
            _dark: {
              bg: selectedGroupId ? '$primary900' : '$backgroundDark800'
            }
          }}
        >
          <HStack space="xs" alignItems="center">
            <Icon
              as={Filter}
              size="sm"
              color={selectedGroupId ? '$primary600' : '$textLight600'}
            />
            <ButtonText
              size="sm"
              color={selectedGroupId ? '$primary600' : '$textLight600'}
            >
              {selectedGroupId ? '已选分组' : '选择分组'}
            </ButtonText>
          </HStack>
        </Pressable>

        <Pressable
          onPress={() => handleTagToggle('favorites')}
          bg={selectedTags.includes('favorites') ? '$primary100' : '$backgroundLight100'}
          px="$3"
          py="$2"
          borderRadius="$full"
          sx={{
            _dark: {
              bg: selectedTags.includes('favorites') ? '$primary900' : '$backgroundDark800'
            }
          }}
        >
          <HStack space="xs" alignItems="center">
            <Icon
              as={Star}
              size="sm"
              color={selectedTags.includes('favorites') ? '$primary600' : '$textLight600'}
            />
            <ButtonText
              size="sm"
              color={selectedTags.includes('favorites') ? '$primary600' : '$textLight600'}
            >
              收藏 ({favoriteCount})
            </ButtonText>
          </HStack>
        </Pressable>

        <Pressable
          onPress={() => handleTagToggle('recent')}
          bg={selectedTags.includes('recent') ? '$primary100' : '$backgroundLight100'}
          px="$3"
          py="$2"
          borderRadius="$full"
          sx={{
            _dark: {
              bg: selectedTags.includes('recent') ? '$primary900' : '$backgroundDark800'
            }
          }}
        >
          <HStack space="xs" alignItems="center">
            <Icon
              as={Users}
              size="sm"
              color={selectedTags.includes('recent') ? '$primary600' : '$textLight600'}
            />
            <ButtonText
              size="sm"
              color={selectedTags.includes('recent') ? '$primary600' : '$textLight600'}
            >
              最近
            </ButtonText>
          </HStack>
        </Pressable>
      </HStack>

      {/* Available Tags */}
      {availableTags.length > 0 && (
        <VStack space="sm">
          <HStack justifyContent="space-between" alignItems="center">
            <ButtonText size="sm" color="$textLight600">
              Filter by tags:
            </ButtonText>
            {hasActiveFilters && (
              <Button size="xs" variant="link" onPress={clearAllFilters}>
                <ButtonText>Clear all</ButtonText>
              </Button>
            )}
          </HStack>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <HStack space="xs">
              {availableTags.map((tag) => (
                <Pressable
                  key={tag}
                  onPress={() => handleTagToggle(tag)}
                >
                  <Badge
                    size="md"
                    variant={selectedTags.includes(tag) ? "solid" : "outline"}
                    action={selectedTags.includes(tag) ? "primary" : "muted"}
                  >
                    <BadgeText>{tag}</BadgeText>
                  </Badge>
                </Pressable>
              ))}
            </HStack>
          </ScrollView>
        </VStack>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <HStack space="xs" alignItems="center" flexWrap="wrap">
          <ButtonText size="xs" color="$textLight500">
            Active filters:
          </ButtonText>
          {searchQuery && (
            <Badge size="sm" variant="outline" action="info">
              <BadgeText>Search: "{searchQuery}"</BadgeText>
            </Badge>
          )}
          {selectedTags.map((tag) => (
            <Badge key={tag} size="sm" variant="solid" action="primary">
              <BadgeText>{tag}</BadgeText>
              <Pressable onPress={() => handleTagToggle(tag)} ml="$1">
                <Icon as={X} size="xs" color="white" />
              </Pressable>
            </Badge>
          ))}
        </HStack>
      )}

      {/* 分组选择模态框 */}
      <Modal isOpen={showGroupSelector} onClose={() => setShowGroupSelector(false)} size="lg">
        <ModalBackdrop />
        <ModalContent>
          <ModalHeader>
            <Heading size="md">选择分组</Heading>
            <ModalCloseButton>
              <Icon as={X} />
            </ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            {/* 临时注释掉ContactGroupSelector，避免无限循环 */}
            {/* <ContactGroupSelector
              onGroupSelect={handleGroupSelect}
              onManageGroups={() => {
                setShowGroupSelector(false);
                setShowGroupManager(true);
              }}
              selectedGroupId={selectedGroupId}
              showContactCounts={true}
            /> */}
            <VStack space="md" p="$4">
              <ButtonText>分组选择功能暂时不可用</ButtonText>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* 分组管理模态框 - 临时注释掉避免无限循环 */}
      {/* <ContactGroupManager
        visible={showGroupManager}
        onClose={() => setShowGroupManager(false)}
      /> */}
    </VStack>
  );
};

export default ContactsFilter;
