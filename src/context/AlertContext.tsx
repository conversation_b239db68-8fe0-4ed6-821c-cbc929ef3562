import React, { createContext, useState, useContext, ReactNode } from 'react';
import CustomAlertModal, { AlertButton } from '../components/ui/CustomAlertModal';

interface AlertConfig {
  title: string;
  message: string;
  buttons: AlertButton[];
  onClose?: () => void; // Optional: for backdrop press or hardware back button
}

interface AlertContextType {
  showAlert: (config: AlertConfig) => void;
  hideAlert: () => void;
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

export const useAlert = () => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};

interface AlertProviderProps {
  children: ReactNode;
}

interface AlertState extends AlertConfig {
  visible: boolean;
}

const initialAlertState: AlertState = {
  visible: false,
  title: '',
  message: '',
  buttons: [],
  onClose: undefined,
};

export const AlertProvider: React.FC<AlertProviderProps> = ({ children }) => {
  const [alertState, setAlertState] = useState<AlertState>(initialAlertState);

  const showAlert = (config: AlertConfig) => {
    setAlertState({
      ...config,
      visible: true,
      onClose: config.onClose || hideAlert, // Default backdrop close to hideAlert
    });
  };

  const hideAlert = () => {
    // Call the original onClose if it exists before hiding
    if (alertState.onClose && alertState.visible) {
        // Check if it's different from the hideAlert itself to avoid infinite loop if not careful
        if (alertState.onClose !== hideAlert) {
            alertState.onClose(); 
        }
    }
    setAlertState(initialAlertState);
  };

  return (
    <AlertContext.Provider value={{ showAlert, hideAlert }}>
      {children}
      {/* <CustomAlertModal
        visible={alertState.visible}
        title={alertState.title}
        message={alertState.message}
        buttons={alertState.buttons.map(btn => ({
          ...btn,
          onPress: () => { // Ensure hideAlert is called after button press
            btn.onPress();
            hideAlert();
          }
        }))}
        onClose={hideAlert} // Always allow backdrop/hardware back to hide the alert
      /> */}
    </AlertContext.Provider>
  );
};
