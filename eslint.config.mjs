// eslint.config.js
import globals from "globals";
import tseslint from "@typescript-eslint/eslint-plugin";
import tsParser from "@typescript-eslint/parser";
import reactPlugin from "eslint-plugin-react";
import reactHooksPlugin from "eslint-plugin-react-hooks";
import reactNativePlugin from "eslint-plugin-react-native";

export default [
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      parser: tsParser,
      globals: {
        // Browser globals
        "window": "readonly",
        "document": "readonly",
        "navigator": "readonly",
        "console": "readonly",
        "localStorage": "readonly",
        "sessionStorage": "readonly",
        "fetch": "readonly",
        "setTimeout": "readonly",
        "setInterval": "readonly",
        "clearTimeout": "readonly",
        "clearInterval": "readonly",
        // Node.js globals
        "process": "readonly",
        "require": "readonly",
        "module": "readonly",
        "exports": "writable",
        "__dirname": "readonly",
        "__filename": "readonly",
        // ES2021 globals (subset, add more if needed)
        "AggregateError": "readonly",
        "FinalizationRegistry": "readonly",
        "Promise": "readonly",
        "Proxy": "readonly",
        "Reflect": "readonly",
        "Symbol": "readonly",
        "WeakRef": "readonly",
        // React Native global
        "__DEV__": "readonly"
      }
    },
    plugins: {
      "@typescript-eslint": tseslint,
      "react": reactPlugin,
      "react-hooks": reactHooksPlugin,
      "react-native": reactNativePlugin
    },
    rules: {
      ...tseslint.configs["eslint-recommended"].rules,
      ...tseslint.configs["recommended"].rules,
      ...reactPlugin.configs.recommended.rules,
      ...reactHooksPlugin.configs.recommended.rules,
      "react-native/no-unused-styles": 2,
      "react-native/split-platform-components": 2,
      "react-native/no-inline-styles": 1, // Warn for inline styles
      "react-native/no-color-literals": 1, // Warn for color literals
      "react-native/no-raw-text": 2, // Disallow raw text outside of Text components
      "react/prop-types": "off", // Not needed with TypeScript
      "react/react-in-jsx-scope": "off", // Not needed with new JSX transform
      "@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_" }], // Warn for unused vars
      // Add any project-specific rules here
    },
    settings: {
      react: {
        version: "detect" // Automatically detect the React version
      }
    }
  }
];
