/**
 * RelationHub 设计系统配置
 * 统一的设计规范和样式常量
 */

// 颜色系统
export const COLORS = {
  // 主色调
  primary: {
    50: '#E3F2FD',
    100: '#BBDEFB',
    200: '#90CAF9',
    300: '#64B5F6',
    400: '#42A5F5',
    500: '#007AFF', // 主色
    600: '#1E88E5',
    700: '#1976D2',
    800: '#1565C0',
    900: '#0D47A1',
  },
  
  // 中性色
  neutral: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  
  // 语义色
  semantic: {
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',
  },
  
  // 文本颜色
  text: {
    primary: '#333333',
    secondary: '#666666',
    tertiary: '#999999',
    disabled: '#CCCCCC',
    inverse: '#FFFFFF',
  },
  
  // 背景色
  background: {
    primary: '#FFFFFF',
    secondary: '#F5F5F5',
    tertiary: '#FAFAFA',
    overlay: 'rgba(0, 0, 0, 0.5)',
  },
  
  // 边框色
  border: {
    light: '#E0E0E0',
    medium: '#BDBDBD',
    dark: '#9E9E9E',
  },
} as const;

// 字体系统
export const TYPOGRAPHY = {
  // 字体大小
  fontSize: {
    xs: 10,
    sm: 12,
    base: 14,
    lg: 16,
    xl: 18,
    '2xl': 20,
    '3xl': 24,
    '4xl': 28,
    '5xl': 32,
  },
  
  // 字重
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  
  // 行高
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
} as const;

// 间距系统
export const SPACING = {
  // 基础间距单位 (4px)
  unit: 4,
  
  // 间距值
  xs: 4,   // 4px
  sm: 8,   // 8px
  md: 12,  // 12px
  lg: 16,  // 16px
  xl: 20,  // 20px
  '2xl': 24, // 24px
  '3xl': 32, // 32px
  '4xl': 40, // 40px
  '5xl': 48, // 48px
  
  // 页面级间距
  page: {
    horizontal: 16,
    vertical: 24,
    section: 32,
  },
  
  // 组件间距
  component: {
    card: 16,
    button: 12,
    input: 16,
    icon: 8,
  },
} as const;

// 圆角系统
export const BORDER_RADIUS = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
} as const;

// 阴影系统
export const SHADOWS = {
  sm: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  lg: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  xl: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
} as const;

// 图标尺寸系统
export const ICON_SIZES = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 28,
  '2xl': 32,
  '3xl': 36,
  '4xl': 40,
} as const;

// 按钮系统
export const BUTTON = {
  // 按钮尺寸
  sizes: {
    xs: {
      height: 28,
      paddingHorizontal: 12,
      paddingVertical: 6,
      fontSize: TYPOGRAPHY.fontSize.sm,
    },
    sm: {
      height: 32,
      paddingHorizontal: 16,
      paddingVertical: 8,
      fontSize: TYPOGRAPHY.fontSize.sm,
    },
    md: {
      height: 40,
      paddingHorizontal: 24,
      paddingVertical: 12,
      fontSize: TYPOGRAPHY.fontSize.base,
    },
    lg: {
      height: 48,
      paddingHorizontal: 32,
      paddingVertical: 16,
      fontSize: TYPOGRAPHY.fontSize.lg,
    },
    xl: {
      height: 56,
      paddingHorizontal: 40,
      paddingVertical: 20,
      fontSize: TYPOGRAPHY.fontSize.xl,
    },
  },
  
  // 圆形按钮尺寸
  circular: {
    sm: 32,
    md: 40,
    lg: 48,
    xl: 56,
    '2xl': 64,
  },
} as const;

// 输入框系统
export const INPUT = {
  sizes: {
    sm: {
      height: 32,
      paddingHorizontal: 12,
      paddingVertical: 8,
      fontSize: TYPOGRAPHY.fontSize.sm,
    },
    md: {
      height: 40,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: TYPOGRAPHY.fontSize.base,
    },
    lg: {
      height: 48,
      paddingHorizontal: 20,
      paddingVertical: 16,
      fontSize: TYPOGRAPHY.fontSize.lg,
    },
  },
} as const;

// 卡片系统
export const CARD = {
  padding: SPACING.lg,
  borderRadius: BORDER_RADIUS.lg,
  shadow: SHADOWS.sm,
  borderWidth: 1,
  borderColor: COLORS.border.light,
} as const;

// 动画系统
export const ANIMATIONS = {
  duration: {
    fast: 150,
    normal: 250,
    slow: 350,
  },
  
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
} as const;

// 断点系统
export const BREAKPOINTS = {
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  '2xl': 1400,
} as const;

// Z-index 系统
export const Z_INDEX = {
  base: 0,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070,
} as const;

// 导出所有设计系统常量
export const DESIGN_SYSTEM = {
  COLORS,
  TYPOGRAPHY,
  SPACING,
  BORDER_RADIUS,
  SHADOWS,
  ICON_SIZES,
  BUTTON,
  INPUT,
  CARD,
  ANIMATIONS,
  BREAKPOINTS,
  Z_INDEX,
} as const;

export default DESIGN_SYSTEM;
