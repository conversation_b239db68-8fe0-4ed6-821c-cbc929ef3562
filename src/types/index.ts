// src/types/index.ts
export interface Tag {
  id: string;
  name: string;
  color?: string; // Color for UI display, can be optional
}

export type TagCategory = 'industry' | 'relationship' | 'personal';

export interface Contact {
  id: string;
  name: string; // Combined first and last name, or primary display name
  avatar: string; // URL or local path for display
  photo?: string; // Potentially raw URI from image picker
  role?: string;
  company?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  address?: string;
  location?: string; // New field
  position?: string; // New field
  birthday?: string; // ISO date string, e.g., "YYYY-MM-DD"
  tags?: Tag[]; // Changed from string[] to Tag[]
  notes?: string;
  isFavorite?: boolean; // Whether the contact is marked as favorite
  // Consider adding fields from ContactData in ContactDetailScreen for more comprehensive type
  // For example:
  // lastInteraction?: string; // ISO date string
  // reminders?: Reminder[]; // If Reminder type is also centralized
  // connectionStrength?: number;
  // interactionFrequency?: string;
  // socialMedia?: Record<string, string>;
  // customFields?: Record<string, any>;
}

// Example of centralizing other types if needed in the future

// export interface Reminder {
//   id: string;
//   title: string;
//   dueDate: string; // ISO date string
//   priority: 'high' | 'medium' | 'low';
//   isCompleted: boolean;
// }


export type BottomTabParamList = {
  HomeTab: undefined;
  ContactsTab: undefined;
  CreateTab: undefined; // This is for the button, not a screen
  GraphTab: undefined;
  TasksTab: undefined;
};

export type RootStackParamList = {
  MainTabs: { screen: keyof BottomTabParamList };
  Home: undefined;
  Settings: undefined;
  ContactDetail: { contactId?: string; contact?: Contact; isEditedJustNow?: boolean };
  ContactCreate: { contact?: Contact };
  Contacts: { newContact?: Contact }; // Added for passing new contact data
  MeetingRecord: { contactId?: string };
  NetworkVisual: { focusedContactId?: string };
};

