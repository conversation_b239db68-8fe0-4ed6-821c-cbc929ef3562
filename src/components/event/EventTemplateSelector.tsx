/**
 * 活动模板选择器组件
 * 提供模板选择和预览功能
 */

import React, { useState, useMemo } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Heading,
  ScrollView,
  Pressable,
  Badge,
  BadgeText,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  FlatList,
  Input,
  InputField,
  InputSlot,
  InputIcon,
} from '@gluestack-ui/themed';
import {
  Search,
  X,
  Clock,
  Users,
  MapPin,
  Video,
  Star,
  Plus,
  Eye,
  CheckCircle,
} from 'lucide-react-native';

import { StandardButton, StandardIcon } from '../ui';
import { EventTemplate, TemplateCategory, eventTemplateService } from '../../services/eventTemplateService';
import { BusinessEvent } from '../../types';

interface EventTemplateSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTemplate: (template: EventTemplate) => void;
  userHistory?: BusinessEvent[];
  context?: {
    industry?: string;
    eventType?: string;
    scale?: string;
  };
}

export const EventTemplateSelector: React.FC<EventTemplateSelectorProps> = ({
  isOpen,
  onClose,
  onSelectTemplate,
  userHistory = [],
  context
}) => {
  const [selectedCategory, setSelectedCategory] = useState<TemplateCategory | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<EventTemplate | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // 获取模板数据
  const allTemplates = useMemo(() => eventTemplateService.getAllTemplates(), []);
  const recommendedTemplates = useMemo(() => 
    eventTemplateService.recommendTemplates(userHistory, context), 
    [userHistory, context]
  );

  // 过滤模板
  const filteredTemplates = useMemo(() => {
    let templates = selectedCategory === 'all' 
      ? allTemplates 
      : eventTemplateService.getTemplatesByCategory(selectedCategory);

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      templates = templates.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.category.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return templates;
  }, [allTemplates, selectedCategory, searchQuery]);

  // 类别选项
  const categories: Array<{ key: TemplateCategory | 'all'; label: string; icon: any }> = [
    { key: 'all', label: '全部', icon: Star },
    { key: 'networking', label: '社交网络', icon: Users },
    { key: 'business_meeting', label: '商务会议', icon: Users },
    { key: 'conference', label: '会议论坛', icon: Users },
    { key: 'training', label: '培训学习', icon: Users },
    { key: 'client_engagement', label: '客户关系', icon: Users },
    { key: 'product_launch', label: '产品发布', icon: Users },
    { key: 'custom', label: '自定义', icon: Plus },
  ];

  const handleTemplateSelect = (template: EventTemplate) => {
    onSelectTemplate(template);
    onClose();
  };

  const handleTemplatePreview = (template: EventTemplate) => {
    setSelectedTemplate(template);
    setShowPreview(true);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
    }
    return `${mins}分钟`;
  };

  const getScaleLabel = (scale: string) => {
    const labels: Record<string, string> = {
      small: '小型',
      medium: '中型',
      large: '大型',
      massive: '超大型'
    };
    return labels[scale] || scale;
  };

  const renderTemplateCard = ({ item: template }: { item: EventTemplate }) => {
    const isRecommended = recommendedTemplates.some(t => t.id === template.id);

    return (
      <Pressable
        onPress={() => handleTemplateSelect(template)}
        onLongPress={() => handleTemplatePreview(template)}
        bg="$backgroundLight0"
        borderRadius="$lg"
        borderWidth="$1"
        borderColor="$borderLight200"
        p="$4"
        mb="$3"
        sx={{
          _dark: {
            bg: '$backgroundDark900',
            borderColor: '$borderDark700',
          },
          _pressed: {
            bg: '$backgroundLight100',
            _dark: { bg: '$backgroundDark800' }
          }
        }}
      >
        <VStack space="md">
          {/* Header */}
          <HStack justifyContent="space-between" alignItems="flex-start">
            <HStack space="sm" alignItems="center" flex={1}>
              <Text fontSize="$2xl">{template.icon}</Text>
              <VStack flex={1} space="xs">
                <HStack space="sm" alignItems="center">
                  <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    {template.name}
                  </Text>
                  {isRecommended && (
                    <Badge size="xs" variant="solid" action="success">
                      <BadgeText>推荐</BadgeText>
                    </Badge>
                  )}
                  {template.isCustom && (
                    <Badge size="xs" variant="outline" action="muted">
                      <BadgeText>自定义</BadgeText>
                    </Badge>
                  )}
                </HStack>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  {template.description}
                </Text>
              </VStack>
            </HStack>

            <Pressable onPress={() => handleTemplatePreview(template)} p="$1">
              <StandardIcon as={Eye} size="sm" color="$textLight500" />
            </Pressable>
          </HStack>

          {/* Details */}
          <HStack space="lg" justifyContent="space-between">
            <HStack space="sm" alignItems="center">
              <StandardIcon as={Clock} size="xs" color="$textLight500" />
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                {formatDuration(template.defaultDuration)}
              </Text>
            </HStack>

            <HStack space="sm" alignItems="center">
              <StandardIcon as={template.isVirtual ? Video : MapPin} size="xs" color="$textLight500" />
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                {template.isVirtual ? '线上' : '线下'}
              </Text>
            </HStack>

            <HStack space="sm" alignItems="center">
              <StandardIcon as={Users} size="xs" color="$textLight500" />
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                {getScaleLabel(template.scale)}
              </Text>
            </HStack>

            {template.usageCount > 0 && (
              <HStack space="sm" alignItems="center">
                <StandardIcon as={Star} size="xs" color="$warning500" />
                <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  {template.usageCount}次
                </Text>
              </HStack>
            )}
          </HStack>

          {/* Tags */}
          {template.tags.length > 0 && (
            <HStack space="xs" flexWrap="wrap">
              {template.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} size="xs" variant="outline" action="muted">
                  <BadgeText>{tag}</BadgeText>
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <Badge size="xs" variant="outline" action="muted">
                  <BadgeText>+{template.tags.length - 3}</BadgeText>
                </Badge>
              )}
            </HStack>
          )}
        </VStack>
      </Pressable>
    );
  };

  return (
    <>
      {/* 主模态框 */}
      <Modal isOpen={isOpen} onClose={onClose} size="full">
        <ModalBackdrop />
        <ModalContent>
          <ModalHeader>
            <VStack space="sm" flex={1}>
              <HStack justifyContent="space-between" alignItems="center">
                <Heading size="lg">选择活动模板</Heading>
                <ModalCloseButton>
                  <StandardIcon as={X} />
                </ModalCloseButton>
              </HStack>
              
              {/* 搜索框 */}
              <Input variant="outline" size="md">
                <InputSlot pl="$3">
                  <InputIcon as={Search} />
                </InputSlot>
                <InputField
                  placeholder="搜索模板..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                />
              </Input>
            </VStack>
          </ModalHeader>

          <ModalBody>
            <VStack space="md" flex={1}>
              {/* 类别选择 */}
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <HStack space="sm" px="$2">
                  {categories.map((category) => (
                    <Pressable
                      key={category.key}
                      onPress={() => setSelectedCategory(category.key)}
                      px="$3"
                      py="$2"
                      borderRadius="$full"
                      bg={selectedCategory === category.key ? '$primary100' : 'transparent'}
                      borderWidth="$1"
                      borderColor={selectedCategory === category.key ? '$primary300' : '$borderLight200'}
                      sx={{
                        _dark: {
                          bg: selectedCategory === category.key ? '$primary900' : 'transparent',
                          borderColor: selectedCategory === category.key ? '$primary700' : '$borderDark700'
                        }
                      }}
                    >
                      <HStack space="xs" alignItems="center">
                        <StandardIcon 
                          as={category.icon} 
                          size="xs" 
                          color={selectedCategory === category.key ? '$primary600' : '$textLight600'} 
                        />
                        <Text
                          size="sm"
                          fontWeight={selectedCategory === category.key ? '$medium' : '$normal'}
                          color={selectedCategory === category.key ? '$primary600' : '$textLight600'}
                          sx={{
                            _dark: {
                              color: selectedCategory === category.key ? '$primary400' : '$textDark400'
                            }
                          }}
                        >
                          {category.label}
                        </Text>
                      </HStack>
                    </Pressable>
                  ))}
                </HStack>
              </ScrollView>

              {/* 推荐模板 */}
              {selectedCategory === 'all' && recommendedTemplates.length > 0 && (
                <VStack space="sm">
                  <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    为您推荐
                  </Text>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    <HStack space="md" px="$2">
                      {recommendedTemplates.slice(0, 3).map((template) => (
                        <Box key={template.id} width="$64">
                          {renderTemplateCard({ item: template })}
                        </Box>
                      ))}
                    </HStack>
                  </ScrollView>
                </VStack>
              )}

              {/* 模板列表 */}
              <VStack space="sm" flex={1}>
                <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  {selectedCategory === 'all' ? '所有模板' : categories.find(c => c.key === selectedCategory)?.label}
                  <Text color="$textLight500"> ({filteredTemplates.length})</Text>
                </Text>
                
                <FlatList
                  data={filteredTemplates}
                  keyExtractor={(item) => item.id}
                  renderItem={renderTemplateCard}
                  showsVerticalScrollIndicator={false}
                  ListEmptyComponent={
                    <Box py="$8" alignItems="center">
                      <VStack space="md" alignItems="center">
                        <StandardIcon as={Search} size="2xl" color="$textLight400" />
                        <Text color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                          {searchQuery ? '没有找到匹配的模板' : '暂无模板'}
                        </Text>
                      </VStack>
                    </Box>
                  }
                />
              </VStack>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <HStack space="md" flex={1}>
              <StandardButton
                variant="outline"
                onPress={onClose}
                flex={1}
              >
                取消
              </StandardButton>
              <StandardButton
                variant="outline"
                onPress={() => {
                  // TODO: 创建自定义模板
                }}
                flex={1}
                leftIcon={Plus}
              >
                自定义模板
              </StandardButton>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 模板预览模态框 */}
      {selectedTemplate && (
        <Modal isOpen={showPreview} onClose={() => setShowPreview(false)}>
          <ModalBackdrop />
          <ModalContent maxWidth="$96">
            <ModalHeader>
              <HStack space="sm" alignItems="center" flex={1}>
                <Text fontSize="$2xl">{selectedTemplate.icon}</Text>
                <VStack flex={1}>
                  <Heading size="lg">{selectedTemplate.name}</Heading>
                  <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                    {selectedTemplate.category}
                  </Text>
                </VStack>
              </HStack>
              <ModalCloseButton>
                <StandardIcon as={X} />
              </ModalCloseButton>
            </ModalHeader>

            <ModalBody>
              <VStack space="lg">
                {/* 基本信息 */}
                <VStack space="sm">
                  <Text fontWeight="$bold">描述</Text>
                  <Text color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                    {selectedTemplate.description}
                  </Text>
                </VStack>

                {/* 详细信息 */}
                <VStack space="sm">
                  <Text fontWeight="$bold">活动详情</Text>
                  <VStack space="xs">
                    <HStack justifyContent="space-between">
                      <Text>时长:</Text>
                      <Text>{formatDuration(selectedTemplate.defaultDuration)}</Text>
                    </HStack>
                    <HStack justifyContent="space-between">
                      <Text>规模:</Text>
                      <Text>{getScaleLabel(selectedTemplate.scale)}</Text>
                    </HStack>
                    <HStack justifyContent="space-between">
                      <Text>形式:</Text>
                      <Text>{selectedTemplate.isVirtual ? '线上活动' : '线下活动'}</Text>
                    </HStack>
                  </VStack>
                </VStack>

                {/* 议程 */}
                {selectedTemplate.agenda && (
                  <VStack space="sm">
                    <Text fontWeight="$bold">议程</Text>
                    <Text color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                      {selectedTemplate.agenda}
                    </Text>
                  </VStack>
                )}

                {/* 目标 */}
                {selectedTemplate.objectives.length > 0 && (
                  <VStack space="sm">
                    <Text fontWeight="$bold">活动目标</Text>
                    {selectedTemplate.objectives.map((objective, index) => (
                      <HStack key={index} space="xs" alignItems="center">
                        <StandardIcon as={CheckCircle} size="xs" color="$success600" />
                        <Text color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                          {objective}
                        </Text>
                      </HStack>
                    ))}
                  </VStack>
                )}
              </VStack>
            </ModalBody>

            <ModalFooter>
              <HStack space="md" flex={1}>
                <StandardButton
                  variant="outline"
                  onPress={() => setShowPreview(false)}
                  flex={1}
                >
                  关闭
                </StandardButton>
                <StandardButton
                  variant="solid"
                  action="primary"
                  onPress={() => {
                    setShowPreview(false);
                    handleTemplateSelect(selectedTemplate);
                  }}
                  flex={1}
                >
                  使用模板
                </StandardButton>
              </HStack>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}
    </>
  );
};

export default EventTemplateSelector;
