/**
 * 可滑动行组件
 * 支持左滑右滑显示操作按钮
 */

import React, { useRef, useState } from 'react';
import { Animated } from 'react-native';
import {
  PanGestureHandler,
  PanGestureHandlerGestureEvent,
  State,
} from 'react-native-gesture-handler';
import {
  Box,
  HStack,
  Pressable,
  Text,
} from '@gluestack-ui/themed';
import {
  Trash2,
  Edit,
  Heart,
  Phone,
  MessageCircle,
  Star,
  Archive,
  Share,
} from 'lucide-react-native';

import { StandardIcon } from './StandardIcon';

export interface SwipeAction {
  id: string;
  title: string;
  icon: any;
  color: string;
  backgroundColor: string;
  onPress: () => void;
}

interface SwipeableRowProps {
  children: React.ReactNode;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  onSwipeStart?: () => void;
  onSwipeEnd?: () => void;
  disabled?: boolean;
}

export const SwipeableRow: React.FC<SwipeableRowProps> = ({
  children,
  leftActions = [],
  rightActions = [],
  onSwipeStart,
  onSwipeEnd,
  disabled = false,
}) => {
  const translateX = useRef(new Animated.Value(0)).current;
  const [isSwipeActive, setIsSwipeActive] = useState(false);
  const [swipeDirection, setSwipeDirection] = useState<'left' | 'right' | null>(null);

  const SWIPE_THRESHOLD = 80;
  const ACTION_WIDTH = 80;

  const handleGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: false }
  );

  const handleStateChange = (event: PanGestureHandlerGestureEvent) => {
    if (disabled) return;

    const { state, translationX, velocityX } = event.nativeEvent;

    if (state === State.BEGAN) {
      setIsSwipeActive(true);
      onSwipeStart?.();
    } else if (state === State.END) {
      const shouldTriggerAction = Math.abs(translationX) > SWIPE_THRESHOLD || Math.abs(velocityX) > 500;
      
      if (shouldTriggerAction) {
        const direction = translationX > 0 ? 'right' : 'left';
        setSwipeDirection(direction);
        
        const maxSwipe = direction === 'right' 
          ? leftActions.length * ACTION_WIDTH 
          : -rightActions.length * ACTION_WIDTH;
        
        Animated.spring(translateX, {
          toValue: maxSwipe,
          useNativeDriver: false,
          tension: 100,
          friction: 8,
        }).start();
      } else {
        // 回弹到原位
        Animated.spring(translateX, {
          toValue: 0,
          useNativeDriver: false,
          tension: 100,
          friction: 8,
        }).start(() => {
          setIsSwipeActive(false);
          setSwipeDirection(null);
          onSwipeEnd?.();
        });
      }
    }
  };

  const resetSwipe = () => {
    Animated.spring(translateX, {
      toValue: 0,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start(() => {
      setIsSwipeActive(false);
      setSwipeDirection(null);
      onSwipeEnd?.();
    });
  };

  const handleActionPress = (action: SwipeAction) => {
    action.onPress();
    resetSwipe();
  };

  const renderActions = (actions: SwipeAction[], side: 'left' | 'right') => {
    if (actions.length === 0) return null;

    return (
      <HStack
        position="absolute"
        top={0}
        bottom={0}
        right={side === 'right' ? 0 : undefined}
        left={side === 'left' ? 0 : undefined}
        width={actions.length * ACTION_WIDTH}
      >
        {actions.map((action, index) => (
          <Pressable
            key={action.id}
            onPress={() => handleActionPress(action)}
            width={ACTION_WIDTH}
            justifyContent="center"
            alignItems="center"
            bg={action.backgroundColor}
            sx={{
              _pressed: {
                opacity: 0.8,
              }
            }}
          >
            <StandardIcon
              as={action.icon}
              size="md"
              color={action.color}
            />
            <Text
              size="xs"
              color={action.color}
              fontWeight="$medium"
              mt="$1"
            >
              {action.title}
            </Text>
          </Pressable>
        ))}
      </HStack>
    );
  };

  return (
    <Box position="relative" overflow="hidden">
      {/* 左侧操作 */}
      {renderActions(leftActions, 'left')}
      
      {/* 右侧操作 */}
      {renderActions(rightActions, 'right')}

      {/* 主内容 */}
      <PanGestureHandler
        onGestureEvent={handleGestureEvent}
        onHandlerStateChange={handleStateChange}
        enabled={!disabled}
        activeOffsetX={[-10, 10]}
        failOffsetY={[-20, 20]}
      >
        <Animated.View
          style={{
            transform: [{ translateX }],
            backgroundColor: 'transparent',
          }}
        >
          <Box bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark900' } }}>
            {children}
          </Box>
        </Animated.View>
      </PanGestureHandler>

      {/* 遮罩层 - 点击重置滑动 */}
      {isSwipeActive && (
        <Pressable
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          onPress={resetSwipe}
          bg="transparent"
        />
      )}
    </Box>
  );
};

// 预定义的常用操作
export const SwipeActions = {
  delete: (onPress: () => void): SwipeAction => ({
    id: 'delete',
    title: '删除',
    icon: Trash2,
    color: '$white',
    backgroundColor: '$error600',
    onPress,
  }),

  edit: (onPress: () => void): SwipeAction => ({
    id: 'edit',
    title: '编辑',
    icon: Edit,
    color: '$white',
    backgroundColor: '$primary600',
    onPress,
  }),

  favorite: (onPress: () => void, isFavorite: boolean = false): SwipeAction => ({
    id: 'favorite',
    title: isFavorite ? '取消收藏' : '收藏',
    icon: isFavorite ? Star : Heart,
    color: '$white',
    backgroundColor: isFavorite ? '$warning600' : '$error500',
    onPress,
  }),

  call: (onPress: () => void): SwipeAction => ({
    id: 'call',
    title: '拨打',
    icon: Phone,
    color: '$white',
    backgroundColor: '$success600',
    onPress,
  }),

  message: (onPress: () => void): SwipeAction => ({
    id: 'message',
    title: '消息',
    icon: MessageCircle,
    color: '$white',
    backgroundColor: '$info600',
    onPress,
  }),

  archive: (onPress: () => void): SwipeAction => ({
    id: 'archive',
    title: '归档',
    icon: Archive,
    color: '$white',
    backgroundColor: '$secondary600',
    onPress,
  }),

  share: (onPress: () => void): SwipeAction => ({
    id: 'share',
    title: '分享',
    icon: Share,
    color: '$white',
    backgroundColor: '$info500',
    onPress,
  }),
};

// 默认导出
export default SwipeableRow;
