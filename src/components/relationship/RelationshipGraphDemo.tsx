/**
 * 关系图谱演示组件
 * 用于测试和演示关系图谱功能
 */

import React, { useEffect } from 'react';
import {
  Box,
  Text,
  VStack,
  HStack,
  Heading,
  ScrollView,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';

import { StandardButton } from '../ui';
import { SimpleRelationshipGraph } from './SimpleRelationshipGraph';
import { useContactsStore, useRelationshipStore } from '../../store';

export const RelationshipGraphDemo: React.FC = () => {
  const toast = useToast();
  const { contacts } = useContactsStore();
  const { relationships, initializeSampleData } = useRelationshipStore();

  // 初始化示例数据
  useEffect(() => {
    initializeSampleData();
  }, [initializeSampleData]);

  // 选择一个有关系的联系人作为中心
  const centerContact = contacts.find(c => c.id === 'contact_001') || contacts[0];

  if (!centerContact) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" p="$8">
        <VStack space="md" alignItems="center">
          <Text>没有找到联系人数据</Text>
          <Text size="sm" color="$textLight600">
            请先添加一些联系人
          </Text>
        </VStack>
      </Box>
    );
  }

  return (
    <Box flex={1} bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark950' } }}>
      {/* 标题 */}
      <Box p="$4" bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark900' } }}>
        <VStack space="sm">
          <Heading size="lg">关系图谱演示</Heading>
          <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
            以 {centerContact.name} 为中心的关系网络
          </Text>
          <HStack space="md" justifyContent="space-between">
            <VStack alignItems="center">
              <Text fontWeight="$bold" color="$primary600">
                {contacts.length}
              </Text>
              <Text size="xs">联系人</Text>
            </VStack>
            <VStack alignItems="center">
              <Text fontWeight="$bold" color="$success600">
                {relationships.length}
              </Text>
              <Text size="xs">关系</Text>
            </VStack>
          </HStack>
        </VStack>
      </Box>

      {/* 图谱展示 */}
      <Box flex={1}>
        <SimpleRelationshipGraph
          centerContactId={centerContact.id}
          contacts={contacts}
          relationships={relationships}
          maxDepth={2}
          onContactPress={(contactId) => {
            const contact = contacts.find(c => c.id === contactId);
            toast.show({
              placement: "top",
              render: ({ id }) => (
                <Toast nativeID={id} action="info" variant="accent">
                  <ToastTitle>联系人详情</ToastTitle>
                  <ToastDescription>
                    点击了 {contact?.name || contactId}
                  </ToastDescription>
                </Toast>
              )
            });
          }}
          onAddRelationship={(fromId, toId) => {
            toast.show({
              placement: "top",
              render: ({ id }) => (
                <Toast nativeID={id} action="info" variant="accent">
                  <ToastTitle>添加关系</ToastTitle>
                  <ToastDescription>
                    从 {fromId} 到 {toId} 的关系
                  </ToastDescription>
                </Toast>
              )
            });
          }}
        />
      </Box>

      {/* 操作按钮 */}
      <Box p="$4" bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark900' } }}>
        <HStack space="md">
          <StandardButton
            variant="outline"
            onPress={() => {
              initializeSampleData();
              toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={id} action="success" variant="accent">
                    <ToastTitle>数据已重新加载</ToastTitle>
                  </Toast>
                )
              });
            }}
            flex={1}
          >
            重新加载数据
          </StandardButton>
          
          <StandardButton
            variant="solid"
            action="primary"
            onPress={() => {
              toast.show({
                placement: "top",
                render: ({ id }) => (
                  <Toast nativeID={id} action="info" variant="accent">
                    <ToastTitle>功能演示</ToastTitle>
                    <ToastDescription>
                      点击节点查看详情，长按分析关系路径
                    </ToastDescription>
                  </Toast>
                )
              });
            }}
            flex={1}
          >
            使用说明
          </StandardButton>
        </HStack>
      </Box>
    </Box>
  );
};

export default RelationshipGraphDemo;
