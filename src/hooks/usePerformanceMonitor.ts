import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  componentName: string;
  renderTime: number;
  mountTime: number;
  updateCount: number;
}

/**
 * 性能监控Hook
 * 监控组件渲染性能和更新频率
 */
export const usePerformanceMonitor = (componentName: string, enabled: boolean = __DEV__) => {
  const renderStartTime = useRef<number>(0);
  const mountTime = useRef<number>(0);
  const updateCount = useRef<number>(0);
  const isFirstRender = useRef<boolean>(true);

  // 记录渲染开始时间
  if (enabled) {
    renderStartTime.current = performance.now();
  }

  useEffect(() => {
    if (!enabled) return;

    const renderEndTime = performance.now();
    const renderTime = renderEndTime - renderStartTime.current;

    if (isFirstRender.current) {
      // 首次挂载
      mountTime.current = renderTime;
      isFirstRender.current = false;
      
      if (__DEV__) {
        console.log(`🚀 [Performance] ${componentName} mounted in ${renderTime.toFixed(2)}ms`);
      }
    } else {
      // 更新渲染
      updateCount.current += 1;
      
      if (__DEV__ && renderTime > 16) { // 超过一帧的时间
        console.warn(`⚠️ [Performance] ${componentName} slow render: ${renderTime.toFixed(2)}ms (update #${updateCount.current})`);
      }
    }
  });

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      if (enabled && __DEV__) {
        console.log(`🔄 [Performance] ${componentName} unmounted after ${updateCount.current} updates`);
      }
    };
  }, [componentName, enabled]);

  return {
    renderTime: performance.now() - renderStartTime.current,
    mountTime: mountTime.current,
    updateCount: updateCount.current,
  };
};

/**
 * 内存使用监控Hook
 * 监控内存使用情况（仅在开发环境）
 */
export const useMemoryMonitor = (interval: number = 5000) => {
  useEffect(() => {
    if (!__DEV__) return;

    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const used = Math.round(memory.usedJSHeapSize / 1048576); // MB
        const total = Math.round(memory.totalJSHeapSize / 1048576); // MB
        const limit = Math.round(memory.jsHeapSizeLimit / 1048576); // MB
        
        console.log(`💾 [Memory] Used: ${used}MB / Total: ${total}MB / Limit: ${limit}MB`);
        
        // 警告内存使用过高
        if (used / limit > 0.8) {
          console.warn(`⚠️ [Memory] High memory usage: ${Math.round((used / limit) * 100)}%`);
        }
      }
    };

    const intervalId = setInterval(checkMemory, interval);
    
    return () => clearInterval(intervalId);
  }, [interval]);
};

/**
 * 网络请求性能监控
 */
export const measureNetworkRequest = async <T>(
  requestName: string,
  requestFn: () => Promise<T>
): Promise<T> => {
  const startTime = performance.now();
  
  try {
    const result = await requestFn();
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    if (__DEV__) {
      console.log(`🌐 [Network] ${requestName} completed in ${duration.toFixed(2)}ms`);
      
      if (duration > 3000) {
        console.warn(`⚠️ [Network] Slow request: ${requestName} took ${duration.toFixed(2)}ms`);
      }
    }
    
    return result;
  } catch (error) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    if (__DEV__) {
      console.error(`❌ [Network] ${requestName} failed after ${duration.toFixed(2)}ms:`, error);
    }
    
    throw error;
  }
};
