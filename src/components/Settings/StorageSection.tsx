import React from 'react';
import {
  VStack,
  HStack,
  Text,
  Progress,
  ProgressFilledTrack
} from '@gluestack-ui/themed';

interface StorageSectionProps {
  storageUsed: string;
  storageTotal: string;
  usagePercentage: number;
}

/**
 * 存储信息区域组件
 * 显示存储使用情况和进度条
 */
const StorageSection: React.FC<StorageSectionProps> = ({
  storageUsed,
  storageTotal,
  usagePercentage
}) => {
  return (
    <VStack
      bg="$backgroundLight0"
      p="$4"
      mb="$5"
      space="md"
      sx={{
        _dark: {
          bg: '$backgroundDark900'
        }
      }}
    >
      <HStack justifyContent="space-between" alignItems="center">
        <Text 
          fontSize="$sm" 
          color="$textLight600"
          sx={{ _dark: { color: '$textDark400' } }}
        >
          Storage
        </Text>
        <Text 
          fontSize="$sm" 
          fontWeight="$semibold" 
          color="$textLight900"
          sx={{ _dark: { color: '$textDark100' } }}
        >
          {usagePercentage}% Used
        </Text>
      </HStack>

      <Progress value={usagePercentage} size="sm">
        <ProgressFilledTrack bg="$primary500" />
      </Progress>

      <HStack justifyContent="space-between" alignItems="center">
        <Text 
          fontSize="$xs" 
          color="$textLight500"
          sx={{ _dark: { color: '$textDark500' } }}
        >
          {storageUsed} of {storageTotal} used
        </Text>
      </HStack>
    </VStack>
  );
};

export default StorageSection;
