import React from 'react';
import { VStack } from '@gluestack-ui/themed';
import SectionHeader from './SectionHeader';

interface FormSectionProps extends VStackProps {
  title: string;
  children: React.ReactNode;
  headerSize?: 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * 通用的表单区域组件
 * 包含标题和内容区域，用于组织表单的不同部分
 */
const FormSection: React.FC<FormSectionProps> = ({ 
  title, 
  children, 
  headerSize = 'md',
  ...props 
}) => {
  return (
    <VStack space="md" {...props}>
      <SectionHeader title={title} size={headerSize} />
      {children}
    </VStack>
  );
};

export default FormSection;
