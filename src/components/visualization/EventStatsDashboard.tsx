/**
 * 活动统计仪表板组件
 * 显示活动效果统计和趋势分析
 */

import React, { useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  BadgeText,
  ScrollView,
} from '@gluestack-ui/themed';
import {
  Calendar,
  Users,
  TrendingUp,
  Target,
  Award,
  Clock,
  MapPin,
  Video,
} from 'lucide-react-native';

import { StandardIcon } from '../ui/StandardIcon';
import { BusinessEvent, Contact } from '../../types';

interface EventStatsData {
  totalEvents: number;
  upcomingEvents: number;
  completedEvents: number;
  totalParticipants: number;
  avgParticipants: number;
  relationshipsEstablished: number;
  relationshipsStrengthened: number;
  eventTypes: Array<{ type: string; count: number; percentage: number }>;
  monthlyTrend: Array<{ month: string; count: number; effectiveness: number }>;
  topPerformingEvents: BusinessEvent[];
}

interface EventStatsDashboardProps {
  events: BusinessEvent[];
  contacts: Contact[];
  timeRange?: 'week' | 'month' | 'quarter' | 'year';
}

export const EventStatsDashboard: React.FC<EventStatsDashboardProps> = ({
  events,
  contacts,
  timeRange = 'month',
}) => {
  // 计算统计数据
  const statsData = useMemo(() => {
    return calculateEventStats(events, timeRange);
  }, [events, timeRange]);

  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <VStack space="lg" p="$4">
        {/* 概览卡片 */}
        <VStack space="md">
          <Text fontWeight="$bold" size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
            活动统计概览
          </Text>
          
          <HStack space="md">
            <StatCard
              title="总活动数"
              value={statsData.totalEvents}
              icon={Calendar}
              color="$primary600"
              trend={statsData.totalEvents > 0 ? '+12%' : undefined}
            />
            <StatCard
              title="即将到来"
              value={statsData.upcomingEvents}
              icon={Clock}
              color="$warning600"
            />
          </HStack>

          <HStack space="md">
            <StatCard
              title="总参与者"
              value={statsData.totalParticipants}
              icon={Users}
              color="$success600"
              subtitle={`平均 ${statsData.avgParticipants} 人/活动`}
            />
            <StatCard
              title="建立关系"
              value={statsData.relationshipsEstablished}
              icon={TrendingUp}
              color="$info600"
              subtitle={`强化 ${statsData.relationshipsStrengthened} 个`}
            />
          </HStack>
        </VStack>

        {/* 活动类型分布 */}
        <Box
          p="$4"
          bg="$backgroundLight0"
          borderRadius="$lg"
          borderWidth="$1"
          borderColor="$borderLight200"
          sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
        >
          <VStack space="md">
            <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              活动类型分布
            </Text>
            
            <VStack space="sm">
              {statsData.eventTypes.map((eventType, index) => (
                <HStack key={index} justifyContent="space-between" alignItems="center">
                  <HStack space="sm" alignItems="center" flex={1}>
                    <Box
                      width={12}
                      height={12}
                      borderRadius="$sm"
                      bg={getEventTypeColor(eventType.type)}
                    />
                    <Text
                      size="sm"
                      color="$textLight700"
                      sx={{ _dark: { color: '$textDark300' } }}
                      flex={1}
                    >
                      {getEventTypeDisplayName(eventType.type)}
                    </Text>
                  </HStack>
                  
                  <HStack space="sm" alignItems="center">
                    <Text
                      size="sm"
                      fontWeight="$medium"
                      color="$textLight900"
                      sx={{ _dark: { color: '$textDark100' } }}
                    >
                      {eventType.count}
                    </Text>
                    <Badge size="xs" variant="outline">
                      <BadgeText>{eventType.percentage}%</BadgeText>
                    </Badge>
                  </HStack>
                </HStack>
              ))}
            </VStack>
          </VStack>
        </Box>

        {/* 月度趋势 */}
        <Box
          p="$4"
          bg="$backgroundLight0"
          borderRadius="$lg"
          borderWidth="$1"
          borderColor="$borderLight200"
          sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
        >
          <VStack space="md">
            <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              月度活动趋势
            </Text>
            
            <VStack space="sm">
              {statsData.monthlyTrend.map((month, index) => (
                <HStack key={index} justifyContent="space-between" alignItems="center">
                  <Text
                    size="sm"
                    color="$textLight700"
                    sx={{ _dark: { color: '$textDark300' } }}
                    flex={1}
                  >
                    {month.month}
                  </Text>
                  
                  <HStack space="md" alignItems="center">
                    <HStack space="xs" alignItems="center">
                      <StandardIcon as={Calendar} size="xs" color="$textLight500" />
                      <Text
                        size="sm"
                        fontWeight="$medium"
                        color="$textLight900"
                        sx={{ _dark: { color: '$textDark100' } }}
                      >
                        {month.count}
                      </Text>
                    </HStack>
                    
                    <HStack space="xs" alignItems="center">
                      <StandardIcon as={Target} size="xs" color="$success600" />
                      <Text
                        size="sm"
                        color="$success600"
                      >
                        {month.effectiveness}%
                      </Text>
                    </HStack>
                  </HStack>
                </HStack>
              ))}
            </VStack>
          </VStack>
        </Box>

        {/* 表现最佳的活动 */}
        {statsData.topPerformingEvents.length > 0 && (
          <Box
            p="$4"
            bg="$backgroundLight0"
            borderRadius="$lg"
            borderWidth="$1"
            borderColor="$borderLight200"
            sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
          >
            <VStack space="md">
              <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                表现最佳的活动
              </Text>
              
              <VStack space="sm">
                {statsData.topPerformingEvents.slice(0, 3).map((event, index) => (
                  <Box
                    key={event.id}
                    p="$3"
                    bg="$backgroundLight50"
                    borderRadius="$md"
                    sx={{ _dark: { bg: '$backgroundDark800' } }}
                  >
                    <VStack space="xs">
                      <HStack justifyContent="space-between" alignItems="flex-start">
                        <VStack flex={1} space="xs">
                          <Text
                            fontWeight="$bold"
                            size="sm"
                            color="$textLight900"
                            sx={{ _dark: { color: '$textDark100' } }}
                          >
                            {event.title}
                          </Text>
                          <Text
                            size="xs"
                            color="$textLight600"
                            sx={{ _dark: { color: '$textDark400' } }}
                          >
                            {new Date(event.date).toLocaleDateString('zh-CN')} • {getEventTypeDisplayName(event.eventType)}
                          </Text>
                        </VStack>
                        
                        <HStack space="xs" alignItems="center">
                          <StandardIcon as={Award} size="xs" color="$warning500" />
                          <Text size="xs" color="$warning600" fontWeight="$medium">
                            #{index + 1}
                          </Text>
                        </HStack>
                      </HStack>
                      
                      <HStack space="lg" justifyContent="space-between">
                        <HStack space="xs" alignItems="center">
                          <StandardIcon as={Users} size="xs" color="$textLight500" />
                          <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                            {event.participants.length} 参与者
                          </Text>
                        </HStack>
                        
                        <HStack space="xs" alignItems="center">
                          <StandardIcon as={TrendingUp} size="xs" color="$success600" />
                          <Text size="xs" color="$success600">
                            {event.relationshipsEstablished} 新关系
                          </Text>
                        </HStack>
                        
                        <HStack space="xs" alignItems="center">
                          <StandardIcon as={event.isVirtual ? Video : MapPin} size="xs" color="$textLight500" />
                          <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                            {event.isVirtual ? '线上' : '线下'}
                          </Text>
                        </HStack>
                      </HStack>
                    </VStack>
                  </Box>
                ))}
              </VStack>
            </VStack>
          </Box>
        )}
      </VStack>
    </ScrollView>
  );
};

// 统计卡片组件
interface StatCardProps {
  title: string;
  value: number;
  icon: any;
  color: string;
  subtitle?: string;
  trend?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  color,
  subtitle,
  trend,
}) => (
  <Box
    flex={1}
    p="$4"
    bg="$backgroundLight0"
    borderRadius="$lg"
    borderWidth="$1"
    borderColor="$borderLight200"
    sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
  >
    <VStack space="sm">
      <HStack justifyContent="space-between" alignItems="center">
        <Text
          size="sm"
          color="$textLight600"
          sx={{ _dark: { color: '$textDark400' } }}
        >
          {title}
        </Text>
        <StandardIcon as={icon} size="sm" color={color} />
      </HStack>
      
      <Text
        fontSize="$2xl"
        fontWeight="$bold"
        color="$textLight900"
        sx={{ _dark: { color: '$textDark100' } }}
      >
        {value}
      </Text>
      
      {(subtitle || trend) && (
        <HStack justifyContent="space-between" alignItems="center">
          {subtitle && (
            <Text
              size="xs"
              color="$textLight500"
              sx={{ _dark: { color: '$textDark500' } }}
            >
              {subtitle}
            </Text>
          )}
          {trend && (
            <Badge size="xs" variant="solid" action="success">
              <BadgeText>{trend}</BadgeText>
            </Badge>
          )}
        </HStack>
      )}
    </VStack>
  </Box>
);

/**
 * 计算活动统计数据
 */
function calculateEventStats(events: BusinessEvent[], timeRange: string): EventStatsData {
  const now = new Date();
  const filteredEvents = filterEventsByTimeRange(events, timeRange, now);

  // 基础统计
  const totalEvents = filteredEvents.length;
  const upcomingEvents = filteredEvents.filter(e => new Date(e.date) > now).length;
  const completedEvents = filteredEvents.filter(e => e.status === 'completed').length;
  
  const totalParticipants = filteredEvents.reduce((sum, e) => sum + e.participants.length, 0);
  const avgParticipants = totalEvents > 0 ? Math.round(totalParticipants / totalEvents) : 0;
  
  const relationshipsEstablished = filteredEvents.reduce((sum, e) => sum + (e.relationshipsEstablished || 0), 0);
  const relationshipsStrengthened = filteredEvents.reduce((sum, e) => sum + (e.relationshipsStrengthened || 0), 0);

  // 活动类型分布
  const typeCount = new Map<string, number>();
  filteredEvents.forEach(event => {
    typeCount.set(event.eventType, (typeCount.get(event.eventType) || 0) + 1);
  });

  const eventTypes = Array.from(typeCount.entries()).map(([type, count]) => ({
    type,
    count,
    percentage: Math.round((count / totalEvents) * 100) || 0,
  })).sort((a, b) => b.count - a.count);

  // 月度趋势（简化版本）
  const monthlyTrend = generateMonthlyTrend(filteredEvents);

  // 表现最佳的活动
  const topPerformingEvents = filteredEvents
    .filter(e => e.status === 'completed')
    .sort((a, b) => {
      const scoreA = (a.relationshipsEstablished || 0) + (a.relationshipsStrengthened || 0) * 0.5;
      const scoreB = (b.relationshipsEstablished || 0) + (b.relationshipsStrengthened || 0) * 0.5;
      return scoreB - scoreA;
    });

  return {
    totalEvents,
    upcomingEvents,
    completedEvents,
    totalParticipants,
    avgParticipants,
    relationshipsEstablished,
    relationshipsStrengthened,
    eventTypes,
    monthlyTrend,
    topPerformingEvents,
  };
}

function filterEventsByTimeRange(events: BusinessEvent[], timeRange: string, now: Date): BusinessEvent[] {
  const ranges = {
    week: 7,
    month: 30,
    quarter: 90,
    year: 365,
  };

  const days = ranges[timeRange as keyof typeof ranges] || 30;
  const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

  return events.filter(event => new Date(event.date) >= startDate);
}

function generateMonthlyTrend(events: BusinessEvent[]): Array<{ month: string; count: number; effectiveness: number }> {
  const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
  const now = new Date();
  const trend = [];

  for (let i = 5; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const monthEvents = events.filter(e => {
      const eventDate = new Date(e.date);
      return eventDate.getFullYear() === date.getFullYear() && 
             eventDate.getMonth() === date.getMonth();
    });

    const completedEvents = monthEvents.filter(e => e.status === 'completed');
    const effectiveness = completedEvents.length > 0 
      ? Math.round((completedEvents.reduce((sum, e) => sum + (e.relationshipsEstablished || 0), 0) / completedEvents.length) * 100)
      : 0;

    trend.push({
      month: monthNames[date.getMonth()],
      count: monthEvents.length,
      effectiveness,
    });
  }

  return trend;
}

function getEventTypeColor(eventType: string): string {
  const colors: Record<string, string> = {
    business_meeting: '#3B82F6',
    industry_conference: '#8B5CF6',
    exhibition: '#F59E0B',
    seminar: '#10B981',
    networking: '#EF4444',
    client_visit: '#06B6D4',
    training: '#84CC16',
    lecture: '#6366F1',
    product_launch: '#EC4899',
    business_dinner: '#F97316',
    workshop: '#14B8A6',
    other: '#6B7280',
  };
  return colors[eventType] || '#6B7280';
}

function getEventTypeDisplayName(eventType: string): string {
  const displayNames: Record<string, string> = {
    business_meeting: '商务会议',
    industry_conference: '行业会议',
    exhibition: '展会',
    seminar: '研讨会',
    networking: '社交活动',
    client_visit: '客户拜访',
    training: '培训',
    lecture: '讲座',
    product_launch: '产品发布',
    business_dinner: '商务晚宴',
    workshop: '工作坊',
    other: '其他',
  };
  return displayNames[eventType] || eventType;
}

export default EventStatsDashboard;
