import React from 'react';

import {
  Box,
  Text,
  VStack,
  HStack,
  Pressable,
  Icon,
  Heading,
  Image,
  Button,
  ButtonText
} from '@gluestack-ui/themed';
import { ChevronRight } from 'lucide-react-native';
import { Connection } from '../hooks/useContactDetail'; // Assuming Connection type is exported
import ConnectionItem from './ConnectionItem';

interface NetworkTabProps {
  connections: Connection[];
  contactName: string;
  onViewConnection: (id: string, name: string) => void;
  onViewNetworkFromContact: () => void;
}

const NetworkTab: React.FC<NetworkTabProps> = ({ 
  connections, 
  contactName, 
  onViewConnection, 
  onViewNetworkFromContact 
}) => {
  return (
    <VStack space="lg" p={'$4'}>
      <Heading size="md" mb={'$2'}>Connections & Network</Heading>
      
      {connections.length > 0 ? (
        connections.map((connection: Connection) => (
          <ConnectionItem 
            key={connection.id} 
            connection={connection} 
            onPress={() => onViewConnection(connection.id, connection.name)} 
          />
        ))
      ) : (
        <Text textAlign='center' color={'$textLight500'} mt={'$5'}>No connections linked yet.</Text>
      )}
      
      <Box bg={'$backgroundLight0'} borderRadius={'$xl'} p={'$4'} mt={'$4'}>
        <Pressable onPress={onViewNetworkFromContact} h={180} borderRadius={'$lg'} overflow="hidden">
          <Image 
            source={{ uri: 'https://api.a0.dev/assets/image?width=600&height=300&seed=networkgraph&text=Network+Graph+Preview&color=ffffff&bgcolor=primary500' }} 
            alt="Network Graph Preview"
            w="$full"
            h="$full"
            resizeMode='cover'
          />
          <Box 
            position="absolute" 
            top={0} 
            left={0} 
            right={0} 
            bottom={0} 
            bg="rgba(0, 0, 0, 0.4)" 
            justifyContent="center" 
            alignItems="center" 
            p={'$4'}
          >
            <Text color={'$textLight0'} textAlign="center" mb={'$3'} fontSize={'$md'} fontWeight={'$medium'}>
              View {contactName}&apos;s position in your network
            </Text>
            <Button action="primary" variant="solid" size="sm" borderRadius="$full">
              <ButtonText fontWeight={'$medium'}>Explore Network</ButtonText>
            </Button>
          </Box>
        </Pressable>
      </Box>
    </VStack>
  );
};

export default NetworkTab;
