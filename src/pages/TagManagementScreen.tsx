import React, { useState, useEffect, useMemo } from 'react';
import { Platform } from 'react-native';
import {
  Box,
  Text,
  Heading,
  VStack,
  HStack,
  Button,
  ButtonText,
  ButtonIcon,
  Pressable,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  ScrollView,
  FlatList,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
  Badge,
  BadgeText,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  AlertDialog,
  AlertDialogBackdrop,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
} from '@gluestack-ui/themed';
import { useNavigation } from '@react-navigation/native';
import {
  Search,
  Plus,
  X,
  Check,
  Tag as TagIcon,
  ArrowLeft,
  Trash2,
  Edit,
  Filter,
  Hash,
  Users,
  Palette,
} from 'lucide-react-native';

// Import global state and hooks
import { useContactsStore, useContactsSelectors } from '../store';
import { usePerformanceMonitor, useNetworkError } from '../hooks';
import { ErrorBoundary, StandardButton, StandardIcon, CircularButton } from '../components/ui';

// Types and interfaces
type TagManagementScreenNavigationProp = any; // TODO: 添加正确的导航类型

interface TagItem {
  id: string;
  name: string;
  color: string;
  category: string;
  count: number;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface TagCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
}

interface TagStats {
  totalTags: number;
  totalUsage: number;
  categoryCounts: Record<string, number>;
  mostUsedTag: TagItem | null;
  recentlyCreated: TagItem[];
}

// Tag categories configuration
const TAG_CATEGORIES: TagCategory[] = [
  {
    id: 'industry',
    name: '行业',
    description: '按行业分类的标签',
    icon: Hash,
    color: '#3B82F6',
  },
  {
    id: 'relationship',
    name: '关系',
    description: '关系类型标签',
    icon: Users,
    color: '#10B981',
  },
  {
    id: 'personal',
    name: '个人',
    description: '个人兴趣和特征标签',
    icon: TagIcon,
    color: '#F59E0B',
  },
  {
    id: 'custom',
    name: '自定义',
    description: '用户自定义标签',
    icon: Palette,
    color: '#8B5CF6',
  },
];

// Available colors for tags
const TAG_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Orange
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6366F1', // Indigo
  '#F97316', // Orange-600
  '#14B8A6', // Teal
  '#A855F7', // Violet
  '#22C55E', // Green-500
  '#F43F5E', // Rose
  '#6B7280', // Gray
];

/**
 * 标签管理屏幕 - 完全重构版本
 * 使用Gluestack UI和全局状态管理
 */
const TagManagementScreen: React.FC = () => {
  const navigation = useNavigation<TagManagementScreenNavigationProp>();
  const toast = useToast();

  // Performance monitoring
  usePerformanceMonitor('TagManagementScreen');

  // Global state
  const { contacts, tags, addTag, updateTag, deleteTag } = useContactsStore();
  const { showError, clearError } = useNetworkError();

  // Local state
  const [activeCategory, setActiveCategory] = useState<string>('industry');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showTagModal, setShowTagModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editingTag, setEditingTag] = useState<TagItem | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: TAG_COLORS[0],
    category: 'industry',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Process tags data from contacts
  const processedTags = useMemo((): TagItem[] => {
    const tagMap = new Map<string, TagItem>();

    // Count tag usage from contacts
    contacts.forEach(contact => {
      contact.tags?.forEach(tagName => {
        const tagKey = tagName.toLowerCase();
        if (tagMap.has(tagKey)) {
          const existingTag = tagMap.get(tagKey)!;
          existingTag.count += 1;
        } else {
          // Create tag item from contact tag
          const category = determineTagCategory(tagName);
          const color = getTagColor(tagName, category);

          tagMap.set(tagKey, {
            id: `tag-${tagKey}`,
            name: tagName,
            color,
            category,
            count: 1,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
      });
    });

    // Add existing tags from store
    tags.forEach(tag => {
      const tagKey = tag.name.toLowerCase();
      if (!tagMap.has(tagKey)) {
        tagMap.set(tagKey, tag);
      }
    });

    return Array.from(tagMap.values());
  }, [contacts, tags]);

  // Filter tags based on category and search
  const filteredTags = useMemo(() => {
    let filtered = processedTags;

    // Category filter
    if (activeCategory !== 'all') {
      filtered = filtered.filter(tag => tag.category === activeCategory);
    }

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(tag =>
        tag.name.toLowerCase().includes(query) ||
        tag.description?.toLowerCase().includes(query)
      );
    }

    return filtered.sort((a, b) => b.count - a.count);
  }, [processedTags, activeCategory, searchQuery]);

  // Calculate tag statistics
  const tagStats = useMemo((): TagStats => {
    const categoryCounts: Record<string, number> = {};
    let totalUsage = 0;
    let mostUsedTag: TagItem | null = null;

    processedTags.forEach(tag => {
      categoryCounts[tag.category] = (categoryCounts[tag.category] || 0) + 1;
      totalUsage += tag.count;

      if (!mostUsedTag || tag.count > mostUsedTag.count) {
        mostUsedTag = tag;
      }
    });

    const recentlyCreated = processedTags
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);

    return {
      totalTags: processedTags.length,
      totalUsage,
      categoryCounts,
      mostUsedTag,
      recentlyCreated,
    };
  }, [processedTags]);

  // Utility functions
  const determineTagCategory = (tagName: string): string => {
    const name = tagName.toLowerCase();

    // Industry keywords
    if (['tech', 'technology', 'finance', 'healthcare', 'education', 'manufacturing'].some(keyword =>
      name.includes(keyword))) {
      return 'industry';
    }

    // Relationship keywords
    if (['client', 'partner', 'vendor', 'colleague', 'prospect', 'friend'].some(keyword =>
      name.includes(keyword))) {
      return 'relationship';
    }

    // Personal keywords
    if (['golf', 'coffee', 'travel', 'art', 'wine', 'music', 'sports'].some(keyword =>
      name.includes(keyword))) {
      return 'personal';
    }

    return 'custom';
  };

  const getTagColor = (tagName: string, category: string): string => {
    const categoryColors = {
      industry: '#3B82F6',
      relationship: '#10B981',
      personal: '#F59E0B',
      custom: '#8B5CF6',
    };

    // Use category color as base, but add some variation
    const baseColor = categoryColors[category as keyof typeof categoryColors] || '#6B7280';
    const hash = tagName.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);

    const colorIndex = Math.abs(hash) % TAG_COLORS.length;
    return TAG_COLORS[colorIndex];
  };

  const getCategoryInfo = (categoryId: string): TagCategory => {
    return TAG_CATEGORIES.find(cat => cat.id === categoryId) || TAG_CATEGORIES[0];
  };

  // Event handlers
  const handleTagPress = (tag: TagItem) => {
    setEditingTag(tag);
    setFormData({
      name: tag.name,
      description: tag.description || '',
      color: tag.color,
      category: tag.category,
    });
    setShowTagModal(true);
  };

  const handleViewContacts = (tag: TagItem) => {
    // Navigate to contacts screen with tag filter
    navigation.navigate('Contacts', { tagFilter: tag.name });
  };

  const openNewTagModal = () => {
    setEditingTag(null);
    setFormData({
      name: '',
      description: '',
      color: TAG_COLORS[0],
      category: activeCategory,
    });
    setErrors({});
    setShowTagModal(true);
  };

  const updateFormData = (updates: Partial<typeof formData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    // Clear related errors
    Object.keys(updates).forEach(key => {
      if (errors[key]) {
        setErrors(prev => ({ ...prev, [key]: undefined }));
      }
    });
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = '标签名称不能为空';
    } else if (formData.name.length < 2) {
      newErrors.name = '标签名称至少需要2个字符';
    } else if (formData.name.length > 20) {
      newErrors.name = '标签名称不能超过20个字符';
    }

    // Check for duplicate names (excluding current editing tag)
    const existingTag = processedTags.find(tag =>
      tag.name.toLowerCase() === formData.name.toLowerCase() &&
      tag.id !== editingTag?.id
    );
    if (existingTag) {
      newErrors.name = '该标签名称已存在';
    }

    if (formData.description && formData.description.length > 100) {
      newErrors.description = '描述不能超过100个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveTag = async () => {
    if (!validateForm()) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>表单验证失败</ToastTitle>
            <ToastDescription>请检查并修正表单中的错误</ToastDescription>
          </Toast>
        ),
      });
      return;
    }

    try {
      clearError();

      if (editingTag) {
        // Update existing tag
        const updatedTag: TagItem = {
          ...editingTag,
          name: formData.name.trim(),
          description: formData.description.trim(),
          color: formData.color,
          category: formData.category,
          updatedAt: new Date(),
        };

        updateTag(updatedTag);

        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="success" variant="accent">
              <ToastTitle>标签更新成功</ToastTitle>
              <ToastDescription>标签信息已成功更新</ToastDescription>
            </Toast>
          ),
        });
      } else {
        // Create new tag
        const newTag: TagItem = {
          id: `tag-${Date.now()}`,
          name: formData.name.trim(),
          description: formData.description.trim(),
          color: formData.color,
          category: formData.category,
          count: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        addTag(newTag);

        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="success" variant="accent">
              <ToastTitle>标签创建成功</ToastTitle>
              <ToastDescription>新标签已成功创建</ToastDescription>
            </Toast>
          ),
        });
      }

      setShowTagModal(false);
    } catch (error) {
      console.error('Error saving tag:', error);
      showError({
        message: '保存标签时出错，请重试',
        code: 'SAVE_ERROR'
      });
    }
  };

  const handleDeleteTag = async () => {
    if (!editingTag) return;

    try {
      clearError();

      deleteTag(editingTag.id);

      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>标签删除成功</ToastTitle>
            <ToastDescription>标签已从所有联系人中移除</ToastDescription>
          </Toast>
        ),
      });

      setShowTagModal(false);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting tag:', error);
      showError({
        message: '删除标签时出错，请重试',
        code: 'DELETE_ERROR'
      });
    }
  };

  const confirmDeleteTag = () => {
    setShowDeleteDialog(true);
  };

  // Render tag item
  const renderTag = ({ item }: { item: TagItem }) => (
    <TouchableOpacity
      style={[styles.tag, { borderColor: item.color }]}
      onPress={() => handleTagPress(item)}
    >
      <View style={[styles.tagDot, { backgroundColor: item.color }]} />
      <Text style={styles.tagName}>{item.name}</Text>
      <View style={styles.tagCountContainer}>
        <Text style={styles.tagCount}>{item.count}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tag Management</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Search size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search tags..."
          placeholderTextColor="#999"
          value={searchQuery}
          onChangeText={setSearchQuery}
          clearButtonMode="while-editing"
        />
      </View>

      {/* Category Tabs */}
      {searchQuery.trim() === '' && (
        <View style={styles.categoryTabsContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.categoryTabs}>
            {Object.keys(tags).map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryTab,
                  activeCategory === category && styles.activeCategoryTab
                ]}
                onPress={() => setActiveCategory(category)}
              >
                <Text style={[
                  styles.categoryTabText,
                  activeCategory === category && styles.activeCategoryTabText
                ]}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </Text>
                {activeCategory === category && <View style={styles.categoryTabIndicator} />}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Tag List */}
      <FlatList
        data={filteredTags}
        renderItem={renderTag}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.tagListContainer}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={() => searchQuery.trim() !== '' ? (
          <Text style={styles.searchResultsText}>
            {filteredTags.length} tags found for &quot;{searchQuery}&quot;
          </Text>
        ) : null}
        ListEmptyComponent={() => (
          <View style={styles.emptyState}>
            <TagIcon size={60} color="#ccc" />
            <Text style={styles.emptyStateText}>
              {searchQuery.trim() !== '' ? 'No tags found matching your search' : 'No tags in this category yet'}
            </Text>
            <TouchableOpacity style={styles.emptyStateButton} onPress={openNewTagModal}>
              <Text style={styles.emptyStateButtonText}>Create a tag</Text>
            </TouchableOpacity>
          </View>
        )}
      />

      {/* Add Tag Button */}
      <TouchableOpacity style={styles.addButton} onPress={openNewTagModal}>
        <LinearGradient
          colors={['#4A78D9', '#2F5FE3']}
          style={styles.addButtonGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Plus size={28} color="#fff" />
        </LinearGradient>
      </TouchableOpacity>

      {/* Tag Creation/Editing Modal */}
      <Modal
        visible={tagModalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setTagModalVisible(false)}
      >
        <TouchableWithoutFeedback onPress={() => setTagModalVisible(false)}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
              <View style={styles.modalContainer}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>
                    {editingTag ? 'Edit Tag' : 'Create New Tag'}
                  </Text>
                  <TouchableOpacity onPress={() => setTagModalVisible(false)}>
                    <X size={24} color="#555" />
                  </TouchableOpacity>
                </View>

                <View style={styles.modalContent}>
                  <View style={styles.modalInputContainer}>
                    <Text style={styles.modalInputLabel}>Tag Name</Text>
                    <TextInput
                      style={styles.modalInput}
                      placeholder="Enter tag name"
                      placeholderTextColor="#999"
                      value={newTagName}
                      onChangeText={setNewTagName}
                      autoFocus
                    />
                  </View>

                  <Text style={styles.modalInputLabel}>Tag Color</Text>
                  <View style={styles.colorPickerContainer}>
                    {TAG_COLORS.map(color => (
                      <TouchableOpacity
                        key={color}
                        style={[
                          styles.colorOption,
                          { backgroundColor: color },
                          selectedColor === color && styles.selectedColorOption
                        ]}
                        onPress={() => setSelectedColor(color)}
                      >
                        {selectedColor === color && (
                          <Check size={18} color="#fff" />
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>

                  <Text style={styles.modalInputLabel}>Tag Category</Text>
                  <View style={styles.categoryPickerContainer}>
                    {Object.keys(tags).map(category => (
                      <TouchableOpacity
                        key={category}
                        style={[
                          styles.categoryOption,
                          selectedCategory === category && { backgroundColor: selectedColor }
                        ]}
                        onPress={() => setSelectedCategory(category)}
                      >
                        <Text style={[
                          styles.categoryOptionText,
                          selectedCategory === category && { color: '#fff' }
                        ]}>
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                <View style={styles.modalActions}>
                  {editingTag && (
                    <TouchableOpacity style={styles.deleteButton} onPress={deleteTag}>
                      <Trash2 size={20} color="#e74c3c" />
                      <Text style={styles.deleteButtonText}>Delete</Text>
                    </TouchableOpacity>
                  )}

                  <TouchableOpacity
                    style={[styles.saveTagButton, { backgroundColor: selectedColor }]}
                    onPress={saveTag}
                  >
                    <Text style={styles.saveTagButtonText}>
                      {editingTag ? 'Update Tag' : 'Create Tag'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eaeaea',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  categoryTabsContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
  },
  categoryTabs: {
    paddingHorizontal: 16,
  },
  categoryTab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
    position: 'relative',
  },
  activeCategoryTab: {},
  categoryTabText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#777',
  },
  activeCategoryTabText: {
    color: '#4A78D9',
  },
  categoryTabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 16,
    right: 16,
    height: 3,
    backgroundColor: '#4A78D9',
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  tagListContainer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 80,
  },
  searchResultsText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    fontStyle: 'italic',
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.05)',
    } : {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
    }),
    elevation: 1,
  },
  tagDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 12,
  },
  tagName: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  tagCountContainer: {
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    paddingVertical: 2,
    paddingHorizontal: 8,
  },
  tagCount: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#777',
    marginTop: 12,
    marginBottom: 24,
    textAlign: 'center',
  },
  emptyStateButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#4A78D9',
    borderRadius: 8,
  },
  emptyStateButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#fff',
  },
  addButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.2)',
    } : {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 5,
    }),
    elevation: 5,
  },
  addButtonGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#eaeaea',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  modalContent: {
    padding: 16,
  },
  modalInputContainer: {
    marginBottom: 16,
  },
  modalInputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
    marginBottom: 8,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  colorPickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  colorOption: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
    marginBottom: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedColorOption: {
    borderWidth: 2,
    borderColor: '#fff',
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 0px 3px 0px rgba(0, 0, 0, 0.2)',
    } : {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.2,
      shadowRadius: 3,
    }),
    elevation: 3,
  },
  categoryPickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  categoryOption: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    marginRight: 8,
    marginBottom: 8,
  },
  categoryOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#eaeaea',
    padding: 16,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButtonText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#e74c3c',
    marginLeft: 4,
  },
  saveTagButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: '#4A78D9',
  },
  saveTagButtonText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#fff',
  },
});

export default TagManagementScreen;