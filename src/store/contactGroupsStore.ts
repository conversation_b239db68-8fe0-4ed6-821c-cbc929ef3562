import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ContactGroup } from '../types';

interface ContactGroupsState {
  groups: ContactGroup[];
  selectedGroupId: string | null;
  isLoading: boolean;
  error: string | null;
}

interface ContactGroupsActions {
  // 分组管理
  addGroup: (group: Omit<ContactGroup, 'id' | 'contactCount' | 'createdAt' | 'updatedAt'>) => void;
  updateGroup: (id: string, updates: Partial<ContactGroup>) => void;
  deleteGroup: (id: string) => void;
  
  // 分组选择
  setSelectedGroup: (groupId: string | null) => void;
  
  // 分组统计
  updateGroupContactCount: (groupId: string, count: number) => void;
  incrementGroupContactCount: (groupId: string) => void;
  decrementGroupContactCount: (groupId: string) => void;
  
  // 工具方法
  getGroupById: (id: string) => ContactGroup | undefined;
  getDefaultGroups: () => ContactGroup[];
  getCustomGroups: () => ContactGroup[];
  
  // 状态管理
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // 初始化
  initializeDefaultGroups: () => void;
}

type ContactGroupsStore = ContactGroupsState & ContactGroupsActions;

// 默认分组配置
const DEFAULT_GROUPS: Omit<ContactGroup, 'id' | 'contactCount' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: '全部联系人',
    description: '所有联系人',
    color: '#6B7280',
    icon: 'users',
    isDefault: true,
  },
  {
    name: '工作',
    description: '工作相关联系人',
    color: '#3B82F6',
    icon: 'briefcase',
    isDefault: true,
  },
  {
    name: '朋友',
    description: '朋友和社交联系人',
    color: '#10B981',
    icon: 'heart',
    isDefault: true,
  },
  {
    name: '家人',
    description: '家庭成员',
    color: '#F59E0B',
    icon: 'home',
    isDefault: true,
  },
  {
    name: '客户',
    description: '客户和商业伙伴',
    color: '#8B5CF6',
    icon: 'star',
    isDefault: true,
  },
  {
    name: '收藏',
    description: '收藏的联系人',
    color: '#EF4444',
    icon: 'bookmark',
    isDefault: true,
  },
];

export const useContactGroupsStore = create<ContactGroupsStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      groups: [],
      selectedGroupId: null,
      isLoading: false,
      error: null,

      // 分组管理
      addGroup: (groupData) => {
        const newGroup: ContactGroup = {
          ...groupData,
          id: Date.now().toString(),
          contactCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          groups: [...state.groups, newGroup],
        }));
      },

      updateGroup: (id, updates) => {
        set((state) => ({
          groups: state.groups.map((group) =>
            group.id === id
              ? { ...group, ...updates, updatedAt: new Date() }
              : group
          ),
        }));
      },

      deleteGroup: (id) => {
        const group = get().getGroupById(id);
        if (group?.isDefault) {
          set({ error: '无法删除默认分组' });
          return;
        }

        set((state) => ({
          groups: state.groups.filter((group) => group.id !== id),
          selectedGroupId: state.selectedGroupId === id ? null : state.selectedGroupId,
        }));
      },

      // 分组选择
      setSelectedGroup: (groupId) => {
        set({ selectedGroupId: groupId });
      },

      // 分组统计
      updateGroupContactCount: (groupId, count) => {
        set((state) => ({
          groups: state.groups.map((group) =>
            group.id === groupId
              ? { ...group, contactCount: count, updatedAt: new Date() }
              : group
          ),
        }));
      },

      incrementGroupContactCount: (groupId) => {
        set((state) => ({
          groups: state.groups.map((group) =>
            group.id === groupId
              ? { ...group, contactCount: group.contactCount + 1, updatedAt: new Date() }
              : group
          ),
        }));
      },

      decrementGroupContactCount: (groupId) => {
        set((state) => ({
          groups: state.groups.map((group) =>
            group.id === groupId
              ? { ...group, contactCount: Math.max(0, group.contactCount - 1), updatedAt: new Date() }
              : group
          ),
        }));
      },

      // 工具方法
      getGroupById: (id) => {
        return get().groups.find((group) => group.id === id);
      },

      getDefaultGroups: () => {
        return get().groups.filter((group) => group.isDefault);
      },

      getCustomGroups: () => {
        return get().groups.filter((group) => !group.isDefault);
      },

      // 状态管理
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),

      // 初始化默认分组
      initializeDefaultGroups: () => {
        const currentGroups = get().groups;
        const existingDefaultGroups = currentGroups.filter(g => g.isDefault);
        
        // 只添加不存在的默认分组
        const newDefaultGroups = DEFAULT_GROUPS.filter(
          defaultGroup => !existingDefaultGroups.some(existing => existing.name === defaultGroup.name)
        ).map(groupData => ({
          ...groupData,
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          contactCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        }));

        if (newDefaultGroups.length > 0) {
          set((state) => ({
            groups: [...state.groups, ...newDefaultGroups],
          }));
        }
      },
    }),
    {
      name: 'contact-groups-store',
      version: 1,
    }
  )
);

// 选择器
export const useContactGroupsSelectors = {
  useGroups: () => useContactGroupsStore((state) => state.groups),
  useSelectedGroupId: () => useContactGroupsStore((state) => state.selectedGroupId),
  useDefaultGroups: () => useContactGroupsStore((state) => state.getDefaultGroups()),
  useCustomGroups: () => useContactGroupsStore((state) => state.getCustomGroups()),
  useIsLoading: () => useContactGroupsStore((state) => state.isLoading),
  useError: () => useContactGroupsStore((state) => state.error),
};
