import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import {
  Text
} from '@gluestack-ui/themed';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { XCircle, Calendar } from 'lucide-react-native';
import { useAlert } from '../../../context/AlertContext';

// For a more sophisticated date picker, you might use @react-native-community/datetimepicker
// For priority, a simple segmented control or custom buttons could be used.

const { width, height } = Dimensions.get('window');

export interface ReminderFormData {
  title: string;
  dueDate?: string; // Temporarily string, ideally Date object and use a date picker
  priority?: 'high' | 'medium' | 'low'; // Temporarily string, ideally a selection
}

interface ReminderAddModalProps {
  visible: boolean;
  onSave: (reminderData: ReminderFormData) => void;
  onClose: () => void;
}

const ReminderAddModal: React.FC<ReminderAddModalProps> = ({ visible, onSave, onClose }) => {
  const { showAlert } = useAlert();
  const [title, setTitle] = useState('');
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [priority, setPriority] = useState<'high' | 'medium' | 'low'>('medium');

  useEffect(() => {
    // Reset form when modal becomes visible
    if (visible) {
      setTitle('');
      setDate(undefined);
      setShowDatePicker(false);
      setPriority('medium');
    }
  }, [visible]);

  const handleSave = () => {
    if (!title.trim()) {
      showAlert({
        title: 'Missing Title',
        message: 'Please enter a title for the reminder.',
        buttons: [{ text: 'OK', onPress: () => {} }],
      });
      return;
    }
    const dueDateString = date ? date.toISOString().split('T')[0] : undefined;
    onSave({ title, dueDate: dueDateString, priority });
  };

  const onChangeDate = (event: DateTimePickerEvent, selectedDate?: Date) => {
    const currentDate = selectedDate || date;
    setShowDatePicker(Platform.OS === 'ios');
    if (currentDate) {
      setDate(currentDate);
    }
  };

  const showDatepicker = () => {
    setShowDatePicker(true);
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.centeredView}
      >
        <View style={styles.modalOverlay} />
        <View style={styles.modalView}>
          <View style={styles.header}>
            <Text style={styles.modalTitle}>Add Reminder</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <XCircle size={28} color={'#999999' /* TODO: Replace with Gluestack UI token */} />
            </TouchableOpacity>
          </View>

          <Text style={styles.label}>Title</Text>
          <TextInput
            style={styles.textInput}
            onChangeText={setTitle}
            value={title}
            placeholder="e.g., Follow up call"
            placeholderTextColor={'#999999' /* TODO: Replace with Gluestack UI token */}
          />

          <Text style={styles.label}>Due Date (Optional)</Text>
          <TouchableOpacity onPress={showDatepicker} style={styles.datePickerButton}>
            <Text style={styles.datePickerButtonText}>
              {date ? date.toLocaleDateString() : 'Select a date'}
            </Text>
            <Calendar size={22} color={'#007AFF' /* TODO: Replace with Gluestack UI token */} />
          </TouchableOpacity>

          {showDatePicker && (
            <DateTimePicker
              testID="dateTimePicker"
              value={date || new Date()} 
              mode="date"
              is24Hour={true}
              display="default"
              onChange={onChangeDate}
            />
          )}
          
          <Text style={styles.label}>Priority (Optional)</Text>
          {/* Basic Priority Selection - can be replaced with custom buttons or a picker */}
          <View style={styles.priorityContainer}>
            {(['low', 'medium', 'high'] as const).map(p => (
              <TouchableOpacity 
                key={p} 
                style={[
                  styles.priorityButton,
                  priority === p && styles.priorityButtonSelected
                ]} 
                onPress={() => setPriority(p)}
              >
                <Text 
                  style={[
                    styles.priorityButtonText,
                    priority === p && styles.priorityButtonTextSelected
                  ]}
                >
                  {p.charAt(0).toUpperCase() + p.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.actionsContainer}>
            <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={onClose}>
              <Text style={[styles.buttonText, styles.cancelButtonText]}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.button, styles.saveButton]} onPress={handleSave}>
              <Text style={[styles.buttonText, styles.saveButtonText]}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)' /* TODO: Replace with Gluestack UI token */,
  },
  modalView: {
    width: width * 0.9,
    backgroundColor: '#FFFFFF' /* TODO: Replace with Gluestack UI token */,
    borderRadius: 12,
    padding: 20,
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
    } : {
      shadowColor: '#000000' /* TODO: Replace with Gluestack UI token */,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: Platform.OS === 'android' ? 5 : 0,
    }),
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333' /* TODO: Replace with Gluestack UI token */,
  },
  closeButton: {
    padding: 5,
  },
  label: {
    fontSize: 14,
    color: '#666666' /* TODO: Replace with Gluestack UI token */,
    marginBottom: 6,
    marginLeft: 2, // Align with text input padding
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0' /* TODO: Replace with Gluestack UI token */,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#333333' /* TODO: Replace with Gluestack UI token */,
    backgroundColor: '#F5F5F5' /* TODO: Replace with Gluestack UI token */,
    marginBottom: 15,
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0' /* TODO: Replace with Gluestack UI token */,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#F5F5F5' /* TODO: Replace with Gluestack UI token */,
    marginBottom: 15,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: '#333333' /* TODO: Replace with Gluestack UI token */,
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  priorityButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0' /* TODO: Replace with Gluestack UI token */,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  priorityButtonSelected: {
    backgroundColor: '#007AFF' /* TODO: Replace with Gluestack UI token */,
    borderColor: '#007AFF' /* TODO: Replace with Gluestack UI token */,
  },
  priorityButtonText: {
    fontSize: 14,
    color: '#007AFF' /* TODO: Replace with Gluestack UI token */,
  },
  priorityButtonTextSelected: {
    color: '#FFFFFF' /* TODO: Replace with Gluestack UI token */,
    fontWeight: '500',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 15,
  },
  button: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginLeft: 10,
    minWidth: 100,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#C7C7CC' /* TODO: Replace with Gluestack UI token */,
  },
  saveButton: {
    backgroundColor: '#007AFF' /* TODO: Replace with Gluestack UI token */,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  cancelButtonText: {
    color: '#666666' /* TODO: Replace with Gluestack UI token */,
  },
  saveButtonText: {
    color: '#FFFFFF' /* TODO: Replace with Gluestack UI token */,
  },
});

export default ReminderAddModal;
