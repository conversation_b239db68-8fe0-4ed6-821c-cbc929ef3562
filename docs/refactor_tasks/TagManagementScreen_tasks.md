# TagManagementScreen Refactor Tasks

- **重构状态:** ToDo
- **主要区域/视图:**
    - **1. 顶部导航/操作区 (Header)**
        - **描述:** 包含返回按钮、屏幕标题 (\"Manage Tags\")、可能还有 \"Add Tag\" 按钮（如果主列表区域没有）。
        - **现有主要组件:** `View`, `TouchableOpacity`, `Ionicons`, `Text`. `LinearGradient` for header background.
        - **Gluestack UI 迁移建议:**
            - `View` (header container) -> `HStack` or `Box` with `justifyContent=\"space-between\"`, `alignItems=\"center\"`.
            - `TouchableOpacity` (back, add) -> `Pressable` or `Button` (icon only or text).
            - `Ionicons` -> `lucide-react-native` icons (e.g., `ChevronLeft`, `Plus`).
            - `Text` (screen title) -> `Heading`.
            - `LinearGradient`: Header背景优先使用主题色。如果渐变是设计必需，尝试将 `HStack`/`Box` 包裹在 `LinearGradient` 组件内，或者通过 `sx` 属性配置（如果 `gluestack-ui` 支持）。
        - **设计规范参考:** 页面顶部标题与内容间距, 主色.

    - **2. 搜索和筛选区 (Search and Filter Area)**
        - **描述:** 搜索框用于按名称搜索标签，可能还有按类别筛选的下拉菜单或按钮组。
        - **现有主要组件:** `View`, `TextInput` (search), `Picker` or custom buttons (filter).
        - **Gluestack UI 迁移建议:**
            - `View` (container) -> `HStack` or `VStack` with `p=\"$3\"`.
            - `TextInput` (search) -> `Input` with `InputField`, possibly with a leading `InputSlot` and `InputIcon` (e.g., `Search` icon).
            - `Picker` (filter by category) -> `Select` component with `SelectTrigger`, `SelectInput`, `SelectIcon`, `SelectPortal`, `SelectBackdrop`, `SelectContent`, `SelectDragIndicatorWrapper`, `SelectDragIndicator`, `SelectItem`.
            - Filter buttons: `ButtonGroup` with `Button`s.
        - **设计规范参考:** 输入框样式, 选择器样式, 按钮组.

    - **3. 标签列表区 (Tag List Area)**
        - **描述:** 按类别分组显示标签列表。每个标签项可能显示标签名称、颜色标记，并有编辑/删除操作。
        - **现有主要组件:** `SectionList` or `FlatList` (grouped manually), `View` (category header, tag item), `Text` (category name, tag name), `TouchableOpacity` (edit/delete tag).
        - **Gluestack UI 迁移建议:**
            - `SectionList` -> `SectionList`.
                - `renderSectionHeader`: `Box` or `Heading` (`size=\"sm\"`, `mt=\"$3\"`, `mb=\"$1\"`).
                - `renderItem`:
                    - `View` (tag item) -> `Pressable` or `HStack` with `justifyContent=\"space-between\"`, `alignItems=\"center\"`, `p=\"$3\"`, `borderBottomWidth=\"$1\"`, `borderColor=\"$borderLight300\"`.
                    - Color indicator: `Box` with `bg` set to tag color, `w=\"$2\"`, `h=\"$2\"`, `borderRadius=\"$full\"`, `mr=\"$2\"`.
                    - `Text` (tag name) -> `Text`.
                    - Action buttons (edit/delete): `HStack` with `Button` (iconOnly, `variant=\"link\"`, e.g., `Edit3`, `Trash2` icons).
            - If using `FlatList` with manual grouping, group headers would be regular items.
        - **设计规范参考:** 列表项样式, 文本, 图标按钮, 分割线.

    - **4. \"添加标签\" 浮动按钮 (FAB - Floating Action Button for Add Tag)**
        - **描述:** 如果 \"Add Tag\" 功能是通过屏幕右下角的浮动按钮触发。
        - **现有主要组件:** `TouchableOpacity`, `Ionicons`.
        - **Gluestack UI 迁移建议:**
            - `TouchableOpacity` -> `Fab` component from `gluestack-ui` (if available and suitable) or a `Button` styled as FAB (circular, shadow, absolute positioning).
                - `Fab.Icon` or `Button.Icon` with `lucide-react-native` `Plus` icon.
        - **设计规范参考:** 圆形按钮尺寸, 主色, 阴影.

    - **5. 添加/编辑标签模态框 (Add/Edit Tag Modal)**
        - **描述:** 用于创建新标签或编辑现有标签的表单，包含名称输入、颜色选择、类别选择。
        - **现有主要组件:** `Modal`, `View` (modal content), `TextInput` (name), custom color picker, `Picker` (category).
        - **Gluestack UI 迁移建议:**
            - `Modal` -> `Modal` component.
                - `Modal.Backdrop`, `Modal.Content`, `Modal.Header` (with `Heading`), `Modal.Body`, `Modal.Footer`.
            - `TextInput` (name) -> `Input` with `InputField`.
            - Color picker:
                - Simple: `HStack` of `Pressable` `Box` components, each with a different `bg` color.
                - Advanced: Retain custom color picker or find a compatible third-party one. Wrap with `gluestack-ui` for surrounding layout.
            - `Picker` (category) -> `Select` component.
            - Save/Cancel buttons: `ButtonGroup` with `Button`s in `Modal.Footer`.
        - **设计规范参考:** 模态框样式, 输入框, 选择器, 按钮.

- **整体注意事项:**
    - **图标替换:** All `Ionicons`, `FontAwesome` to `lucide-react-native`.
    - **颜色和样式:** Migrate `StyleSheet` to `gluestack-ui` props/`sx`, using theme tokens.
    - **`LinearGradient`:** Similar to other screens, prioritize theme colors. If gradients are essential for backgrounds (e.g., header), wrap `gluestack-ui` layout components or explore `sx` prop capabilities.
    - **State Management:** State for search text, selected filters, tags list, modal visibility, and form data will be preserved.
    - **CRUD Logic:** Tag creation, update, deletion logic remains.
