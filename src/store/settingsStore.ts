import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SettingsStore, SettingsState } from './types';

// Default settings state
const defaultSettings: SettingsState = {
  // Notifications
  notificationsEnabled: true,
  emailNotifications: true,
  reminderNotifications: true,
  networkUpdates: true,
  
  // App Settings
  dataSync: true,
  darkMode: false,
  aiSuggestions: true,
  autoTagging: true,
  
  // Security
  biometricLogin: false,
  locationTracking: false,
  
  // Language & Locale
  language: 'en',
  
  // User Profile
  userProfile: {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://api.a0.dev/assets/image?text=AM&aspect=1:1',
    plan: 'Professional',
  },
};

/**
 * Settings Store
 * 管理应用设置状态，支持持久化存储
 */
export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      ...defaultSettings,
      
      // Actions
      updateSetting: <K extends keyof SettingsState>(key: K, value: SettingsState[K]) => {
        set((state) => ({
          ...state,
          [key]: value,
        }));
      },
      
      resetSettings: () => {
        set(defaultSettings);
      },
      
      updateUserProfile: (profile) => {
        set((state) => ({
          ...state,
          userProfile: {
            ...state.userProfile,
            ...profile,
          },
        }));
      },
      
      toggleNotifications: (enabled) => {
        set((state) => ({
          ...state,
          notificationsEnabled: enabled,
          // 如果关闭总开关，同时关闭所有子选项
          emailNotifications: enabled ? state.emailNotifications : false,
          reminderNotifications: enabled ? state.reminderNotifications : false,
          networkUpdates: enabled ? state.networkUpdates : false,
        }));
      },
      
      setLanguage: (language) => {
        set((state) => ({
          ...state,
          language,
        }));
      },
    }),
    {
      name: 'settings-storage',
      storage: createJSONStorage(() => AsyncStorage),
      // 只持久化设置数据，不持久化actions
      partialize: (state) => {
        const { updateSetting, resetSettings, updateUserProfile, toggleNotifications, setLanguage, ...persistedState } = state;
        return persistedState;
      },
    }
  )
);

// Selectors for better performance
export const useSettingsSelectors = {
  // 通知设置
  useNotificationSettings: () => useSettingsStore((state) => ({
    notificationsEnabled: state.notificationsEnabled,
    emailNotifications: state.emailNotifications,
    reminderNotifications: state.reminderNotifications,
    networkUpdates: state.networkUpdates,
  })),
  
  // 应用设置
  useAppSettings: () => useSettingsStore((state) => ({
    dataSync: state.dataSync,
    darkMode: state.darkMode,
    aiSuggestions: state.aiSuggestions,
    autoTagging: state.autoTagging,
  })),
  
  // 安全设置
  useSecuritySettings: () => useSettingsStore((state) => ({
    biometricLogin: state.biometricLogin,
    locationTracking: state.locationTracking,
  })),
  
  // 用户资料
  useUserProfile: () => useSettingsStore((state) => state.userProfile),
  
  // 语言设置
  useLanguage: () => useSettingsStore((state) => state.language),
  
  // 主题设置
  useDarkMode: () => useSettingsStore((state) => state.darkMode),
};

// Actions selectors
export const useSettingsActions = () => useSettingsStore((state) => ({
  updateSetting: state.updateSetting,
  resetSettings: state.resetSettings,
  updateUserProfile: state.updateUserProfile,
  toggleNotifications: state.toggleNotifications,
  setLanguage: state.setLanguage,
}));
