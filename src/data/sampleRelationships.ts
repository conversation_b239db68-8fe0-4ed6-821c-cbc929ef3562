/**
 * 示例关系数据
 * 用于演示关系图谱功能
 */

import { ContactRelationship } from '../types';

export const sampleRelationships: ContactRelationship[] = [
  // 张三的关系网络
  {
    id: 'rel_001',
    fromContactId: 'user',
    toContactId: 'contact_001', // 张三
    relationshipType: 'colleague',
    strength: 85,
    notes: '同事关系，合作密切',
    isActive: true,
    establishedDate: new Date('2023-01-15'),
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2023-01-15'),
  },
  {
    id: 'rel_002',
    fromContactId: 'contact_001', // 张三
    toContactId: 'contact_002', // 李四
    relationshipType: 'introduced_by',
    strength: 70,
    notes: '张三介绍认识的',
    isActive: true,
    establishedDate: new Date('2023-02-20'),
    createdAt: new Date('2023-02-20'),
    updatedAt: new Date('2023-02-20'),
  },
  {
    id: 'rel_003',
    fromContactId: 'user',
    toContactId: 'contact_002', // 李四
    relationshipType: 'introduced_to',
    strength: 65,
    notes: '通过张三认识',
    isActive: true,
    establishedDate: new Date('2023-02-20'),
    createdAt: new Date('2023-02-20'),
    updatedAt: new Date('2023-02-20'),
  },
  
  // 王五的关系网络
  {
    id: 'rel_004',
    fromContactId: 'user',
    toContactId: 'contact_003', // 王五
    relationshipType: 'business_partner',
    strength: 90,
    notes: '重要的商业伙伴',
    isActive: true,
    establishedDate: new Date('2022-08-10'),
    createdAt: new Date('2022-08-10'),
    updatedAt: new Date('2022-08-10'),
  },
  {
    id: 'rel_005',
    fromContactId: 'contact_003', // 王五
    toContactId: 'contact_004', // 赵六
    relationshipType: 'colleague',
    strength: 75,
    notes: '同公司同事',
    isActive: true,
    establishedDate: new Date('2022-09-01'),
    createdAt: new Date('2022-09-01'),
    updatedAt: new Date('2022-09-01'),
  },
  {
    id: 'rel_006',
    fromContactId: 'user',
    toContactId: 'contact_004', // 赵六
    relationshipType: 'introduced_to',
    strength: 60,
    notes: '通过王五认识',
    isActive: true,
    establishedDate: new Date('2022-10-15'),
    createdAt: new Date('2022-10-15'),
    updatedAt: new Date('2022-10-15'),
  },
  
  // 陈七的关系网络
  {
    id: 'rel_007',
    fromContactId: 'user',
    toContactId: 'contact_005', // 陈七
    relationshipType: 'family_friend',
    strength: 95,
    notes: '家庭朋友，关系很好',
    isActive: true,
    establishedDate: new Date('2020-01-01'),
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2020-01-01'),
  },
  {
    id: 'rel_008',
    fromContactId: 'contact_005', // 陈七
    toContactId: 'contact_006', // 周八
    relationshipType: 'mutual_friend',
    strength: 80,
    notes: '共同朋友',
    isActive: true,
    establishedDate: new Date('2021-05-20'),
    createdAt: new Date('2021-05-20'),
    updatedAt: new Date('2021-05-20'),
  },
  {
    id: 'rel_009',
    fromContactId: 'user',
    toContactId: 'contact_006', // 周八
    relationshipType: 'mutual_friend',
    strength: 70,
    notes: '通过陈七认识的共同朋友',
    isActive: true,
    establishedDate: new Date('2021-06-10'),
    createdAt: new Date('2021-06-10'),
    updatedAt: new Date('2021-06-10'),
  },
  
  // 吴九的关系网络
  {
    id: 'rel_010',
    fromContactId: 'user',
    toContactId: 'contact_007', // 吴九
    relationshipType: 'mentor_mentee',
    strength: 85,
    notes: '我的导师',
    isActive: true,
    establishedDate: new Date('2019-09-01'),
    createdAt: new Date('2019-09-01'),
    updatedAt: new Date('2019-09-01'),
  },
  {
    id: 'rel_011',
    fromContactId: 'contact_007', // 吴九
    toContactId: 'contact_008', // 郑十
    relationshipType: 'colleague',
    strength: 70,
    notes: '同事关系',
    isActive: true,
    establishedDate: new Date('2020-03-15'),
    createdAt: new Date('2020-03-15'),
    updatedAt: new Date('2020-03-15'),
  },
  {
    id: 'rel_012',
    fromContactId: 'user',
    toContactId: 'contact_008', // 郑十
    relationshipType: 'introduced_to',
    strength: 55,
    notes: '通过吴九老师认识',
    isActive: true,
    establishedDate: new Date('2020-04-01'),
    createdAt: new Date('2020-04-01'),
    updatedAt: new Date('2020-04-01'),
  },
  
  // 更多复杂关系
  {
    id: 'rel_013',
    fromContactId: 'contact_001', // 张三
    toContactId: 'contact_005', // 陈七
    relationshipType: 'mutual_friend',
    strength: 65,
    notes: '共同朋友，偶尔聚会',
    isActive: true,
    establishedDate: new Date('2023-03-10'),
    createdAt: new Date('2023-03-10'),
    updatedAt: new Date('2023-03-10'),
  },
  {
    id: 'rel_014',
    fromContactId: 'contact_002', // 李四
    toContactId: 'contact_006', // 周八
    relationshipType: 'colleague',
    strength: 60,
    notes: '在不同公司但有业务往来',
    isActive: true,
    establishedDate: new Date('2023-04-05'),
    createdAt: new Date('2023-04-05'),
    updatedAt: new Date('2023-04-05'),
  },
  {
    id: 'rel_015',
    fromContactId: 'contact_003', // 王五
    toContactId: 'contact_007', // 吴九
    relationshipType: 'business_partner',
    strength: 80,
    notes: '商业合作伙伴',
    isActive: true,
    establishedDate: new Date('2022-11-20'),
    createdAt: new Date('2022-11-20'),
    updatedAt: new Date('2022-11-20'),
  },
  
  // 一些较弱的关系
  {
    id: 'rel_016',
    fromContactId: 'contact_004', // 赵六
    toContactId: 'contact_008', // 郑十
    relationshipType: 'other',
    strength: 40,
    notes: '偶尔联系',
    isActive: true,
    establishedDate: new Date('2023-01-30'),
    createdAt: new Date('2023-01-30'),
    updatedAt: new Date('2023-01-30'),
  },
  {
    id: 'rel_017',
    fromContactId: 'contact_005', // 陈七
    toContactId: 'contact_007', // 吴九
    relationshipType: 'mutual_friend',
    strength: 50,
    notes: '通过我认识的',
    isActive: true,
    establishedDate: new Date('2021-08-15'),
    createdAt: new Date('2021-08-15'),
    updatedAt: new Date('2021-08-15'),
  },
  
  // 一些休眠关系
  {
    id: 'rel_018',
    fromContactId: 'user',
    toContactId: 'contact_009', // 假设有第9个联系人
    relationshipType: 'colleague',
    strength: 30,
    notes: '前同事，现在很少联系',
    isActive: false,
    establishedDate: new Date('2018-06-01'),
    createdAt: new Date('2018-06-01'),
    updatedAt: new Date('2023-01-01'),
  },
  {
    id: 'rel_019',
    fromContactId: 'contact_006', // 周八
    toContactId: 'contact_008', // 郑十
    relationshipType: 'other',
    strength: 35,
    notes: '以前认识，现在不常联系',
    isActive: false,
    establishedDate: new Date('2019-12-10'),
    createdAt: new Date('2019-12-10'),
    updatedAt: new Date('2022-06-01'),
  },
];
