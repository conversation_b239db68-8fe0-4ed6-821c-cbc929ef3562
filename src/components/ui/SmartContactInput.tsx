/**
 * 智能联系人信息输入组件
 * 支持拍照、文本粘贴和AI解析
 */

import React, { useState } from 'react';
import { Platform, Clipboard } from 'react-native';
import {
  Box,
  Text,
  VStack,
  HStack,
  Button,
  ButtonText,
  ButtonIcon,
  Pressable,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Textarea,
  TextareaInput,
  Spinner,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
  Image,
  Heading,
} from '@gluestack-ui/themed';
import {
  Camera,
  Image as ImageIcon,
  Clipboard as ClipboardIcon,
  Wand2,
  X,
  Check,
  AlertCircle,
} from 'lucide-react-native';

import { StandardButton } from './StandardButton';
import { StandardIcon } from './StandardIcon';
import { imageProcessingService, ImagePickerResult } from '../../services/imageProcessingService';
import { aiParsingService, ParsedContactInfo } from '../../services/aiParsingService';

interface SmartContactInputProps {
  isOpen: boolean;
  onClose: () => void;
  onParsedData: (data: ParsedContactInfo) => void;
  onError?: (error: string) => void;
}

export const SmartContactInput: React.FC<SmartContactInputProps> = ({
  isOpen,
  onClose,
  onParsedData,
  onError,
}) => {
  const toast = useToast();
  const [currentStep, setCurrentStep] = useState<'select' | 'camera' | 'text' | 'processing' | 'result'>('select');
  const [inputText, setInputText] = useState('');
  const [selectedImage, setSelectedImage] = useState<ImagePickerResult | null>(null);
  const [parsedData, setParsedData] = useState<ParsedContactInfo | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState('');

  // 重置状态
  const resetState = () => {
    setCurrentStep('select');
    setInputText('');
    setSelectedImage(null);
    setParsedData(null);
    setIsProcessing(false);
    setProcessingStep('');
  };

  // 处理关闭
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 处理拍照选择
  const handleCameraOption = async () => {
    try {
      setCurrentStep('processing');
      setIsProcessing(true);
      setProcessingStep('正在打开相机...');

      const image = await imageProcessingService.showImagePickerOptions();
      
      if (!image) {
        setCurrentStep('select');
        setIsProcessing(false);
        return;
      }

      setSelectedImage(image);
      setProcessingStep('正在识别图片中的文字...');

      // 进行OCR识别
      const ocrResult = await imageProcessingService.extractTextFromImage(image.base64 || '');
      
      if (!ocrResult.text.trim()) {
        toast.show({
          placement: 'top',
          render: ({ id }) => (
            <Toast nativeID={id} action="warning" variant="accent">
              <ToastTitle>未识别到文字</ToastTitle>
              <ToastDescription>请确保图片清晰，或选择手动输入文本</ToastDescription>
            </Toast>
          ),
        });
        setCurrentStep('select');
        setIsProcessing(false);
        return;
      }

      // 使用OCR结果进行AI解析
      await processTextWithAI(ocrResult.text);
    } catch (error) {
      console.error('拍照处理失败:', error);
      handleError('拍照识别失败，请重试');
    }
  };

  // 处理文本输入选择
  const handleTextOption = () => {
    setCurrentStep('text');
  };

  // 从剪贴板粘贴
  const handlePasteFromClipboard = async () => {
    try {
      const clipboardText = await Clipboard.getString();
      if (clipboardText.trim()) {
        setInputText(clipboardText);
        toast.show({
          placement: 'top',
          render: ({ id }) => (
            <Toast nativeID={id} action="success" variant="accent">
              <ToastTitle>已粘贴文本</ToastTitle>
              <ToastDescription>文本已从剪贴板粘贴</ToastDescription>
            </Toast>
          ),
        });
      } else {
        toast.show({
          placement: 'top',
          render: ({ id }) => (
            <Toast nativeID={id} action="warning" variant="accent">
              <ToastTitle>剪贴板为空</ToastTitle>
              <ToastDescription>请先复制联系人信息到剪贴板</ToastDescription>
            </Toast>
          ),
        });
      }
    } catch (error) {
      console.error('粘贴失败:', error);
      handleError('无法访问剪贴板');
    }
  };

  // 处理文本解析
  const handleTextParsing = async () => {
    if (!inputText.trim()) {
      toast.show({
        placement: 'top',
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>文本不能为空</ToastTitle>
            <ToastDescription>请输入或粘贴联系人信息</ToastDescription>
          </Toast>
        ),
      });
      return;
    }

    await processTextWithAI(inputText);
  };

  // 使用AI处理文本
  const processTextWithAI = async (text: string) => {
    try {
      setCurrentStep('processing');
      setIsProcessing(true);
      setProcessingStep('正在使用AI解析联系人信息...');

      const result = await aiParsingService.parseContactInfo(text, {
        language: 'auto',
        includeNotes: true,
      });

      setParsedData(result);
      setCurrentStep('result');
      setIsProcessing(false);
    } catch (error) {
      console.error('AI解析失败:', error);
      handleError('AI解析失败，请检查网络连接或稍后重试');
    }
  };

  // 处理错误
  const handleError = (message: string) => {
    setIsProcessing(false);
    setCurrentStep('select');
    
    toast.show({
      placement: 'top',
      render: ({ id }) => (
        <Toast nativeID={id} action="error" variant="accent">
          <ToastTitle>操作失败</ToastTitle>
          <ToastDescription>{message}</ToastDescription>
        </Toast>
      ),
    });

    if (onError) {
      onError(message);
    }
  };

  // 确认使用解析结果
  const handleConfirmResult = () => {
    if (parsedData) {
      onParsedData(parsedData);
      handleClose();
    }
  };

  // 渲染选择方式界面
  const renderSelectStep = () => (
    <VStack space="lg">
      <VStack space="sm" alignItems="center">
        <StandardIcon as={Wand2} size="3xl" color="$primary600" />
        <Heading size="lg" textAlign="center">智能识别联系人信息</Heading>
        <Text textAlign="center" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
          选择一种方式来快速添加联系人信息
        </Text>
      </VStack>

      <VStack space="md">
        <StandardButton
          variant="outline"
          size="lg"
          onPress={handleCameraOption}
          leftIcon={Camera}
        >
          拍照识别
        </StandardButton>

        <StandardButton
          variant="outline"
          size="lg"
          onPress={handleTextOption}
          leftIcon={ClipboardIcon}
        >
          输入文本
        </StandardButton>
      </VStack>

      <Text size="sm" textAlign="center" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
        支持名片、简历、邮件签名等多种格式
      </Text>
    </VStack>
  );

  // 渲染文本输入界面
  const renderTextStep = () => (
    <VStack space="lg">
      <VStack space="sm">
        <HStack justifyContent="space-between" alignItems="center">
          <Heading size="md">输入联系人信息</Heading>
          <StandardButton
            variant="outline"
            size="sm"
            onPress={handlePasteFromClipboard}
            leftIcon={ClipboardIcon}
          >
            粘贴
          </StandardButton>
        </HStack>
        <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
          请输入或粘贴包含联系人信息的文本
        </Text>
      </VStack>

      <Textarea h="$40">
        <TextareaInput
          placeholder="例如：&#10;张三&#10;产品经理&#10;科技有限公司&#10;电话：138-0013-8000&#10;邮箱：<EMAIL>"
          value={inputText}
          onChangeText={setInputText}
          multiline
        />
      </Textarea>

      <HStack space="md">
        <StandardButton
          variant="outline"
          onPress={() => setCurrentStep('select')}
          flex={1}
        >
          返回
        </StandardButton>
        <StandardButton
          variant="solid"
          action="primary"
          onPress={handleTextParsing}
          flex={1}
          leftIcon={Wand2}
        >
          开始解析
        </StandardButton>
      </HStack>
    </VStack>
  );

  // 渲染处理中界面
  const renderProcessingStep = () => (
    <VStack space="lg" alignItems="center" py="$8">
      <Spinner size="large" color="$primary600" />
      <VStack space="sm" alignItems="center">
        <Heading size="md">正在处理</Heading>
        <Text textAlign="center" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
          {processingStep}
        </Text>
      </VStack>
      {selectedImage && (
        <Box borderRadius="$md" overflow="hidden" maxWidth="$64" maxHeight="$48">
          <Image
            source={{ uri: selectedImage.uri }}
            alt="选择的图片"
            width="100%"
            height="$48"
            resizeMode="cover"
          />
        </Box>
      )}
    </VStack>
  );

  // 渲染结果界面
  const renderResultStep = () => {
    if (!parsedData) return null;

    const hasData = Object.keys(parsedData).some(key => 
      key !== 'confidence' && parsedData[key as keyof ParsedContactInfo]
    );

    return (
      <VStack space="lg">
        <VStack space="sm">
          <HStack justifyContent="space-between" alignItems="center">
            <Heading size="md">解析结果</Heading>
            <HStack space="xs" alignItems="center">
              <StandardIcon 
                as={parsedData.confidence > 0.7 ? Check : AlertCircle} 
                size="sm" 
                color={parsedData.confidence > 0.7 ? "$success600" : "$warning600"} 
              />
              <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                置信度: {Math.round(parsedData.confidence * 100)}%
              </Text>
            </HStack>
          </HStack>
          
          {!hasData && (
            <Text color="$warning600">
              未能识别到有效的联系人信息，请检查输入内容或手动填写
            </Text>
          )}
        </VStack>

        {hasData && (
          <VStack space="md">
            {parsedData.name && (
              <HStack justifyContent="space-between">
                <Text fontWeight="$medium">姓名:</Text>
                <Text>{parsedData.name}</Text>
              </HStack>
            )}
            
            {parsedData.company && (
              <HStack justifyContent="space-between">
                <Text fontWeight="$medium">公司:</Text>
                <Text>{parsedData.company}</Text>
              </HStack>
            )}
            
            {parsedData.position && (
              <HStack justifyContent="space-between">
                <Text fontWeight="$medium">职位:</Text>
                <Text>{parsedData.position}</Text>
              </HStack>
            )}
            
            {parsedData.phones && parsedData.phones.length > 0 && (
              <VStack space="xs">
                <Text fontWeight="$medium">电话:</Text>
                {parsedData.phones.map((phone, index) => (
                  <Text key={index} ml="$4">{phone}</Text>
                ))}
              </VStack>
            )}
            
            {parsedData.emails && parsedData.emails.length > 0 && (
              <VStack space="xs">
                <Text fontWeight="$medium">邮箱:</Text>
                {parsedData.emails.map((email, index) => (
                  <Text key={index} ml="$4">{email}</Text>
                ))}
              </VStack>
            )}
            
            {parsedData.address && (
              <HStack justifyContent="space-between">
                <Text fontWeight="$medium">地址:</Text>
                <Text flex={1} textAlign="right">{parsedData.address}</Text>
              </HStack>
            )}
          </VStack>
        )}

        <HStack space="md">
          <StandardButton
            variant="outline"
            onPress={() => setCurrentStep('select')}
            flex={1}
          >
            重新识别
          </StandardButton>
          <StandardButton
            variant="solid"
            action="primary"
            onPress={handleConfirmResult}
            flex={1}
            isDisabled={!hasData}
          >
            使用此结果
          </StandardButton>
        </HStack>
      </VStack>
    );
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <ModalBackdrop />
      <ModalContent maxWidth="$96">
        <ModalHeader>
          <ModalCloseButton>
            <StandardIcon as={X} />
          </ModalCloseButton>
        </ModalHeader>
        
        <ModalBody>
          {currentStep === 'select' && renderSelectStep()}
          {currentStep === 'text' && renderTextStep()}
          {currentStep === 'processing' && renderProcessingStep()}
          {currentStep === 'result' && renderResultStep()}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default SmartContactInput;
