import React from 'react';
import { Pressable, Box } from '@gluestack-ui/themed';
import { DESIGN_SYSTEM } from '../../constants/designSystem';
import StandardIcon from './StandardIcon';

interface CircularButtonProps {
  icon: React.ComponentType<any>;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  variant?: 'solid' | 'outline' | 'ghost';
  action?: 'primary' | 'secondary' | 'positive' | 'negative';
  isDisabled?: boolean;
  onPress?: () => void;
  style?: any;
  testID?: string;
}

/**
 * 标准化圆形按钮组件
 * 确保所有圆形按钮遵循设计系统规范 - 标准56px直径
 */
const CircularButton: React.FC<CircularButtonProps> = ({
  icon,
  size = 'xl', // 默认使用xl尺寸 (56px)
  variant = 'solid',
  action = 'primary',
  isDisabled = false,
  onPress,
  style,
  testID,
}) => {
  const buttonSize = DESIGN_SYSTEM.BUTTON.circular[size];
  const iconSize = size === 'sm' ? 'sm' : size === 'md' ? 'md' : 'lg';

  // 根据variant和action确定样式
  const getButtonStyles = () => {
    const baseStyles = {
      width: buttonSize,
      height: buttonSize,
      borderRadius: buttonSize / 2,
      justifyContent: 'center',
      alignItems: 'center',
      opacity: isDisabled ? 0.5 : 1,
    };

    switch (variant) {
      case 'solid':
        return {
          ...baseStyles,
          bg: action === 'primary' ? '$primary500' : 
              action === 'secondary' ? '$neutral500' :
              action === 'positive' ? '$success500' :
              action === 'negative' ? '$error500' : '$primary500',
          ...DESIGN_SYSTEM.SHADOWS.md,
        };
      
      case 'outline':
        return {
          ...baseStyles,
          bg: 'transparent',
          borderWidth: 2,
          borderColor: action === 'primary' ? '$primary500' : 
                      action === 'secondary' ? '$neutral500' :
                      action === 'positive' ? '$success500' :
                      action === 'negative' ? '$error500' : '$primary500',
        };
      
      case 'ghost':
        return {
          ...baseStyles,
          bg: 'transparent',
        };
      
      default:
        return baseStyles;
    }
  };

  const getIconColor = () => {
    if (variant === 'solid') {
      return 'white';
    }
    
    switch (action) {
      case 'primary':
        return DESIGN_SYSTEM.COLORS.primary[500];
      case 'secondary':
        return DESIGN_SYSTEM.COLORS.neutral[500];
      case 'positive':
        return DESIGN_SYSTEM.COLORS.semantic.success;
      case 'negative':
        return DESIGN_SYSTEM.COLORS.semantic.error;
      default:
        return DESIGN_SYSTEM.COLORS.primary[500];
    }
  };

  return (
    <Pressable
      onPress={isDisabled ? undefined : onPress}
      testID={testID}
      sx={{
        _pressed: {
          opacity: 0.8,
          transform: [{ scale: 0.95 }],
        },
      }}
    >
      <Box sx={{ ...getButtonStyles(), ...style }}>
        <StandardIcon
          as={icon}
          size={iconSize}
          color={getIconColor()}
        />
      </Box>
    </Pressable>
  );
};

export default CircularButton;
