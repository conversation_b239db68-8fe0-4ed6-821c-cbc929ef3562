import React, { useState, useEffect } from 'react';
import {
  ScrollView,
  KeyboardAvoidingView,
  Box,
  VStack,
  Pressable,
  Icon,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
  Heading,
  Button,
  ButtonText
} from '@gluestack-ui/themed';
import { Platform } from 'react-native';
import { useNavigation, NavigationProp, RouteProp } from '@react-navigation/native';
import { X, Wand2 } from 'lucide-react-native';
import { Tag, Contact, TagCategory, RootStackParamList } from '../types';

// Import global state and hooks
import { useContactsStore, useUIActions, useUISelectors, useAppActions } from '../store';
import { useNetworkError, usePerformanceMonitor } from '../hooks';
import { ErrorBoundary, SmartContactInput } from '../components/ui';
import { ParsedContactInfo } from '../services/aiParsingService';

// Import our new components
import AvatarPicker from '../components/ContactCreate/AvatarPicker';
import BasicInfoForm from '../components/ContactCreate/BasicInfoForm';
import ContactDetailsForm from '../components/ContactCreate/ContactDetailsForm';
import TagSelection from '../components/ContactCreate/TagSelection';
import NotesSection from '../components/ContactCreate/NotesSection';


type ContactCreateScreenProps = {
  route: RouteProp<RootStackParamList, 'ContactCreate'>;
};

const ContactCreateScreen = ({ route }: ContactCreateScreenProps) => {
  const toast = useToast();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  // Performance monitoring
  usePerformanceMonitor('ContactCreateScreen');

  // Global state
  const { addContact, updateContact } = useContactsStore();
  const { setFormDirty, setFormErrors, clearFormErrors } = useUIActions();
  const { setLoading } = useAppActions();
  const { isDirty, errors } = useUISelectors.useFormState();
  const { showError, clearError } = useNetworkError();

  // Check if we're editing an existing contact
  const isEditing = route?.params?.contact !== undefined;
  const existingContact = route?.params?.contact || null;

  // Form state
  const [contactPhoto, setContactPhoto] = useState<string | null>(
    isEditing ? existingContact?.photo || null : null
  );
  const [firstName, setFirstName] = useState(
    isEditing ? (existingContact?.firstName || '') : ''
  );
  const [lastName, setLastName] = useState(
    isEditing ? (existingContact?.lastName || '') : ''
  );
  const [company, setCompany] = useState(
    isEditing ? (existingContact?.company || '') : ''
  );
  const [position, setPosition] = useState(
    isEditing ? (existingContact?.position || '') : ''
  );
  const [email, setEmail] = useState(
    isEditing ? (existingContact?.email || '') : ''
  );
  const [phone, setPhone] = useState(
    isEditing ? (existingContact?.phone || '') : ''
  );
  const [location, setLocation] = useState(
    isEditing ? (existingContact?.location || '') : ''
  );
  const [notes, setNotes] = useState(isEditing ? (existingContact?.notes || '') : '');
  const [selectedTags, setSelectedTags] = useState<Tag[]>(isEditing ? existingContact?.tags || [] : []);

  // Smart input state
  const [showSmartInput, setShowSmartInput] = useState(false);

  // Input validation states - using global form errors
  const firstNameIsInvalid = !!errors.firstName;
  const lastNameIsInvalid = !!errors.lastName;
  const emailIsInvalid = !!errors.email;

  // Helper function to mark form as dirty and update global state
  const markFormDirty = () => {
    if (!isDirty) {
      setFormDirty(true);
    }
  };

  useEffect(() => {
    navigation.setOptions({
      title: isEditing ? 'Edit Contact' : 'Create Contact',
    });
  }, [navigation, isEditing]);

  // Available tag categories with sample tags
  const tagCategories: Record<TagCategory, Tag[]> = {
    industry: [
      { id: 'ind1', name: 'Technology', color: '#3498db' },
      { id: 'ind2', name: 'Finance', color: '#2ecc71' },
      { id: 'ind3', name: 'Healthcare', color: '#e74c3c' },
      { id: 'ind4', name: 'Education', color: '#f39c12' },
      { id: 'ind5', name: 'Manufacturing', color: '#9b59b6' },
    ],
    relationship: [
      { id: 'rel1', name: 'Client', color: '#1abc9c' },
      { id: 'rel2', name: 'Partner', color: '#d35400' },
      { id: 'rel3', name: 'Vendor', color: '#8e44ad' },
      { id: 'rel4', name: 'Colleague', color: '#27ae60' },
      { id: 'rel5', name: 'Prospect', color: '#c0392b' },
    ],
    personal: [
      { id: 'per1', name: 'Golf', color: '#16a085' },
      { id: 'per2', name: 'Coffee', color: '#7f8c8d' },
      { id: 'per3', name: 'Travel', color: '#f1c40f' },
      { id: 'per4', name: 'Art', color: '#2980b9' },
      { id: 'per5', name: 'Wine', color: '#c0392b' },
    ]
  };



  // Function to save the contact
  const saveContact = async () => {
    setLoading(true);
    clearFormErrors();

    try {
      // Validate required fields
      const validationErrors: Record<string, string> = {};

      if (!firstName.trim()) {
        validationErrors.firstName = 'First name is required';
      }
      if (!lastName.trim()) {
        validationErrors.lastName = 'Last name is required';
      }

      // Email validation (if email is provided)
      if (email.trim()) {
        const emailRegex = /\S+@\S+\.\S+/;
        if (!emailRegex.test(email.trim())) {
          validationErrors.email = 'Please enter a valid email address';
        }
      }

      if (Object.keys(validationErrors).length > 0) {
        setFormErrors(validationErrors);
        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={'toast-' + id} action="error" variant="solid">
              <ToastTitle>Validation Error</ToastTitle>
              <ToastDescription>
                Please fix the errors and try again.
              </ToastDescription>
            </Toast>
          )
        });
        return;
      }

      // Prepare contact data for store
      const contactData = {
        name: `${firstName.trim()} ${lastName.trim()}`.trim(),
        email: email.trim() || undefined,
        phone: phone.trim() || undefined,
        company: company.trim() || undefined,
        position: position.trim() || undefined,
        notes: notes.trim() || undefined,
        tags: selectedTags.map(tag => tag.name), // Convert to string array for store
        avatar: contactPhoto || undefined,
        isFavorite: existingContact?.isFavorite || false,
      };

      if (isEditing && existingContact) {
        // Update existing contact
        updateContact(existingContact.id, contactData);

        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="info" variant="accent">
              <ToastTitle>Contact Updated</ToastTitle>
              <ToastDescription>
                {firstName} {lastName} has been successfully updated.
              </ToastDescription>
            </Toast>
          ),
        });

        navigation.goBack();
      } else {
        // Add new contact
        addContact(contactData);

        toast.show({
          placement: "top",
          render: ({ id }) => (
            <Toast nativeID={id} action="success" variant="accent">
              <ToastTitle>Contact Saved</ToastTitle>
              <ToastDescription>
                {firstName} {lastName} has been successfully saved.
              </ToastDescription>
            </Toast>
          ),
        });

        // Clear form and mark as clean
        setFormDirty(false);
        clearFormErrors();
        navigation.goBack();
      }

    } catch (error) {
      console.error('Error saving contact:', error);
      showError({
        message: 'Failed to save contact. Please try again.',
        code: 'SAVE_ERROR'
      });
    } finally {
      setLoading(false);
    }
  };

  // Enhanced change handlers that mark form as dirty
  const handlePhotoChange = (photo: string | null) => {
    setContactPhoto(photo);
    markFormDirty();
  };

  const handleFirstNameChange = (value: string) => {
    setFirstName(value);
    markFormDirty();
    // Clear validation error when user starts typing
    if (errors.firstName) {
      setFormErrors({ ...errors, firstName: undefined });
    }
  };

  const handleLastNameChange = (value: string) => {
    setLastName(value);
    markFormDirty();
    if (errors.lastName) {
      setFormErrors({ ...errors, lastName: undefined });
    }
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);
    markFormDirty();
    if (errors.email) {
      setFormErrors({ ...errors, email: undefined });
    }
  };

  const handleCompanyChange = (value: string) => {
    setCompany(value);
    markFormDirty();
  };

  const handlePositionChange = (value: string) => {
    setPosition(value);
    markFormDirty();
  };

  const handlePhoneChange = (value: string) => {
    setPhone(value);
    markFormDirty();
  };

  const handleLocationChange = (value: string) => {
    setLocation(value);
    markFormDirty();
  };

  const handleNotesChange = (value: string) => {
    setNotes(value);
    markFormDirty();
  };

  const handleTagsChange = (tags: Tag[]) => {
    setSelectedTags(tags);
    markFormDirty();
  };

  // Handle AI parsed contact data
  const handleParsedContactData = (parsedData: ParsedContactInfo) => {
    // Fill form with parsed data
    if (parsedData.name) {
      const nameParts = parsedData.name.trim().split(' ');
      if (nameParts.length >= 2) {
        setFirstName(nameParts[0]);
        setLastName(nameParts.slice(1).join(' '));
      } else {
        setFirstName(parsedData.name);
      }
    }

    if (parsedData.company) {
      setCompany(parsedData.company);
    }

    if (parsedData.position) {
      setPosition(parsedData.position);
    }

    if (parsedData.emails && parsedData.emails.length > 0) {
      setEmail(parsedData.emails[0]); // Use first email
    }

    if (parsedData.phones && parsedData.phones.length > 0) {
      setPhone(parsedData.phones[0]); // Use first phone
    }

    if (parsedData.address) {
      setLocation(parsedData.address);
    }

    if (parsedData.notes) {
      setNotes(parsedData.notes);
    }

    // Mark form as dirty since we've filled it with data
    markFormDirty();

    // Show success message
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={id} action="success" variant="accent">
          <ToastTitle>信息已自动填充</ToastTitle>
          <ToastDescription>
            AI已成功识别并填充联系人信息，请检查并确认
          </ToastDescription>
        </Toast>
      ),
    });
  };

  // Handle smart input error
  const handleSmartInputError = (error: string) => {
    showError({
      message: error,
      code: 'AI_PARSING_ERROR'
    });
  };

  return (
    <ErrorBoundary>
      <KeyboardAvoidingView
        flex={1}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
        sx={{ _light: { bg: '$backgroundLight50' }, _dark: { bg: '$backgroundDark900' } }}
      >
      {/* Screen Header */}
      <Box
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        p="$4"
        borderBottomWidth={1}
        sx={{
          _light: { borderBottomColor: '$borderLight200', bg: '$primary500' },
          _dark: { borderBottomColor: '$borderDark700', bg: '$primary700' }
        }}
      >
        <Pressable onPress={() => navigation.goBack()}>
          <Icon as={X} size="xl" color="$textLight50" />
        </Pressable>

        <Heading size="lg" color="$textLight50" flex={1} textAlign="center">
          {isEditing ? 'Edit Contact' : 'Create New Contact'}
        </Heading>

        {/* Smart Input Button - only show when creating new contact */}
        {!isEditing && (
          <Pressable onPress={() => setShowSmartInput(true)} p="$2">
            <Icon as={Wand2} size="xl" color="$textLight50" />
          </Pressable>
        )}

        {/* Placeholder for balance when editing */}
        {isEditing && <Box w="$8" />}
      </Box>

      <ScrollView contentContainerStyle={{ flexGrow: 1 }} bounces={false}>
        <VStack space="lg" p="$4">
          {/* Avatar Picker Component */}
          <AvatarPicker
            contactPhoto={contactPhoto}
            onPhotoChange={handlePhotoChange}
            firstName={firstName}
            lastName={lastName}
          />

          {/* Basic Information Form Component */}
          <BasicInfoForm
            firstName={firstName}
            lastName={lastName}
            company={company}
            position={position}
            onFirstNameChange={handleFirstNameChange}
            onLastNameChange={handleLastNameChange}
            onCompanyChange={handleCompanyChange}
            onPositionChange={handlePositionChange}
            firstNameIsInvalid={firstNameIsInvalid}
            lastNameIsInvalid={lastNameIsInvalid}
            onFirstNameValidationChange={() => {}} // No longer needed
            onLastNameValidationChange={() => {}} // No longer needed
          />

          {/* Contact Details Form Component */}
          <ContactDetailsForm
            email={email}
            phone={phone}
            location={location}
            onEmailChange={handleEmailChange}
            onPhoneChange={handlePhoneChange}
            onLocationChange={handleLocationChange}
            emailIsInvalid={emailIsInvalid}
            onEmailValidationChange={() => {}} // No longer needed
          />

          {/* Tag Selection Component */}
          <TagSelection
            selectedTags={selectedTags}
            onTagsChange={handleTagsChange}
            availableTagCategories={tagCategories}
          />

          {/* Notes Section Component */}
          <NotesSection
            notes={notes}
            onNotesChange={handleNotesChange}
          />

          {/* Save Button */}
          <VStack space="md" mt="$6">
            <Button
              action="primary"
              onPress={saveContact}
              size="lg"
            >
              <ButtonText>
                {isEditing ? 'Update Contact' : 'Save Contact'}
              </ButtonText>
            </Button>
          </VStack>

          {/* Extra space at the bottom for better scrolling */}
          <Box sx={{ h: '$16' }} />
        </VStack>
      </ScrollView>

      {/* Smart Contact Input Modal */}
      <SmartContactInput
        isOpen={showSmartInput}
        onClose={() => setShowSmartInput(false)}
        onParsedData={handleParsedContactData}
        onError={handleSmartInputError}
      />
    </KeyboardAvoidingView>
    </ErrorBoundary>
  );
};

export default ContactCreateScreen;