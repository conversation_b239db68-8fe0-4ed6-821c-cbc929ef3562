import React, { useState, useEffect, useMemo } from 'react';
import { Platform, Dimensions } from 'react-native';
import {
  Box,
  Text,
  Heading,
  VStack,
  HStack,
  Button,
  ButtonText,
  ButtonIcon,
  Pressable,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  ScrollView,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
  Badge,
  BadgeText,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
} from '@gluestack-ui/themed';
import { useNavigation, RouteProp } from '@react-navigation/native';
import {
  ArrowLeft,
  Share,
  Settings,
  UserPlus,
  Plus,
  Minus,
  RotateCcw,
  Filter,
  Users,
  Network,
  X,
  Eye,
  MessageCircle,
  ChevronRight,
} from 'lucide-react-native';
import { PanGestureHandler, PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';

// Import global state and hooks
import { useContactsStore, useContactsSelectors } from '../store';
import { usePerformanceMonitor, useNetworkError } from '../hooks';
import { ErrorBoundary, StandardButton, StandardIcon, CircularButton } from '../components/ui';
import { Contact } from '../types';

// Types and interfaces
type NetworkVisualScreenNavigationProp = any; // TODO: 添加正确的导航类型

interface NetworkNode {
  id: string;
  name: string;
  type: 'self' | 'contact';
  x: number;
  y: number;
  connections: string[];
  industry?: string;
  avatar?: string;
  company?: string;
  position?: string;
  connectionStrength?: number; // 0-1, 连接强度
  lastInteraction?: Date;
}

interface ConnectionLink {
  from: string;
  to: string;
  strength: number; // 连接强度
  type: 'direct' | 'mutual'; // 直接连接或通过共同联系人
}

interface NetworkStats {
  totalContacts: number;
  directConnections: number;
  mutualConnections: number;
  industries: Record<string, number>;
  strongConnections: number; // 强连接数量
}

type GestureContext = {
  startX: number;
  startY: number;
};

const { width, height } = Dimensions.get('window');

// Industry colors mapping
const industryColors: Record<string, string> = {
  'Technology': '#3B82F6', // Blue
  'Finance': '#8B5CF6', // Purple
  'Marketing': '#F59E0B', // Orange
  'Sales': '#10B981', // Green
  'Healthcare': '#EF4444', // Red
  'Education': '#6366F1', // Indigo
  'Consulting': '#84CC16', // Lime
  'Media': '#EC4899', // Pink
  'self': '#1F2937', // Dark gray
  'default': '#6B7280', // Gray
};

// Connection strength colors
const connectionColors = {
  strong: '#10B981', // Green
  medium: '#F59E0B', // Orange
  weak: '#6B7280', // Gray
};

/**
 * 关系网络可视化屏幕 - 完全重构版本
 * 使用Gluestack UI和全局状态管理
 */
const NetworkVisualScreen: React.FC = () => {
  const navigation = useNavigation<NetworkVisualScreenNavigationProp>();
  const toast = useToast();

  // Performance monitoring
  usePerformanceMonitor('NetworkVisualScreen');

  // Global state
  const { contacts } = useContactsStore();
  const { showError, clearError } = useNetworkError();

  // Local state
  const [selectedNode, setSelectedNode] = useState<NetworkNode | null>(null);
  const [industryFilter, setIndustryFilter] = useState<string>('All');
  const [connectionFilter, setConnectionFilter] = useState<string>('All'); // All, Strong, Medium, Weak
  const [scale, setScale] = useState(1);
  const [showNodeModal, setShowNodeModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [layoutType, setLayoutType] = useState<'circular' | 'force' | 'hierarchical'>('circular');
  const [showConnectionLabels, setShowConnectionLabels] = useState(false);

  // Animated values for pan and zoom
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const animatedScale = useSharedValue(1);

  // Generate network data from contacts
  const networkData = useMemo(() => {
    const centerX = width / 2;
    const centerY = height / 2.5;

    // Create user node
    const userNode: NetworkNode = {
      id: 'user',
      name: '我',
      type: 'self',
      x: centerX,
      y: centerY,
      connections: contacts.map(c => c.id),
      industry: 'self',
    };

    // Create contact nodes in circular layout
    const contactNodes: NetworkNode[] = contacts.map((contact, index) => {
      const angle = (index / contacts.length) * 2 * Math.PI;
      const radius = 120 + Math.random() * 80; // Add some randomness

      return {
        id: contact.id,
        name: contact.name,
        type: 'contact',
        x: centerX + Math.cos(angle) * radius,
        y: centerY + Math.sin(angle) * radius,
        connections: ['user'], // For now, all contacts connect to user
        industry: contact.company || 'default',
        avatar: contact.avatar,
        company: contact.company,
        position: contact.position,
        connectionStrength: Math.random(), // Random for demo
        lastInteraction: contact.lastContactDate,
      };
    });

    return [userNode, ...contactNodes];
  }, [contacts, width, height]);

  // Generate connections
  const connections = useMemo((): ConnectionLink[] => {
    const links: ConnectionLink[] = [];

    networkData.forEach(node => {
      if (node.type === 'contact') {
        // Add connection to user
        links.push({
          from: 'user',
          to: node.id,
          strength: node.connectionStrength || 0.5,
          type: 'direct',
        });

        // Add some mutual connections (demo)
        if (Math.random() > 0.7) {
          const otherContacts = networkData.filter(n => n.type === 'contact' && n.id !== node.id);
          if (otherContacts.length > 0) {
            const randomContact = otherContacts[Math.floor(Math.random() * otherContacts.length)];
            links.push({
              from: node.id,
              to: randomContact.id,
              strength: Math.random() * 0.5,
              type: 'mutual',
            });
          }
        }
      }
    });

    return links;
  }, [networkData]);

  // Calculate network statistics
  const networkStats = useMemo((): NetworkStats => {
    const industries: Record<string, number> = {};
    let strongConnections = 0;

    networkData.forEach(node => {
      if (node.type === 'contact') {
        const industry = node.industry || 'default';
        industries[industry] = (industries[industry] || 0) + 1;

        if ((node.connectionStrength || 0) > 0.7) {
          strongConnections++;
        }
      }
    });

    return {
      totalContacts: contacts.length,
      directConnections: contacts.length,
      mutualConnections: connections.filter(c => c.type === 'mutual').length,
      industries,
      strongConnections,
    };
  }, [networkData, connections, contacts.length]);

  // Pan gesture handler
  const panGestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent, GestureContext>({
    onStart: (_, ctx) => {
      ctx.startX = translateX.value;
      ctx.startY = translateY.value;
    },
    onActive: (event, ctx) => {
      translateX.value = ctx.startX + event.translationX;
      translateY.value = ctx.startY + event.translationY;
    },
    onEnd: () => {
      // Optional: Add spring animation when releasing
    },
  });

  // Animated styles for the graph container
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: animatedScale.value }
      ],
    };
  });

  // Filter nodes based on industry and connection strength
  const filteredNodes = useMemo(() => {
    let nodes = networkData;

    // Industry filter
    if (industryFilter !== 'All') {
      nodes = nodes.filter(node =>
        node.type === 'self' || node.industry === industryFilter
      );
    }

    return nodes;
  }, [networkData, industryFilter]);

  // Filter connections based on strength and visible nodes
  const filteredConnections = useMemo(() => {
    const nodeIds = new Set(filteredNodes.map(n => n.id));
    let filteredConns = connections.filter(conn =>
      nodeIds.has(conn.from) && nodeIds.has(conn.to)
    );

    // Connection strength filter
    if (connectionFilter === 'Strong') {
      filteredConns = filteredConns.filter(conn => conn.strength > 0.7);
    } else if (connectionFilter === 'Medium') {
      filteredConns = filteredConns.filter(conn => conn.strength > 0.4 && conn.strength <= 0.7);
    } else if (connectionFilter === 'Weak') {
      filteredConns = filteredConns.filter(conn => conn.strength <= 0.4);
    }

    return filteredConns;
  }, [connections, filteredNodes, connectionFilter]);

  // Get available industries for filter
  const availableIndustries = useMemo(() => {
    const industries = new Set<string>();
    networkData.forEach(node => {
      if (node.type === 'contact' && node.industry) {
        industries.add(node.industry);
      }
    });
    return Array.from(industries).sort();
  }, [networkData]);

  // Event handlers
  const handleNodePress = (node: NetworkNode) => {
    runOnJS(setSelectedNode)(node);
    runOnJS(setShowNodeModal)(true);
  };

  const handleViewContactDetail = (contactId: string) => {
    if (contactId === 'user') {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="info" variant="accent">
            <ToastTitle>个人资料</ToastTitle>
            <ToastDescription>查看您的个人资料</ToastDescription>
          </Toast>
        )
      });
      return;
    }

    setShowNodeModal(false);
    navigation.navigate('ContactDetail', { contactId });
  };

  const handleAddConnection = () => {
    navigation.navigate('ContactCreate');
  };

  const handleMessageContact = (contactId: string) => {
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={id} action="info" variant="accent">
          <ToastTitle>发送消息</ToastTitle>
          <ToastDescription>消息功能即将推出</ToastDescription>
        </Toast>
      )
    });
  };

  const handleExportNetwork = () => {
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={id} action="info" variant="accent">
          <ToastTitle>导出网络</ToastTitle>
          <ToastDescription>网络导出功能即将推出</ToastDescription>
        </Toast>
      )
    });
  };

  // Zoom and pan controls
  const handleZoomIn = () => {
    if (animatedScale.value < 2) {
      animatedScale.value = withSpring(Math.min(2, animatedScale.value + 0.2));
    }
  };

  const handleZoomOut = () => {
    if (animatedScale.value > 0.5) {
      animatedScale.value = withSpring(Math.max(0.5, animatedScale.value - 0.2));
    }
  };

  const handleReset = () => {
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    animatedScale.value = withSpring(1);
  };

  // Utility functions
  const getNodeColor = (node: NetworkNode) => {
    if (node.type === 'self') {
      return industryColors.self;
    }
    return industryColors[node.industry || 'default'] || industryColors.default;
  };

  const getConnectionColor = (connection: ConnectionLink) => {
    if (connection.strength > 0.7) return connectionColors.strong;
    if (connection.strength > 0.4) return connectionColors.medium;
    return connectionColors.weak;
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Render node information modal
  const renderNodeModal = () => {
    if (!selectedNode) return null;

    const nodeColor = getNodeColor(selectedNode);
    const connectionStrength = selectedNode.connectionStrength || 0;

    return (
      <Modal isOpen={showNodeModal} onClose={() => setShowNodeModal(false)}>
        <ModalBackdrop />
        <ModalContent maxWidth="$96">
          <ModalHeader>
            <VStack space="md" alignItems="center">
              <Avatar size="xl" bg={nodeColor}>
                <AvatarFallbackText>{getInitials(selectedNode.name)}</AvatarFallbackText>
                {selectedNode.avatar && (
                  <AvatarImage source={{ uri: selectedNode.avatar }} alt={selectedNode.name} />
                )}
              </Avatar>
              <VStack space="xs" alignItems="center">
                <Heading size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  {selectedNode.name}
                </Heading>
                {selectedNode.type !== 'self' && (
                  <VStack space="xs" alignItems="center">
                    <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                      {selectedNode.position && selectedNode.company
                        ? `${selectedNode.position} at ${selectedNode.company}`
                        : selectedNode.company || selectedNode.position || '无公司信息'
                      }
                    </Text>
                    <Badge
                      size="sm"
                      variant="solid"
                      sx={{ bg: nodeColor }}
                    >
                      <BadgeText>{selectedNode.industry}</BadgeText>
                    </Badge>
                  </VStack>
                )}
              </VStack>
            </VStack>
            <ModalCloseButton>
              <StandardIcon as={X} />
            </ModalCloseButton>
          </ModalHeader>

          <ModalBody>
            <VStack space="lg">
              {/* Network Statistics */}
              <VStack space="md">
                <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  网络信息
                </Text>
                <HStack space="lg" justifyContent="space-around">
                  <VStack space="xs" alignItems="center">
                    <Text fontWeight="$bold" size="xl" color="$primary600">
                      {selectedNode.connections.length}
                    </Text>
                    <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                      连接数
                    </Text>
                  </VStack>

                  {selectedNode.type !== 'self' && (
                    <>
                      <VStack space="xs" alignItems="center">
                        <Text fontWeight="$bold" size="xl" color="$primary600">
                          {Math.round(connectionStrength * 100)}%
                        </Text>
                        <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                          连接强度
                        </Text>
                      </VStack>

                      <VStack space="xs" alignItems="center">
                        <Text fontWeight="$bold" size="xl" color="$primary600">
                          1st
                        </Text>
                        <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                          关系度
                        </Text>
                      </VStack>
                    </>
                  )}
                </HStack>
              </VStack>

              {/* Last Interaction */}
              {selectedNode.type !== 'self' && selectedNode.lastInteraction && (
                <VStack space="sm">
                  <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    最近互动
                  </Text>
                  <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                    {new Date(selectedNode.lastInteraction).toLocaleDateString('zh-CN')}
                  </Text>
                </VStack>
              )}
            </VStack>
          </ModalBody>

          <ModalFooter>
            <HStack space="md" flex={1}>
              {selectedNode.type !== 'self' ? (
                <>
                  <StandardButton
                    variant="outline"
                    onPress={() => handleViewContactDetail(selectedNode.id)}
                    flex={1}
                    leftIcon={Eye}
                  >
                    查看详情
                  </StandardButton>
                  <StandardButton
                    variant="solid"
                    action="primary"
                    onPress={() => handleMessageContact(selectedNode.id)}
                    flex={1}
                    leftIcon={MessageCircle}
                  >
                    发送消息
                  </StandardButton>
                </>
              ) : (
                <StandardButton
                  variant="solid"
                  action="primary"
                  onPress={handleAddConnection}
                  flex={1}
                  leftIcon={UserPlus}
                >
                  添加联系人
                </StandardButton>
              )}
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>
    );
  };

  // Render settings modal
  const renderSettingsModal = () => (
    <Modal isOpen={showSettingsModal} onClose={() => setShowSettingsModal(false)}>
      <ModalBackdrop />
      <ModalContent maxWidth="$96">
        <ModalHeader>
          <Heading size="lg">网络视图设置</Heading>
          <ModalCloseButton>
            <StandardIcon as={X} />
          </ModalCloseButton>
        </ModalHeader>

        <ModalBody>
          <VStack space="lg">
            {/* Layout Type */}
            <VStack space="sm">
              <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                布局类型
              </Text>
              <VStack space="sm">
                {[
                  { value: 'circular', label: '圆形布局', description: '节点围绕中心排列' },
                  { value: 'force', label: '力导向布局', description: '基于连接强度的动态布局' },
                  { value: 'hierarchical', label: '层次布局', description: '按关系层级排列' },
                ].map((layout) => (
                  <Pressable
                    key={layout.value}
                    onPress={() => setLayoutType(layout.value as any)}
                    p="$3"
                    borderRadius="$md"
                    borderWidth="$1"
                    borderColor={layoutType === layout.value ? '$primary600' : '$borderLight200'}
                    bg={layoutType === layout.value ? '$primary50' : 'transparent'}
                    sx={{
                      _dark: {
                        borderColor: layoutType === layout.value ? '$primary400' : '$borderDark700',
                        bg: layoutType === layout.value ? '$primary900' : 'transparent',
                      }
                    }}
                  >
                    <VStack space="xs">
                      <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                        {layout.label}
                      </Text>
                      <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                        {layout.description}
                      </Text>
                    </VStack>
                  </Pressable>
                ))}
              </VStack>
            </VStack>

            {/* Display Options */}
            <VStack space="sm">
              <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                显示选项
              </Text>
              <HStack justifyContent="space-between" alignItems="center">
                <Text color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  显示连接标签
                </Text>
                <Switch
                  value={showConnectionLabels}
                  onValueChange={setShowConnectionLabels}
                />
              </HStack>
            </VStack>

            {/* Zoom Level */}
            <VStack space="sm">
              <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                缩放级别: {Math.round(animatedScale.value * 100)}%
              </Text>
              <Slider
                value={animatedScale.value}
                onChange={(value) => {
                  animatedScale.value = withSpring(value);
                }}
                minValue={0.5}
                maxValue={2}
                step={0.1}
              >
                <SliderTrack>
                  <SliderFilledTrack />
                </SliderTrack>
                <SliderThumb />
              </Slider>
            </VStack>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack space="md">
            <StandardButton
              variant="outline"
              onPress={handleReset}
              leftIcon={RotateCcw}
            >
              重置视图
            </StandardButton>
            <StandardButton
              variant="solid"
              action="primary"
              onPress={() => setShowSettingsModal(false)}
            >
              完成
            </StandardButton>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );

  return (
    <ErrorBoundary>
      <Box flex={1} bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark950' } }}>
        {/* Header */}
        <Box
          bg="$backgroundLight0"
          pt={Platform.OS === 'ios' ? '$12' : '$6'}
          pb="$4"
          px="$4"
          borderBottomWidth="$1"
          borderBottomColor="$borderLight200"
          sx={{
            _dark: {
              bg: '$backgroundDark900',
              borderBottomColor: '$borderDark700',
            },
          }}
        >
          <HStack justifyContent="space-between" alignItems="center">
            <Pressable onPress={() => navigation.goBack()} p="$1">
              <StandardIcon as={ArrowLeft} size="lg" color="$primary600" />
            </Pressable>

            <Heading size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              关系网络
            </Heading>

            <HStack space="sm">
              <Pressable onPress={() => setShowSettingsModal(true)} p="$1">
                <StandardIcon as={Settings} size="md" color="$textLight600" />
              </Pressable>
              <Pressable onPress={handleExportNetwork} p="$1">
                <StandardIcon as={Share} size="md" color="$textLight600" />
              </Pressable>
            </HStack>
          </HStack>
        </Box>

        {/* Network Statistics */}
        <Box p="$4" bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark900' } }}>
          <VStack space="md">
            <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              网络概览
            </Text>
            <HStack space="lg" justifyContent="space-around">
              <VStack space="xs" alignItems="center">
                <Text fontWeight="$bold" size="xl" color="$primary600">
                  {networkStats.totalContacts}
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  总联系人
                </Text>
              </VStack>

              <VStack space="xs" alignItems="center">
                <Text fontWeight="$bold" size="xl" color="$success600">
                  {networkStats.strongConnections}
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  强连接
                </Text>
              </VStack>

              <VStack space="xs" alignItems="center">
                <Text fontWeight="$bold" size="xl" color="$warning600">
                  {Object.keys(networkStats.industries).length}
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  行业数
                </Text>
              </VStack>

              <VStack space="xs" alignItems="center">
                <Text fontWeight="$bold" size="xl" color="$info600">
                  {networkStats.mutualConnections}
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  共同联系人
                </Text>
              </VStack>
            </HStack>
          </VStack>
        </Box>

        {/* Filters */}
        <VStack space="md" p="$4">
          {/* Industry Filter */}
          <VStack space="sm">
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                行业筛选
              </Text>
              <StandardIcon as={Filter} size="sm" color="$textLight600" />
            </HStack>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <HStack space="sm" px="$1">
                {['All', ...availableIndustries].map((industry) => (
                  <Pressable
                    key={industry}
                    onPress={() => setIndustryFilter(industry)}
                    px="$3"
                    py="$2"
                    borderRadius="$full"
                    bg={industryFilter === industry ? '$primary600' : '$backgroundLight100'}
                    borderWidth="$1"
                    borderColor={industryFilter === industry ? '$primary600' : '$borderLight200'}
                    sx={{
                      _dark: {
                        bg: industryFilter === industry ? '$primary500' : '$backgroundDark800',
                        borderColor: industryFilter === industry ? '$primary500' : '$borderDark600',
                      }
                    }}
                  >
                    <Text
                      size="sm"
                      fontWeight="$medium"
                      color={industryFilter === industry ? 'white' : '$textLight700'}
                      sx={{
                        _dark: {
                          color: industryFilter === industry ? 'white' : '$textDark300',
                        }
                      }}
                    >
                      {industry === 'All' ? '全部' : industry}
                    </Text>
                  </Pressable>
                ))}
              </HStack>
            </ScrollView>
          </VStack>

          {/* Connection Strength Filter */}
          <VStack space="sm">
            <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              连接强度
            </Text>
            <HStack space="sm">
              {[
                { value: 'All', label: '全部', color: '$neutral500' },
                { value: 'Strong', label: '强连接', color: '$success600' },
                { value: 'Medium', label: '中等', color: '$warning600' },
                { value: 'Weak', label: '弱连接', color: '$neutral400' },
              ].map((filter) => (
                <Pressable
                  key={filter.value}
                  onPress={() => setConnectionFilter(filter.value)}
                  px="$3"
                  py="$2"
                  borderRadius="$md"
                  bg={connectionFilter === filter.value ? filter.color : '$backgroundLight100'}
                  borderWidth="$1"
                  borderColor={connectionFilter === filter.value ? filter.color : '$borderLight200'}
                  sx={{
                    _dark: {
                      bg: connectionFilter === filter.value ? filter.color : '$backgroundDark800',
                      borderColor: connectionFilter === filter.value ? filter.color : '$borderDark600',
                    }
                  }}
                >
                  <Text
                    size="sm"
                    fontWeight="$medium"
                    color={connectionFilter === filter.value ? 'white' : '$textLight700'}
                    sx={{
                      _dark: {
                        color: connectionFilter === filter.value ? 'white' : '$textDark300',
                      }
                    }}
                  >
                    {filter.label}
                  </Text>
                </Pressable>
              ))}
            </HStack>
          </VStack>
        </VStack>

        {/* Network Graph */}
        <Box flex={1} position="relative" overflow="hidden">
          <PanGestureHandler onGestureEvent={panGestureHandler}>
            <Animated.View style={[{ flex: 1 }, animatedStyle]}>
              {/* Render connections first (behind nodes) */}
              {filteredConnections.map((connection, index) => {
                const fromNode = filteredNodes.find(n => n.id === connection.from);
                const toNode = filteredNodes.find(n => n.id === connection.to);

                if (!fromNode || !toNode) return null;

                const connectionColor = getConnectionColor(connection);
                const lineWidth = connection.strength > 0.7 ? 3 : connection.strength > 0.4 ? 2 : 1;

                return (
                  <Box
                    key={`${connection.from}-${connection.to}-${index}`}
                    position="absolute"
                    left={Math.min(fromNode.x, toNode.x)}
                    top={Math.min(fromNode.y, toNode.y)}
                    width={Math.abs(toNode.x - fromNode.x)}
                    height={Math.abs(toNode.y - fromNode.y)}
                    pointerEvents="none"
                  >
                    <Box
                      position="absolute"
                      left={fromNode.x < toNode.x ? 0 : Math.abs(toNode.x - fromNode.x)}
                      top={fromNode.y < toNode.y ? 0 : Math.abs(toNode.y - fromNode.y)}
                      width={Math.sqrt(
                        Math.pow(toNode.x - fromNode.x, 2) + Math.pow(toNode.y - fromNode.y, 2)
                      )}
                      height={lineWidth}
                      bg={connectionColor}
                      sx={{
                        transform: [
                          {
                            rotate: `${Math.atan2(toNode.y - fromNode.y, toNode.x - fromNode.x)}rad`
                          }
                        ],
                      }}
                    />

                    {/* Connection label */}
                    {showConnectionLabels && connection.strength > 0.6 && (
                      <Box
                        position="absolute"
                        left={(Math.abs(toNode.x - fromNode.x)) / 2}
                        top={(Math.abs(toNode.y - fromNode.y)) / 2}
                        bg="$backgroundLight0"
                        px="$1"
                        py="$0.5"
                        borderRadius="$sm"
                        sx={{ _dark: { bg: '$backgroundDark900' } }}
                      >
                        <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                          {Math.round(connection.strength * 100)}%
                        </Text>
                      </Box>
                    )}
                  </Box>
                );
              })}

              {/* Render nodes */}
              {filteredNodes.map(node => {
                const nodeColor = getNodeColor(node);
                const isSelected = selectedNode?.id === node.id;
                const nodeSize = node.type === 'self' ? 60 : 50;

                return (
                  <Pressable
                    key={node.id}
                    position="absolute"
                    left={node.x - nodeSize / 2}
                    top={node.y - nodeSize / 2}
                    onPress={() => handleNodePress(node)}
                  >
                    <Box
                      width={nodeSize}
                      height={nodeSize}
                      borderRadius={nodeSize / 2}
                      bg={nodeColor}
                      borderWidth={isSelected ? 3 : node.type === 'self' ? 2 : 1}
                      borderColor={isSelected ? 'white' : node.type === 'self' ? 'white' : nodeColor}
                      justifyContent="center"
                      alignItems="center"
                      sx={{
                        shadowColor: '$neutral900',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.2,
                        shadowRadius: 4,
                        elevation: 4,
                      }}
                    >
                      {node.avatar ? (
                        <Avatar size={node.type === 'self' ? 'lg' : 'md'} bg={nodeColor}>
                          <AvatarImage source={{ uri: node.avatar }} alt={node.name} />
                          <AvatarFallbackText>{getInitials(node.name)}</AvatarFallbackText>
                        </Avatar>
                      ) : (
                        <Text
                          fontWeight="$bold"
                          color="white"
                          size={node.type === 'self' ? 'lg' : 'md'}
                        >
                          {getInitials(node.name)}
                        </Text>
                      )}
                    </Box>

                    {/* Node label */}
                    <Box
                      position="absolute"
                      top={nodeSize + 5}
                      left={-20}
                      width={nodeSize + 40}
                      alignItems="center"
                    >
                      <Text
                        size="xs"
                        fontWeight="$medium"
                        color="$textLight700"
                        textAlign="center"
                        numberOfLines={1}
                        sx={{ _dark: { color: '$textDark300' } }}
                      >
                        {node.name}
                      </Text>
                    </Box>
                  </Pressable>
                );
              })}
            </Animated.View>
          </PanGestureHandler>
        </Box>

        {/* Floating Action Button */}
        <Box position="absolute" bottom="$6" right="$4">
          <CircularButton
            icon={UserPlus}
            size="xl"
            variant="solid"
            action="primary"
            onPress={handleAddConnection}
          />
        </Box>

        {/* Zoom Controls */}
        <VStack
          position="absolute"
          top="$20"
          right="$4"
          space="sm"
          bg="$backgroundLight0"
          borderRadius="$lg"
          p="$2"
          sx={{
            _dark: { bg: '$backgroundDark900' },
            shadowColor: '$neutral900',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 4,
          }}
        >
          <Pressable onPress={handleZoomIn} p="$2">
            <StandardIcon as={Plus} size="md" color="$textLight600" />
          </Pressable>
          <Pressable onPress={handleZoomOut} p="$2">
            <StandardIcon as={Minus} size="md" color="$textLight600" />
          </Pressable>
          <Pressable onPress={handleReset} p="$2">
            <StandardIcon as={RotateCcw} size="md" color="$textLight600" />
          </Pressable>
        </VStack>

        {/* Selected Node Info Card */}
        {selectedNode && !showNodeModal && (
          <Box
            position="absolute"
            bottom="$6"
            left="$4"
            right="$20"
            bg="$backgroundLight0"
            borderRadius="$lg"
            p="$3"
            sx={{
              _dark: { bg: '$backgroundDark900' },
              shadowColor: '$neutral900',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 4,
            }}
          >
            <Pressable onPress={() => setShowNodeModal(true)}>
              <HStack space="md" alignItems="center">
                <Avatar size="sm" bg={getNodeColor(selectedNode)}>
                  <AvatarFallbackText>{getInitials(selectedNode.name)}</AvatarFallbackText>
                  {selectedNode.avatar && (
                    <AvatarImage source={{ uri: selectedNode.avatar }} alt={selectedNode.name} />
                  )}
                </Avatar>
                <VStack flex={1}>
                  <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    {selectedNode.name}
                  </Text>
                  {selectedNode.type !== 'self' && (
                    <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                      {selectedNode.industry}
                    </Text>
                  )}
                </VStack>
                <StandardIcon as={ChevronRight} size="md" color="$primary600" />
              </HStack>
            </Pressable>
          </Box>
        )}

        {/* Modals */}
        {renderNodeModal()}
        {renderSettingsModal()}
      </Box>
    </ErrorBoundary>
  );
};

export default NetworkVisualScreen;

// NetworkVisualScreen 组件完全重构完成
// 使用 Gluestack UI 和全局状态管理，移除了所有 StyleSheet 样式