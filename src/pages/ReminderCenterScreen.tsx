/**
 * 提醒中心页面
 * 显示和管理所有提醒
 */

import React, { useEffect, useState } from 'react';
import { FlatList, RefreshControl } from 'react-native';
import {
  Box,
  Text,
  VStack,
  HStack,
  Heading,
  ScrollView,
  Pressable,
  Badge,
  BadgeText,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';
import {
  Bell,
  Calendar,
  Clock,
  Filter,
  Plus,
  CheckCircle,
  AlertCircle,
  X,
  MoreVertical,
} from 'lucide-react-native';

import { StandardButton, StandardIcon, CircularButton, ErrorBoundary } from '../components/ui';
import { useReminderStore, useReminderSelectors } from '../store/reminderStore';
import { useContactsStore } from '../store/contactsStore';
import { Reminder, ReminderType, ReminderStatus } from '../types';
import { useNavigation } from '@react-navigation/native';

const ReminderCenterScreen: React.FC = () => {
  const navigation = useNavigation();
  const toast = useToast();
  
  // Store状态
  const {
    isLoading,
    error,
    filterType,
    filterStatus,
    completeReminder,
    dismissReminder,
    snoozeReminder,
    generateBirthdayReminders,
    generateFollowUpReminders,
    generateSuggestions,
    setFilterType,
    setFilterStatus,
    initializeReminders,
  } = useReminderStore();

  const { contacts } = useContactsStore();
  const filteredReminders = useReminderSelectors.useFilteredReminders();
  const stats = useReminderSelectors.useReminderStats();
  const upcomingReminders = useReminderSelectors.useUpcomingReminders(7);
  const overdueReminders = useReminderSelectors.useOverdueReminders();

  // 本地状态
  const [refreshing, setRefreshing] = useState(false);
  const [selectedReminders, setSelectedReminders] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);

  // 初始化
  useEffect(() => {
    initializeReminders();
  }, []);

  // 自动生成提醒
  useEffect(() => {
    if (contacts.length > 0) {
      generateBirthdayReminders(contacts);
      generateFollowUpReminders(contacts);
      generateSuggestions(contacts);
    }
  }, [contacts]);

  // 处理刷新
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await generateBirthdayReminders(contacts);
      await generateFollowUpReminders(contacts);
      await generateSuggestions(contacts);
    } catch (error) {
      console.error('刷新提醒失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // 处理提醒操作
  const handleCompleteReminder = async (reminderId: string) => {
    try {
      await completeReminder(reminderId);
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>提醒已完成</ToastTitle>
          </Toast>
        ),
      });
    } catch (error) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>操作失败</ToastTitle>
            <ToastDescription>请重试</ToastDescription>
          </Toast>
        ),
      });
    }
  };

  const handleDismissReminder = async (reminderId: string) => {
    try {
      await dismissReminder(reminderId);
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>提醒已忽略</ToastTitle>
          </Toast>
        ),
      });
    } catch (error) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>操作失败</ToastTitle>
            <ToastDescription>请重试</ToastDescription>
          </Toast>
        ),
      });
    }
  };

  const handleSnoozeReminder = async (reminderId: string, hours: number = 1) => {
    try {
      const snoozeUntil = new Date(Date.now() + hours * 60 * 60 * 1000);
      await snoozeReminder(reminderId, snoozeUntil);
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>提醒已延期</ToastTitle>
            <ToastDescription>{hours}小时后再次提醒</ToastDescription>
          </Toast>
        ),
      });
    } catch (error) {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>操作失败</ToastTitle>
            <ToastDescription>请重试</ToastDescription>
          </Toast>
        ),
      });
    }
  };

  // 获取提醒类型图标
  const getReminderTypeIcon = (type: ReminderType) => {
    switch (type) {
      case 'birthday': return '🎂';
      case 'follow_up': return '💬';
      case 'meeting': return '📅';
      case 'anniversary': return '🎉';
      case 'task_deadline': return '⏰';
      case 'relationship_maintenance': return '🤝';
      default: return '📋';
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '$error600';
      case 'high': return '$warning600';
      case 'medium': return '$info600';
      case 'low': return '$success600';
      default: return '$textLight600';
    }
  };

  // 渲染统计卡片
  const renderStatsCard = () => (
    <Box p="$4" bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark900' } }}>
      <VStack space="md">
        <Heading size="md">提醒概览</Heading>
        <HStack space="md" justifyContent="space-between">
          <VStack alignItems="center" flex={1}>
            <Text size="2xl" fontWeight="$bold" color="$primary600">
              {stats.pending}
            </Text>
            <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
              待处理
            </Text>
          </VStack>
          <VStack alignItems="center" flex={1}>
            <Text size="2xl" fontWeight="$bold" color="$warning600">
              {stats.overdue}
            </Text>
            <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
              已逾期
            </Text>
          </VStack>
          <VStack alignItems="center" flex={1}>
            <Text size="2xl" fontWeight="$bold" color="$success600">
              {stats.completed}
            </Text>
            <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
              已完成
            </Text>
          </VStack>
          <VStack alignItems="center" flex={1}>
            <Text size="2xl" fontWeight="$bold" color="$info600">
              {stats.upcomingWeek}
            </Text>
            <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
              本周
            </Text>
          </VStack>
        </HStack>
      </VStack>
    </Box>
  );

  // 渲染过滤器
  const renderFilters = () => (
    <Box p="$4" borderBottomWidth="$1" borderBottomColor="$borderLight200" sx={{ _dark: { borderBottomColor: '$borderDark700' } }}>
      <VStack space="md">
        <HStack justifyContent="space-between" alignItems="center">
          <Heading size="sm">筛选条件</Heading>
          <Pressable onPress={() => setShowFilters(!showFilters)}>
            <StandardIcon as={Filter} size="md" />
          </Pressable>
        </HStack>
        
        {showFilters && (
          <VStack space="md">
            {/* 类型过滤 */}
            <VStack space="sm">
              <Text fontWeight="$medium">提醒类型</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <HStack space="sm">
                  {(['all', 'birthday', 'follow_up', 'meeting', 'anniversary'] as const).map((type) => (
                    <Pressable
                      key={type}
                      onPress={() => setFilterType(type)}
                      px="$3"
                      py="$2"
                      borderRadius="$full"
                      bg={filterType === type ? '$primary600' : '$backgroundLight100'}
                      borderWidth="$1"
                      borderColor={filterType === type ? '$primary600' : '$borderLight200'}
                      sx={{
                        _dark: {
                          bg: filterType === type ? '$primary500' : '$backgroundDark800',
                          borderColor: filterType === type ? '$primary500' : '$borderDark600',
                        }
                      }}
                    >
                      <Text
                        size="sm"
                        color={filterType === type ? 'white' : '$textLight700'}
                        sx={{
                          _dark: {
                            color: filterType === type ? 'white' : '$textDark300',
                          }
                        }}
                      >
                        {type === 'all' ? '全部' : 
                         type === 'birthday' ? '生日' :
                         type === 'follow_up' ? '联系' :
                         type === 'meeting' ? '会议' :
                         type === 'anniversary' ? '纪念日' : type}
                      </Text>
                    </Pressable>
                  ))}
                </HStack>
              </ScrollView>
            </VStack>

            {/* 状态过滤 */}
            <VStack space="sm">
              <Text fontWeight="$medium">状态</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <HStack space="sm">
                  {(['all', 'pending', 'completed', 'dismissed'] as const).map((status) => (
                    <Pressable
                      key={status}
                      onPress={() => setFilterStatus(status)}
                      px="$3"
                      py="$2"
                      borderRadius="$full"
                      bg={filterStatus === status ? '$primary600' : '$backgroundLight100'}
                      borderWidth="$1"
                      borderColor={filterStatus === status ? '$primary600' : '$borderLight200'}
                      sx={{
                        _dark: {
                          bg: filterStatus === status ? '$primary500' : '$backgroundDark800',
                          borderColor: filterStatus === status ? '$primary500' : '$borderDark600',
                        }
                      }}
                    >
                      <Text
                        size="sm"
                        color={filterStatus === status ? 'white' : '$textLight700'}
                        sx={{
                          _dark: {
                            color: filterStatus === status ? 'white' : '$textDark300',
                          }
                        }}
                      >
                        {status === 'all' ? '全部' : 
                         status === 'pending' ? '待处理' :
                         status === 'completed' ? '已完成' :
                         status === 'dismissed' ? '已忽略' : status}
                      </Text>
                    </Pressable>
                  ))}
                </HStack>
              </ScrollView>
            </VStack>
          </VStack>
        )}
      </VStack>
    </Box>
  );

  // 渲染提醒项
  const renderReminderItem = ({ item: reminder }: { item: Reminder }) => {
    const isOverdue = reminder.status === 'pending' && reminder.triggerDate < new Date();
    const contact = contacts.find(c => c.id === reminder.contactId);
    
    return (
      <Pressable
        onPress={() => {
          // TODO: 导航到提醒详情页面
          console.log('Navigate to reminder detail:', reminder.id);
        }}
        p="$4"
        borderBottomWidth="$1"
        borderBottomColor="$borderLight200"
        sx={{ _dark: { borderBottomColor: '$borderDark700' } }}
      >
        <HStack space="md" alignItems="flex-start">
          {/* 类型图标 */}
          <Box
            w="$12"
            h="$12"
            borderRadius="$full"
            bg={isOverdue ? '$error100' : '$primary100'}
            justifyContent="center"
            alignItems="center"
            sx={{
              _dark: {
                bg: isOverdue ? '$error900' : '$primary900',
              }
            }}
          >
            <Text fontSize="$lg">
              {getReminderTypeIcon(reminder.type)}
            </Text>
          </Box>

          {/* 内容 */}
          <VStack flex={1} space="xs">
            <HStack justifyContent="space-between" alignItems="flex-start">
              <VStack flex={1} space="xs">
                <Text 
                  fontWeight="$medium" 
                  color={isOverdue ? '$error600' : '$textLight900'}
                  sx={{ _dark: { color: isOverdue ? '$error400' : '$textDark100' } }}
                >
                  {reminder.title}
                </Text>
                {reminder.description && (
                  <Text 
                    size="sm" 
                    color="$textLight600" 
                    sx={{ _dark: { color: '$textDark400' } }}
                  >
                    {reminder.description}
                  </Text>
                )}
                {contact && (
                  <Text 
                    size="sm" 
                    color="$textLight500" 
                    sx={{ _dark: { color: '$textDark500' } }}
                  >
                    联系人: {contact.name}
                  </Text>
                )}
              </VStack>

              {/* 优先级标识 */}
              <Badge
                size="sm"
                variant="solid"
                bg={getPriorityColor(reminder.priority)}
              >
                <BadgeText color="white">
                  {reminder.priority === 'urgent' ? '紧急' :
                   reminder.priority === 'high' ? '高' :
                   reminder.priority === 'medium' ? '中' : '低'}
                </BadgeText>
              </Badge>
            </HStack>

            {/* 时间和状态 */}
            <HStack justifyContent="space-between" alignItems="center">
              <HStack space="xs" alignItems="center">
                <StandardIcon 
                  as={isOverdue ? AlertCircle : Clock} 
                  size="xs" 
                  color={isOverdue ? '$error600' : '$textLight500'} 
                />
                <Text 
                  size="xs" 
                  color={isOverdue ? '$error600' : '$textLight500'}
                  sx={{ _dark: { color: isOverdue ? '$error400' : '$textDark500' } }}
                >
                  {isOverdue ? '已逾期' : reminder.triggerDate.toLocaleDateString()}
                </Text>
              </HStack>

              {/* 操作按钮 */}
              {reminder.status === 'pending' && (
                <HStack space="xs">
                  <Pressable
                    onPress={() => handleCompleteReminder(reminder.id)}
                    p="$1"
                  >
                    <StandardIcon as={CheckCircle} size="sm" color="$success600" />
                  </Pressable>
                  <Pressable
                    onPress={() => handleSnoozeReminder(reminder.id, 1)}
                    p="$1"
                  >
                    <StandardIcon as={Clock} size="sm" color="$warning600" />
                  </Pressable>
                  <Pressable
                    onPress={() => handleDismissReminder(reminder.id)}
                    p="$1"
                  >
                    <StandardIcon as={X} size="sm" color="$error600" />
                  </Pressable>
                </HStack>
              )}
            </HStack>
          </VStack>
        </HStack>
      </Pressable>
    );
  };

  return (
    <ErrorBoundary>
      <Box flex={1} bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark950' } }}>
        {/* 头部 */}
        <Box
          p="$4"
          bg="$primary600"
          sx={{ _dark: { bg: '$primary700' } }}
        >
          <HStack justifyContent="space-between" alignItems="center">
            <VStack>
              <Heading size="lg" color="white">
                提醒中心
              </Heading>
              <Text size="sm" color="$primary100">
                {stats.pending} 个待处理提醒
              </Text>
            </VStack>
            <StandardIcon as={Bell} size="xl" color="white" />
          </HStack>
        </Box>

        {/* 统计概览 */}
        {renderStatsCard()}

        {/* 过滤器 */}
        {renderFilters()}

        {/* 提醒列表 */}
        <Box flex={1}>
          {filteredReminders.length > 0 ? (
            <FlatList
              data={filteredReminders}
              renderItem={renderReminderItem}
              keyExtractor={(item) => item.id}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={handleRefresh}
                  colors={['#4A78D9']}
                  tintColor="#4A78D9"
                />
              }
              contentContainerStyle={{ paddingBottom: 100 }}
            />
          ) : (
            <Box flex={1} justifyContent="center" alignItems="center" p="$8">
              <VStack space="lg" alignItems="center">
                <StandardIcon as={Bell} size="4xl" color="$textLight400" />
                <VStack space="sm" alignItems="center">
                  <Text 
                    textAlign="center" 
                    fontWeight="$medium" 
                    color="$textLight500" 
                    sx={{ _dark: { color: '$textDark500' } }}
                  >
                    {filterType === 'all' && filterStatus === 'all' 
                      ? '还没有任何提醒'
                      : '没有符合条件的提醒'
                    }
                  </Text>
                  <Text 
                    textAlign="center" 
                    color="$textLight400" 
                    sx={{ _dark: { color: '$textDark600' } }}
                  >
                    {filterType === 'all' && filterStatus === 'all'
                      ? '系统会自动为你生成生日和联系提醒'
                      : '尝试调整筛选条件'
                    }
                  </Text>
                </VStack>
              </VStack>
            </Box>
          )}
        </Box>

        {/* 浮动操作按钮 */}
        <Box position="absolute" bottom="$6" right="$4">
          <CircularButton
            icon={Plus}
            size="xl"
            variant="solid"
            action="primary"
            onPress={() => {
              // TODO: 导航到创建提醒页面
              console.log('Navigate to create reminder');
            }}
          />
        </Box>
      </Box>
    </ErrorBoundary>
  );
};

export default ReminderCenterScreen;
