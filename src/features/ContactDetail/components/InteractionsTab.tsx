import React, { useState } from 'react';
import { Platform } from 'react-native';
import {
  Box,
  Text,
  Input,
  InputField,
  InputIcon,
  InputSlot,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  Button,
  ButtonText,
  ButtonIcon,
  VStack,
  HStack,
  Icon,
  Pressable,
  ScrollView
} from '@gluestack-ui/themed';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { Search, Calendar, ChevronDownIcon, PlusCircle } from 'lucide-react-native'; // Users, Mail, Phone moved to InteractionItem
import { Interaction, ActionItem } from '../hooks/useContactDetail'; // Assuming Interaction type is exported from the hook
import InteractionItem from './InteractionItem';

interface InteractionsTabProps {
  interactions: Interaction[];
  onAddInteraction: () => void;
  onViewInteractionDetail: (id: string, title: string) => void;
  // Filter props
  filterType: Interaction['type'] | 'all';
  setFilterType: (type: Interaction['type'] | 'all') => void;
  filterDateRange: { startDate: Date | null; endDate: Date | null };
  setFilterDateRange: (range: { startDate: Date | null; endDate: Date | null }) => void;
  searchText: string;
  setSearchText: (text: string) => void;
}

const interactionTypeOptions: (Interaction['type'] | 'all')[] = ['all', 'meeting', 'call', 'email', 'note'];

const InteractionsTab: React.FC<InteractionsTabProps> = ({
  // ... (other props)
  filterType,
  setFilterType, 
  interactions, 
  onAddInteraction, 
  onViewInteractionDetail,
  // Filter props
  // filterType, // Removed duplicate
  // setFilterType, // Removed duplicate
  filterDateRange,
  setFilterDateRange,
  searchText,
  setSearchText,
}) => {
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [typePickerOpen, setTypePickerOpen] = useState(false);
  const [expandedMeetingId, setExpandedMeetingId] = useState<string | null>(null);

  const typeItemsForDropdown = interactionTypeOptions.map(type => ({
    label: type.charAt(0).toUpperCase() + type.slice(1),
    value: type,
  }));

  const handleStartDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setShowStartDatePicker(false); // Hide picker immediately
    if (event.type === 'set' && selectedDate) {
      // Ensure end date is not before new start date
      if (filterDateRange.endDate && selectedDate > filterDateRange.endDate) {
        setFilterDateRange({ startDate: selectedDate, endDate: null }); 
      } else {
        setFilterDateRange({ ...filterDateRange, startDate: selectedDate });
      }
    }
  };

  const handleEndDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setShowEndDatePicker(false); // Hide picker immediately
    if (event.type === 'set' && selectedDate) {
      setFilterDateRange({ ...filterDateRange, endDate: selectedDate });
    }
  };

  return (
    <ScrollView>
      <VStack space="md" p={'$4'}>
        <Input>
          <InputSlot pl="$3">
            <InputIcon as={Search} />
          </InputSlot>
          <InputField 
            placeholder="Search interactions..."
            value={searchText}
            onChangeText={setSearchText}
          />
        </Input>

        <Box>
          <HStack justifyContent="space-between" alignItems="center" mb={'$3'}>
            <Text fontSize={'$lg'} fontWeight={'$semibold'}>Interaction History</Text>
            <Pressable onPress={onAddInteraction} p={'$2'}>
              <Icon as={PlusCircle} size="xl" color="$primary500" />
            </Pressable>
          </HStack>

          <VStack space="md" p={'$3'} borderWidth={1} borderColor={'$backgroundLight200'} borderRadius={'$lg'}>
            <HStack alignItems="center" zIndex={10}>
              <Text w={'$20'} fontWeight={'$medium'}>Type:</Text>
              <Select flex={1} selectedValue={filterType} onValueChange={(value) => setFilterType(value as Interaction['type'] | 'all')}>
                <SelectTrigger variant="outline" size="md">
                  <SelectInput placeholder="Select a type" />
                  <SelectIcon mr={'$3'} as={ChevronDownIcon} />
                </SelectTrigger>
                <SelectPortal>
                  <SelectBackdrop />
                  <SelectContent>
                    <SelectDragIndicatorWrapper>
                      <SelectDragIndicator />
                    </SelectDragIndicatorWrapper>
                    {typeItemsForDropdown.map(item => (
                      <SelectItem key={item.value} label={item.label} value={item.value} />
                    ))}
                  </SelectContent>
                </SelectPortal>
              </Select>
            </HStack>

            <HStack alignItems="center">
              <Text w={'$20'} fontWeight={'$medium'}>Date Range:</Text>
              <HStack flex={1} space="sm">
                <Button variant="outline" size="sm" flex={1} onPress={() => setShowStartDatePicker(true)}>
                  <ButtonText>{filterDateRange.startDate ? filterDateRange.startDate.toLocaleDateString() : 'Start Date'}</ButtonText>
                  <ButtonIcon as={Calendar} ml={'$2'} />
                </Button>
                <Button variant="outline" size="sm" flex={1} onPress={() => setShowEndDatePicker(true)}>
                  <ButtonText>{filterDateRange.endDate ? filterDateRange.endDate.toLocaleDateString() : 'End Date'}</ButtonText>
                  <ButtonIcon as={Calendar} ml={'$2'} />
                </Button>
              </HStack>
            </HStack>
          </VStack>
        </Box>

        {showStartDatePicker && (
          <DateTimePicker
            value={filterDateRange.startDate || new Date()}
            mode="date"
            display="default"
            onChange={handleStartDateChange}
            maximumDate={filterDateRange.endDate || new Date()}
          />
        )}
        {showEndDatePicker && (
          <DateTimePicker
            value={filterDateRange.endDate || new Date()}
            mode="date"
            display="default"
            onChange={handleEndDateChange}
            minimumDate={filterDateRange.startDate}
          />
        )}

        {interactions.length > 0 ? (
          interactions.map(interaction => (
            <InteractionItem 
              key={interaction.id} 
              interaction={interaction} 
              onPress={() => onViewInteractionDetail(interaction.id, interaction.title)} 
              expanded={expandedMeetingId === interaction.id}
              onToggleExpand={() => setExpandedMeetingId(expandedMeetingId === interaction.id ? null : interaction.id)}
            />
          ))
        ) : (
          <Box mt={'$4'}>
            <Text textAlign='center' color={'$textLight500'}>No interactions recorded yet.</Text>
          </Box>
        )}
      </VStack>
    </ScrollView>
  );
};

export default InteractionsTab;
