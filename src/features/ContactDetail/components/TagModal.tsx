import React, { useState, useEffect } from 'react';
import {
  Modal as GSModal,
  ModalBackdrop,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  ModalCloseButton,
  ModalBody,
  Modal<PERSON>ooter,
  Heading,
  Text,
  Input,
  InputField,
  Button,
  ButtonText,
  ButtonIcon,
  Box,
  VStack,
  HStack,
  Pressable,
  Icon,
  ScrollView
} from '@gluestack-ui/themed';
import { Platform } from 'react-native';
import { useAlert } from '../../../context/AlertContext';
import { Tag } from '../hooks/useContactDetail'; // Assuming Tag interface is defined here
import { XCircle, Trash2, HelpCircle, CheckCircle } from 'lucide-react-native';

const PREDEFINED_TAG_TYPES = ['Project', 'Interest', 'Status', 'Relationship', 'Lead Quality', 'Custom'];
const PREDEFINED_TAG_COLORS = [
  '$primary500',
  '$purple500',
  '$orange500',
  '$emerald500',
  '$red500',
  '$yellow500', 
  '$teal500',
];

export interface TagFormData {
  name: string;
  type: string;
  color: string;
  // Include other optional fields from Tag interface if they can be edited/created
  projectId?: string;
  projectName?: string;
  opportunityId?: string;
  opportunityName?: string;
}

interface TagModalProps {
  visible: boolean;
  tag: Tag | null; // null for create mode, Tag object for edit mode
  onClose: () => void;
  onSave: (tagData: TagFormData & { id?: string }) => void; // id is present for existing tags
  onDelete?: (tagId: string) => void; // Optional: only if editing an existing tag
}

const TagModal: React.FC<TagModalProps> = ({ visible, tag, onClose, onSave, onDelete }) => {
  const { showAlert } = useAlert();
  const [tagName, setTagName] = useState('');
  const [selectedType, setSelectedType] = useState(PREDEFINED_TAG_TYPES[0]);
  const [selectedColor, setSelectedColor] = useState(PREDEFINED_TAG_COLORS[0]);

  const isEditMode = tag !== null;

  useEffect(() => {
    if (visible) {
      if (isEditMode && tag) {
        setTagName(tag.name);
        setSelectedType(tag.type || PREDEFINED_TAG_TYPES[0]);
        setSelectedColor(tag.color || PREDEFINED_TAG_COLORS[0]);
      } else {
        // Reset for create mode
        setTagName('');
        setSelectedType(PREDEFINED_TAG_TYPES[0]);
        setSelectedColor(PREDEFINED_TAG_COLORS[0]);
      }
    } else {
      // Optionally reset when modal is hidden, if desired for cleaner state transitions
      // setTagName('');
      // setSelectedType(PREDEFINED_TAG_TYPES[0]);
      // setSelectedColor(PREDEFINED_TAG_COLORS[0]);
    }
  }, [visible, tag, isEditMode]);

  const handleSave = () => {
    if (!tagName.trim()) {
      showAlert({
        title: 'Validation Error',
        message: 'Tag name cannot be empty.',
        buttons: [{ text: 'OK', onPress: () => {} }],
      });
      return;
    }
    const tagDataToSave: TagFormData & { id?: string } = {
      name: tagName.trim(),
      type: selectedType,
      color: selectedColor,
    };
    if (isEditMode && tag?.id) {
      tagDataToSave.id = tag.id;
      // Preserve other potential fields from the original tag if they are not editable in this form
      tagDataToSave.projectId = tag.projectId;
      tagDataToSave.projectName = tag.projectName;
      tagDataToSave.opportunityId = tag.opportunityId;
      tagDataToSave.opportunityName = tag.opportunityName;
    }
    onSave(tagDataToSave);
  };

  const handleDeleteConfirm = () => {
    if (onDelete && tag?.id) {
      onDelete(tag.id);
    }
  };

  const handleDelete = () => {
    if (isEditMode && tag?.id) {
      showAlert({
        title: 'Delete Tag',
        message: `Are you sure you want to delete "${tag?.name || 'this tag'}"? This action cannot be undone.`,
        buttons: [
          { text: 'Cancel', style: 'cancel', onPress: () => {} }, // Default cancel action, alert closes
          { text: 'Delete', style: 'destructive', onPress: handleDeleteConfirm },
        ],
      });
    } else {
      showAlert({
        title: 'Error',
        message: 'This tag cannot be deleted right now.',
        buttons: [{ text: 'OK', onPress: () => {} }],
      });
    }
  };
  
  if (!visible) return null;

  return (
    <GSModal isOpen={visible} onClose={onClose} finalFocusRef={undefined}>
      <ModalBackdrop />
      <ModalContent w={'$full'} maxW={'$lg'} m={'$4'}>
        <ModalHeader>
          <Heading size="lg">{isEditMode ? 'Edit Tag' : 'Create New Tag'}</Heading>
          <ModalCloseButton>
            <Icon as={XCircle} />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody>
          <ScrollView>
            <VStack space="md">
              <VStack space="xs">
                <Text size="sm" fontWeight="$medium">Tag Name</Text>
                <Input>
                  <InputField
                    placeholder="Enter tag name (e.g., VIP Client)"
                    value={tagName}
                    onChangeText={setTagName}
                    autoFocus={Platform.OS === 'web'} // Auto-focus on web for better UX
                  />
                </Input>
              </VStack>

              <VStack space="xs">
                <Text size="sm" fontWeight="$medium">Tag Type</Text>
                <HStack space="sm" flexWrap="wrap">
                  {PREDEFINED_TAG_TYPES.map((type) => (
                    <Button
                      key={type}
                      variant={selectedType === type ? "solid" : "outline"}
                      action={selectedType === type ? "primary" : "secondary"}
                      size="sm"
                      onPress={() => setSelectedType(type)}
                      mb={'$2'}
                    >
                      <ButtonText>{type}</ButtonText>
                    </Button>
                  ))}
                </HStack>
              </VStack>

              <VStack space="xs">
                <Text size="sm" fontWeight="$medium">Tag Color</Text>
                <HStack space="sm" flexWrap="wrap">
                  {PREDEFINED_TAG_COLORS.map((colorToken) => (
                    <Pressable
                      key={colorToken}
                      w={'$8'} 
                      h={'$8'} 
                      bg={colorToken} // Use Gluestack color token directly
                      borderRadius={'$full'}
                      onPress={() => setSelectedColor(colorToken)}
                      borderWidth={2}
                      borderColor={selectedColor === colorToken ? '$primary700' : '$backgroundLight0'}
                      justifyContent='center'
                      alignItems='center'
                      mb={'$2'}
                    >
                      {selectedColor === colorToken && <Icon as={CheckCircle} color='$white' size='sm' />}
                    </Pressable>
                  ))}
                </HStack>
              </VStack>

              {(selectedType === 'Project' || selectedType === 'Opportunity') && (
                <HStack alignItems='center' opacity={0.6} space='xs' mt={'$2'}>
                  <Icon as={HelpCircle} size='sm' color='$textLight500' />
                  <Text size='xs' color='$textLight500'>
                    {selectedType} linking will be available in a future update.
                  </Text>
                </HStack>
              )}
            </VStack>
          </ScrollView>
        </ModalBody>
        <ModalFooter>
          <HStack flex={1} justifyContent='space-between'>
            {isEditMode && onDelete && (
              <Button
                variant="solid"
                action="negative"
                onPress={handleDelete}
                mr={'$auto'} // Push delete button to the left
              >
                <ButtonIcon as={Trash2} mr={'$1'}/>
                <ButtonText>Delete</ButtonText>
              </Button>
            )}
            <HStack space='sm' ml={isEditMode && onDelete ? '$0' : '$auto'}> 
              <Button
                variant="outline"
                action="secondary"
                onPress={onClose}
              >
                <ButtonText>Cancel</ButtonText>
              </Button>
              <Button
                onPress={handleSave}
              >
                <ButtonText>Save</ButtonText>
              </Button>
            </HStack>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </GSModal>
  );
};

export default TagModal;
