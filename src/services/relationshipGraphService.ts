/**
 * 关系图谱服务
 * 管理联系人之间的关系网络，分析关系路径和共同联系人
 */

import { 
  Contact, 
  ContactRelationship, 
  ContactRelationshipType,
  NetworkGraph,
  NetworkGraphNode,
  NetworkGraphEdge,
  ContactRelationshipPath
} from '../types';

export interface RelationshipGraphAnalysis {
  totalNodes: number;
  totalEdges: number;
  maxDepth: number;
  strongConnections: number;
  mutualConnections: number;
  introducedConnections: number;
  networkDensity: number;
  clusteringCoefficient: number;
  centralityScores: Record<string, number>;
}

export interface PathAnalysis {
  shortestPath: string[];
  allPaths: string[][];
  pathStrengths: number[];
  commonConnections: string[];
}

export class RelationshipGraphService {
  
  /**
   * 构建以指定联系人为中心的关系网络图
   */
  buildNetworkGraph(
    centerContactId: string,
    contacts: Contact[],
    relationships: ContactRelationship[],
    maxDepth: number = 3
  ): NetworkGraph {
    const nodes: NetworkGraphNode[] = [];
    const edges: NetworkGraphEdge[] = [];
    const visitedNodes = new Set<string>();
    const nodeQueue: Array<{ id: string; level: number }> = [];

    // 添加中心节点
    const centerContact = contacts.find(c => c.id === centerContactId);
    if (!centerContact) {
      throw new Error(`Contact with ID ${centerContactId} not found`);
    }

    const centerNode: NetworkGraphNode = {
      id: centerContactId,
      name: centerContact.name,
      type: centerContactId === 'user' ? 'self' : 'contact',
      level: 0,
      avatar: centerContact.avatar,
      company: centerContact.company,
      position: centerContact.position,
      relationshipType: centerContact.relationshipType,
      connectionStrength: centerContact.connectionStrength,
    };

    nodes.push(centerNode);
    visitedNodes.add(centerContactId);
    nodeQueue.push({ id: centerContactId, level: 0 });

    // 广度优先搜索构建网络
    while (nodeQueue.length > 0) {
      const { id: currentId, level } = nodeQueue.shift()!;
      
      if (level >= maxDepth) continue;

      // 查找与当前节点相关的所有关系
      const relatedRelationships = relationships.filter(r => 
        (r.fromContactId === currentId || r.toContactId === currentId) && r.isActive
      );

      for (const relationship of relatedRelationships) {
        const relatedContactId = relationship.fromContactId === currentId 
          ? relationship.toContactId 
          : relationship.fromContactId;

        // 添加相关联系人节点
        if (!visitedNodes.has(relatedContactId)) {
          const relatedContact = contacts.find(c => c.id === relatedContactId);
          if (relatedContact) {
            const relatedNode: NetworkGraphNode = {
              id: relatedContactId,
              name: relatedContact.name,
              type: 'contact',
              level: level + 1,
              avatar: relatedContact.avatar,
              company: relatedContact.company,
              position: relatedContact.position,
              relationshipType: relatedContact.relationshipType,
              connectionStrength: relatedContact.connectionStrength,
            };

            nodes.push(relatedNode);
            visitedNodes.add(relatedContactId);
            nodeQueue.push({ id: relatedContactId, level: level + 1 });
          }
        }

        // 添加边
        const edge: NetworkGraphEdge = {
          id: relationship.id,
          fromId: relationship.fromContactId,
          toId: relationship.toContactId,
          relationshipType: relationship.relationshipType,
          strength: relationship.strength,
          isDirected: this.isDirectedRelationship(relationship.relationshipType),
          label: this.getRelationshipLabel(relationship.relationshipType),
        };

        edges.push(edge);
      }
    }

    return {
      nodes,
      edges,
      centerNodeId: centerContactId,
      maxDepth,
    };
  }

  /**
   * 分析两个联系人之间的关系路径
   */
  analyzeRelationshipPath(
    fromContactId: string,
    toContactId: string,
    contacts: Contact[],
    relationships: ContactRelationship[]
  ): PathAnalysis {
    const graph = this.buildAdjacencyList(relationships);
    const shortestPath = this.findShortestPath(graph, fromContactId, toContactId);
    const allPaths = this.findAllPaths(graph, fromContactId, toContactId, 4); // 最多4跳
    
    // 计算路径强度
    const pathStrengths = allPaths.map(path => this.calculatePathStrength(path, relationships));
    
    // 找到共同联系人
    const commonConnections = this.findCommonConnections(fromContactId, toContactId, relationships);

    return {
      shortestPath,
      allPaths,
      pathStrengths,
      commonConnections,
    };
  }

  /**
   * 发现共同联系人
   */
  findMutualConnections(
    contactId: string,
    relationships: ContactRelationship[]
  ): string[] {
    const directConnections = new Set<string>();
    
    // 获取直接连接的联系人
    relationships
      .filter(r => r.isActive && (r.fromContactId === contactId || r.toContactId === contactId))
      .forEach(r => {
        const connectedId = r.fromContactId === contactId ? r.toContactId : r.fromContactId;
        directConnections.add(connectedId);
      });

    const mutualConnections: Record<string, string[]> = {};

    // 对每个直接连接，找到他们的连接
    directConnections.forEach(connectedId => {
      const theirConnections = relationships
        .filter(r => r.isActive && (r.fromContactId === connectedId || r.toContactId === connectedId))
        .map(r => r.fromContactId === connectedId ? r.toContactId : r.fromContactId)
        .filter(id => id !== contactId && directConnections.has(id));

      if (theirConnections.length > 0) {
        mutualConnections[connectedId] = theirConnections;
      }
    });

    // 返回所有共同联系人
    const allMutual = new Set<string>();
    Object.values(mutualConnections).forEach(connections => {
      connections.forEach(id => allMutual.add(id));
    });

    return Array.from(allMutual);
  }

  /**
   * 分析网络图谱统计信息
   */
  analyzeNetworkGraph(graph: NetworkGraph, relationships: ContactRelationship[]): RelationshipGraphAnalysis {
    const totalNodes = graph.nodes.length;
    const totalEdges = graph.edges.length;
    const maxDepth = Math.max(...graph.nodes.map(n => n.level));

    // 计算强连接数量
    const strongConnections = graph.edges.filter(e => e.strength > 70).length;

    // 计算共同连接数量
    const mutualConnections = graph.edges.filter(e => 
      e.relationshipType === 'mutual_friend'
    ).length;

    // 计算介绍关系数量
    const introducedConnections = graph.edges.filter(e => 
      e.relationshipType === 'introduced_by' || e.relationshipType === 'introduced_to'
    ).length;

    // 计算网络密度
    const maxPossibleEdges = totalNodes * (totalNodes - 1) / 2;
    const networkDensity = maxPossibleEdges > 0 ? totalEdges / maxPossibleEdges : 0;

    // 计算聚类系数（简化版本）
    const clusteringCoefficient = this.calculateClusteringCoefficient(graph);

    // 计算中心性得分
    const centralityScores = this.calculateCentralityScores(graph);

    return {
      totalNodes,
      totalEdges,
      maxDepth,
      strongConnections,
      mutualConnections,
      introducedConnections,
      networkDensity,
      clusteringCoefficient,
      centralityScores,
    };
  }

  /**
   * 推荐潜在的关系连接
   */
  recommendConnections(
    contactId: string,
    contacts: Contact[],
    relationships: ContactRelationship[]
  ): Array<{ contactId: string; score: number; reason: string }> {
    const recommendations: Array<{ contactId: string; score: number; reason: string }> = [];
    const directConnections = new Set<string>();

    // 获取直接连接
    relationships
      .filter(r => r.isActive && (r.fromContactId === contactId || r.toContactId === contactId))
      .forEach(r => {
        const connectedId = r.fromContactId === contactId ? r.toContactId : r.fromContactId;
        directConnections.add(connectedId);
      });

    // 分析二度连接
    directConnections.forEach(firstDegreeId => {
      relationships
        .filter(r => r.isActive && (r.fromContactId === firstDegreeId || r.toContactId === firstDegreeId))
        .forEach(r => {
          const secondDegreeId = r.fromContactId === firstDegreeId ? r.toContactId : r.fromContactId;
          
          if (secondDegreeId !== contactId && !directConnections.has(secondDegreeId)) {
            const contact = contacts.find(c => c.id === secondDegreeId);
            const introducer = contacts.find(c => c.id === firstDegreeId);
            
            if (contact && introducer) {
              let score = r.strength * 0.7; // 基础分数基于关系强度
              
              // 同行业加分
              if (contact.company && contacts.find(c => c.id === contactId)?.company === contact.company) {
                score += 20;
              }
              
              // 同类型关系加分
              if (contact.relationshipType === contacts.find(c => c.id === contactId)?.relationshipType) {
                score += 15;
              }

              recommendations.push({
                contactId: secondDegreeId,
                score,
                reason: `通过 ${introducer.name} 认识`,
              });
            }
          }
        });
    });

    // 按分数排序并返回前10个
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, 10);
  }

  // 私有辅助方法
  private buildAdjacencyList(relationships: ContactRelationship[]): Record<string, string[]> {
    const graph: Record<string, string[]> = {};
    
    relationships.filter(r => r.isActive).forEach(r => {
      if (!graph[r.fromContactId]) graph[r.fromContactId] = [];
      if (!graph[r.toContactId]) graph[r.toContactId] = [];
      
      graph[r.fromContactId].push(r.toContactId);
      graph[r.toContactId].push(r.fromContactId);
    });

    return graph;
  }

  private findShortestPath(graph: Record<string, string[]>, start: string, end: string): string[] {
    if (start === end) return [start];
    
    const queue: Array<{ node: string; path: string[] }> = [{ node: start, path: [start] }];
    const visited = new Set<string>();

    while (queue.length > 0) {
      const { node, path } = queue.shift()!;
      
      if (visited.has(node)) continue;
      visited.add(node);

      const neighbors = graph[node] || [];
      for (const neighbor of neighbors) {
        if (neighbor === end) {
          return [...path, neighbor];
        }
        
        if (!visited.has(neighbor)) {
          queue.push({ node: neighbor, path: [...path, neighbor] });
        }
      }
    }

    return []; // 没有找到路径
  }

  private findAllPaths(
    graph: Record<string, string[]>, 
    start: string, 
    end: string, 
    maxDepth: number
  ): string[][] {
    const paths: string[][] = [];
    
    const dfs = (current: string, target: string, path: string[], visited: Set<string>, depth: number) => {
      if (depth > maxDepth) return;
      if (current === target) {
        paths.push([...path]);
        return;
      }

      const neighbors = graph[current] || [];
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          visited.add(neighbor);
          path.push(neighbor);
          dfs(neighbor, target, path, visited, depth + 1);
          path.pop();
          visited.delete(neighbor);
        }
      }
    };

    const visited = new Set<string>([start]);
    dfs(start, end, [start], visited, 0);
    
    return paths;
  }

  private calculatePathStrength(path: string[], relationships: ContactRelationship[]): number {
    if (path.length < 2) return 0;
    
    let totalStrength = 0;
    for (let i = 0; i < path.length - 1; i++) {
      const relationship = relationships.find(r => 
        (r.fromContactId === path[i] && r.toContactId === path[i + 1]) ||
        (r.fromContactId === path[i + 1] && r.toContactId === path[i])
      );
      
      if (relationship) {
        totalStrength += relationship.strength;
      }
    }
    
    return totalStrength / (path.length - 1); // 平均强度
  }

  private findCommonConnections(
    contactId1: string,
    contactId2: string,
    relationships: ContactRelationship[]
  ): string[] {
    const connections1 = new Set<string>();
    const connections2 = new Set<string>();

    relationships.filter(r => r.isActive).forEach(r => {
      if (r.fromContactId === contactId1) connections1.add(r.toContactId);
      if (r.toContactId === contactId1) connections1.add(r.fromContactId);
      if (r.fromContactId === contactId2) connections2.add(r.toContactId);
      if (r.toContactId === contactId2) connections2.add(r.fromContactId);
    });

    return Array.from(connections1).filter(id => connections2.has(id));
  }

  private calculateClusteringCoefficient(graph: NetworkGraph): number {
    // 简化的聚类系数计算
    let totalCoefficient = 0;
    let nodeCount = 0;

    graph.nodes.forEach(node => {
      const neighbors = graph.edges
        .filter(e => e.fromId === node.id || e.toId === node.id)
        .map(e => e.fromId === node.id ? e.toId : e.fromId);

      if (neighbors.length < 2) return;

      let edgesBetweenNeighbors = 0;
      for (let i = 0; i < neighbors.length; i++) {
        for (let j = i + 1; j < neighbors.length; j++) {
          const hasEdge = graph.edges.some(e => 
            (e.fromId === neighbors[i] && e.toId === neighbors[j]) ||
            (e.fromId === neighbors[j] && e.toId === neighbors[i])
          );
          if (hasEdge) edgesBetweenNeighbors++;
        }
      }

      const maxPossibleEdges = neighbors.length * (neighbors.length - 1) / 2;
      const coefficient = maxPossibleEdges > 0 ? edgesBetweenNeighbors / maxPossibleEdges : 0;
      
      totalCoefficient += coefficient;
      nodeCount++;
    });

    return nodeCount > 0 ? totalCoefficient / nodeCount : 0;
  }

  private calculateCentralityScores(graph: NetworkGraph): Record<string, number> {
    const scores: Record<string, number> = {};
    
    // 度中心性（简化版本）
    graph.nodes.forEach(node => {
      const degree = graph.edges.filter(e => e.fromId === node.id || e.toId === node.id).length;
      scores[node.id] = degree;
    });

    return scores;
  }

  private isDirectedRelationship(type: ContactRelationshipType): boolean {
    return ['introduced_by', 'introduced_to', 'mentor_mentee'].includes(type);
  }

  private getRelationshipLabel(type: ContactRelationshipType): string {
    const labels: Record<ContactRelationshipType, string> = {
      introduced_by: '介绍',
      introduced_to: '被介绍',
      mutual_friend: '共同朋友',
      colleague: '同事',
      family_friend: '家庭朋友',
      business_partner: '商业伙伴',
      mentor_mentee: '师生',
      client_vendor: '客户关系',
      other: '其他',
    };
    
    return labels[type] || type;
  }
}

// 导出单例实例
export const relationshipGraphService = new RelationshipGraphService();
export default relationshipGraphService;
