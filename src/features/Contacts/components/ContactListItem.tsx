import React from 'react';
import { Pressable, HStack, VStack, Avatar, Text, Icon, AvatarFallbackText, AvatarImage, Badge, BadgeText } from '@gluestack-ui/themed';
import { ChevronRight, Heart, Star } from 'lucide-react-native';
import { Contact } from '../../../types';
import { useContactsStore } from '../../../store';

interface ContactListItemProps {
  contact: Contact;
  onPress: () => void;
  showFavoriteButton?: boolean;
}

const ContactListItem: React.FC<ContactListItemProps> = ({
  contact,
  onPress,
  showFavoriteButton = true
}) => {
  const { toggleFavorite } = useContactsStore();

  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleToggleFavorite = (e: any) => {
    e.stopPropagation(); // Prevent triggering onPress
    toggleFavorite(contact.id);
  };

  return (
    <Pressable
      onPress={onPress}
      p="$4"
      borderBottomWidth="$1"
      borderColor="$trueGray200"
      sx={{
        ':hover': {
          bg: '$trueGray100',
        },
        '_dark': {
          borderColor: '$trueGray700',
          ':hover': {
            bg: '$trueGray800',
          },
        },
      }}
    >
      <HStack space="md" alignItems="center">
        <Avatar size="md" bg="$primary300">
          <AvatarFallbackText>{getInitials(contact.name)}</AvatarFallbackText>
          {contact.avatar && <AvatarImage source={{ uri: contact.avatar }} alt={contact.name} />}
        </Avatar>

        <VStack flex={1} space="xs">
          <HStack alignItems="center" space="sm">
            <Text size="md" fontWeight="$bold" color="$coolGray800" sx={{ _dark: { color: '$coolGray100' } }}>
              {contact.name}
            </Text>
            {contact.isFavorite && (
              <Icon as={Star} size="sm" color="$yellow500" />
            )}
          </HStack>

          <Text size="sm" color="$coolGray600" sx={{ _dark: { color: '$coolGray400' } }}>
            {contact.company && contact.position
              ? `${contact.position} at ${contact.company}`
              : contact.company || contact.position || 'No company info'
            }
          </Text>

          {contact.email && (
            <Text size="xs" color="$coolGray500" sx={{ _dark: { color: '$coolGray500' } }}>
              {contact.email}
            </Text>
          )}

          {contact.tags && contact.tags.length > 0 && (
            <HStack space="xs" flexWrap="wrap">
              {contact.tags.slice(0, 2).map((tag, index) => (
                <Badge key={index} size="sm" variant="outline" action="muted">
                  <BadgeText>{typeof tag === 'string' ? tag : tag.name}</BadgeText>
                </Badge>
              ))}
              {contact.tags.length > 2 && (
                <Badge size="sm" variant="outline" action="muted">
                  <BadgeText>+{contact.tags.length - 2}</BadgeText>
                </Badge>
              )}
            </HStack>
          )}
        </VStack>

        <HStack alignItems="center" space="sm">
          {showFavoriteButton && (
            <Pressable onPress={handleToggleFavorite} p="$2">
              <Icon
                as={Heart}
                size="sm"
                color={contact.isFavorite ? "$red500" : "$coolGray400"}
                fill={contact.isFavorite ? "$red500" : "transparent"}
              />
            </Pressable>
          )}
          <Icon as={ChevronRight} color="$coolGray400" />
        </HStack>
      </HStack>
    </Pressable>
  );
};

export default ContactListItem;
