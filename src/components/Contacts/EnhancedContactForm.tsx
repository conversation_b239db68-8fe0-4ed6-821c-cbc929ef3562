import React, { useState } from 'react';
import {
  VStack,
  HStack,
  Box,
  Text,
  Heading,
  Input,
  InputField,
  Button,
  ButtonText,
  ButtonIcon,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  Textarea,
  TextareaInput,
  Switch,
  Pressable,
  Icon,
  Badge,
  BadgeText,
  ScrollView,
  Divider,
} from '@gluestack-ui/themed';
import {
  Plus,
  Minus,
  ChevronDown,
  Phone,
  Mail,
  MapPin,
  Globe,
  Calendar,
  Tag,
  Star,
  Heart,
} from 'lucide-react-native';
import { 
  Contact, 
  ContactMethod, 
  Address, 
  SocialLink, 
  ImportantDate, 
  CustomField,
  RelationshipType,
  ContactPriority 
} from '../../types';
import { useContactGroupsSelectors } from '../../store/contactGroupsStore';

interface EnhancedContactFormProps {
  contact?: Contact;
  onSave: (contactData: any) => void;
  onCancel: () => void;
}

/**
 * 增强的联系人表单组件
 * 支持多个电话、邮箱、地址、社交链接等
 */
const EnhancedContactForm: React.FC<EnhancedContactFormProps> = ({
  contact,
  onSave,
  onCancel,
}) => {
  // 基本信息
  const [firstName, setFirstName] = useState(contact?.firstName || '');
  const [lastName, setLastName] = useState(contact?.lastName || '');
  const [company, setCompany] = useState(contact?.company || '');
  const [position, setPosition] = useState(contact?.position || '');
  const [notes, setNotes] = useState(contact?.notes || '');

  // 联系方式
  const [phones, setPhones] = useState<ContactMethod[]>(
    contact?.phones || [{ id: '1', type: 'phone', label: 'mobile', value: '', isPrimary: true }]
  );
  const [emails, setEmails] = useState<ContactMethod[]>(
    contact?.emails || [{ id: '1', type: 'email', label: 'work', value: '', isPrimary: true }]
  );
  const [addresses, setAddresses] = useState<Address[]>(contact?.addresses || []);

  // 社交链接
  const [socialLinks, setSocialLinks] = useState<SocialLink[]>(contact?.socialLinks || []);

  // 重要日期
  const [importantDates, setImportantDates] = useState<ImportantDate[]>(contact?.importantDates || []);

  // 自定义字段
  const [customFields, setCustomFields] = useState<CustomField[]>(contact?.customFields || []);

  // 分类信息
  const [relationshipType, setRelationshipType] = useState<RelationshipType>(
    contact?.relationshipType || 'acquaintance'
  );
  const [priority, setPriority] = useState<ContactPriority>(contact?.priority || 'medium');
  const [selectedGroupIds, setSelectedGroupIds] = useState<string[]>(contact?.groupIds || []);
  const [tags, setTags] = useState<string[]>(contact?.tags || []);
  const [isFavorite, setIsFavorite] = useState(contact?.isFavorite || false);

  // 获取分组数据
  const groups = useContactGroupsSelectors.useGroups();

  // 添加联系方式
  const addContactMethod = (type: 'phone' | 'email') => {
    const newMethod: ContactMethod = {
      id: Date.now().toString(),
      type,
      label: type === 'phone' ? 'mobile' : 'work',
      value: '',
      isPrimary: false,
    };

    if (type === 'phone') {
      setPhones([...phones, newMethod]);
    } else {
      setEmails([...emails, newMethod]);
    }
  };

  // 更新联系方式
  const updateContactMethod = (
    id: string,
    field: keyof ContactMethod,
    value: any,
    type: 'phone' | 'email'
  ) => {
    const updateArray = type === 'phone' ? phones : emails;
    const setArray = type === 'phone' ? setPhones : setEmails;

    const updated = updateArray.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        
        // 如果设置为主要，其他设为非主要
        if (field === 'isPrimary' && value) {
          updateArray.forEach(other => {
            if (other.id !== id) other.isPrimary = false;
          });
        }
        
        return updatedItem;
      }
      return item;
    });

    setArray(updated);
  };

  // 删除联系方式
  const removeContactMethod = (id: string, type: 'phone' | 'email') => {
    if (type === 'phone') {
      const filtered = phones.filter(p => p.id !== id);
      if (filtered.length === 0) {
        // 至少保留一个
        filtered.push({ id: Date.now().toString(), type: 'phone', label: 'mobile', value: '', isPrimary: true });
      }
      setPhones(filtered);
    } else {
      const filtered = emails.filter(e => e.id !== id);
      if (filtered.length === 0) {
        // 至少保留一个
        filtered.push({ id: Date.now().toString(), type: 'email', label: 'work', value: '', isPrimary: true });
      }
      setEmails(filtered);
    }
  };

  // 添加地址
  const addAddress = () => {
    const newAddress: Address = {
      id: Date.now().toString(),
      label: 'home',
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: '',
      isPrimary: addresses.length === 0,
    };
    setAddresses([...addresses, newAddress]);
  };

  // 处理保存
  const handleSave = () => {
    const contactData = {
      name: `${firstName} ${lastName}`.trim(),
      firstName,
      lastName,
      company,
      position,
      notes,
      phones: phones.filter(p => p.value.trim()),
      emails: emails.filter(e => e.value.trim()),
      addresses: addresses.filter(a => a.street?.trim() || a.city?.trim()),
      socialLinks,
      importantDates,
      customFields: customFields.filter(f => f.name.trim() && f.value.trim()),
      relationshipType,
      priority,
      groupIds: selectedGroupIds,
      tags,
      isFavorite,
      // 向后兼容
      email: emails.find(e => e.isPrimary)?.value || emails[0]?.value,
      phone: phones.find(p => p.isPrimary)?.value || phones[0]?.value,
    };

    onSave(contactData);
  };

  const renderContactMethodSection = (
    title: string,
    icon: any,
    methods: ContactMethod[],
    type: 'phone' | 'email'
  ) => (
    <VStack space="md">
      <HStack justifyContent="space-between" alignItems="center">
        <HStack space="sm" alignItems="center">
          <Icon as={icon} size="sm" color="$primary600" />
          <Text fontWeight="$medium">{title}</Text>
        </HStack>
        <Button size="xs" variant="outline" onPress={() => addContactMethod(type)}>
          <ButtonIcon as={Plus} />
        </Button>
      </HStack>

      {methods.map((method, index) => (
        <HStack key={method.id} space="sm" alignItems="center">
          <Select
            selectedValue={method.label}
            onValueChange={(value) => updateContactMethod(method.id, 'label', value, type)}
          >
            <SelectTrigger variant="outline" size="sm" flex={0.3}>
              <SelectInput placeholder="类型" />
              <SelectIcon as={ChevronDown} />
            </SelectTrigger>
            <SelectPortal>
              <SelectBackdrop />
              <SelectContent>
                <SelectDragIndicatorWrapper>
                  <SelectDragIndicator />
                </SelectDragIndicatorWrapper>
                {type === 'phone' ? (
                  <>
                    <SelectItem label="手机" value="mobile" />
                    <SelectItem label="工作" value="work" />
                    <SelectItem label="家庭" value="home" />
                    <SelectItem label="其他" value="other" />
                  </>
                ) : (
                  <>
                    <SelectItem label="工作" value="work" />
                    <SelectItem label="个人" value="home" />
                    <SelectItem label="其他" value="other" />
                  </>
                )}
              </SelectContent>
            </SelectPortal>
          </Select>

          <Input flex={1}>
            <InputField
              placeholder={type === 'phone' ? '电话号码' : '邮箱地址'}
              value={method.value}
              onChangeText={(value) => updateContactMethod(method.id, 'value', value, type)}
              keyboardType={type === 'phone' ? 'phone-pad' : 'email-address'}
            />
          </Input>

          <Pressable
            onPress={() => updateContactMethod(method.id, 'isPrimary', !method.isPrimary, type)}
            p="$2"
          >
            <Icon
              as={Star}
              size="sm"
              color={method.isPrimary ? '$yellow500' : '$textLight400'}
              fill={method.isPrimary ? '$yellow500' : 'transparent'}
            />
          </Pressable>

          {methods.length > 1 && (
            <Pressable onPress={() => removeContactMethod(method.id, type)} p="$2">
              <Icon as={Minus} size="sm" color="$error600" />
            </Pressable>
          )}
        </HStack>
      ))}
    </VStack>
  );

  return (
    <ScrollView flex={1} p="$4">
      <VStack space="lg">
        {/* 基本信息 */}
        <VStack space="md">
          <Heading size="md">基本信息</Heading>
          
          <HStack space="sm">
            <Input flex={1}>
              <InputField
                placeholder="名字"
                value={firstName}
                onChangeText={setFirstName}
              />
            </Input>
            <Input flex={1}>
              <InputField
                placeholder="姓氏"
                value={lastName}
                onChangeText={setLastName}
              />
            </Input>
          </HStack>

          <Input>
            <InputField
              placeholder="公司"
              value={company}
              onChangeText={setCompany}
            />
          </Input>

          <Input>
            <InputField
              placeholder="职位"
              value={position}
              onChangeText={setPosition}
            />
          </Input>
        </VStack>

        <Divider />

        {/* 联系方式 */}
        {renderContactMethodSection('电话号码', Phone, phones, 'phone')}
        
        <Divider />
        
        {renderContactMethodSection('邮箱地址', Mail, emails, 'email')}

        <Divider />

        {/* 分类信息 */}
        <VStack space="md">
          <Heading size="md">分类信息</Heading>
          
          <HStack space="sm">
            <VStack flex={1} space="sm">
              <Text size="sm" fontWeight="$medium">关系类型</Text>
              <Select selectedValue={relationshipType} onValueChange={setRelationshipType}>
                <SelectTrigger variant="outline">
                  <SelectInput placeholder="选择关系" />
                  <SelectIcon as={ChevronDown} />
                </SelectTrigger>
                <SelectPortal>
                  <SelectBackdrop />
                  <SelectContent>
                    <SelectItem label="同事" value="colleague" />
                    <SelectItem label="朋友" value="friend" />
                    <SelectItem label="家人" value="family" />
                    <SelectItem label="客户" value="client" />
                    <SelectItem label="供应商" value="vendor" />
                    <SelectItem label="导师" value="mentor" />
                    <SelectItem label="学员" value="mentee" />
                    <SelectItem label="熟人" value="acquaintance" />
                    <SelectItem label="其他" value="other" />
                  </SelectContent>
                </SelectPortal>
              </Select>
            </VStack>

            <VStack flex={1} space="sm">
              <Text size="sm" fontWeight="$medium">重要程度</Text>
              <Select selectedValue={priority} onValueChange={setPriority}>
                <SelectTrigger variant="outline">
                  <SelectInput placeholder="选择优先级" />
                  <SelectIcon as={ChevronDown} />
                </SelectTrigger>
                <SelectPortal>
                  <SelectBackdrop />
                  <SelectContent>
                    <SelectItem label="低" value="low" />
                    <SelectItem label="中" value="medium" />
                    <SelectItem label="高" value="high" />
                    <SelectItem label="关键" value="critical" />
                  </SelectContent>
                </SelectPortal>
              </Select>
            </VStack>
          </HStack>

          <HStack justifyContent="space-between" alignItems="center">
            <Text fontWeight="$medium">收藏联系人</Text>
            <Switch
              value={isFavorite}
              onValueChange={setIsFavorite}
              trackColor={{ false: '$backgroundLight300', true: '$primary500' }}
            />
          </HStack>
        </VStack>

        <Divider />

        {/* 备注 */}
        <VStack space="md">
          <Text fontWeight="$medium">备注</Text>
          <Textarea>
            <TextareaInput
              placeholder="添加备注..."
              value={notes}
              onChangeText={setNotes}
              numberOfLines={4}
            />
          </Textarea>
        </VStack>

        {/* 操作按钮 */}
        <HStack space="md" mt="$6">
          <Button variant="outline" onPress={onCancel} flex={1}>
            <ButtonText>取消</ButtonText>
          </Button>
          <Button onPress={handleSave} flex={1}>
            <ButtonText>保存</ButtonText>
          </Button>
        </HStack>
      </VStack>
    </ScrollView>
  );
};

export default EnhancedContactForm;
