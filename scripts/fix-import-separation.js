#!/usr/bin/env node

/**
 * 检查并修复项目中混合导入的问题
 * 分离 React Native 原生组件和 Gluestack UI 组件的导入
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Gluestack UI 组件列表
const GLUESTACK_UI_COMPONENTS = [
  'Box', 'Text', 'Heading', 'VStack', 'HStack', 'Button', 'Input',
  'Avatar', 'AvatarImage', 'AvatarFallbackText', 'Badge', 'Card',
  'Checkbox', 'Divider', 'FormControl', 'Image', 'Link', 'Modal',
  'Popover', 'Progress', 'Radio', 'Select', 'Slider', 'Spinner',
  'Switch', 'Toast', 'Tooltip', 'ActionSheet', 'AlertDialog',
  'Center', 'CircularProgress', 'Fab', 'Icon', 'IconButton',
  'LinearGradient', 'Pressable', 'SafeAreaView', 'StatusBar'
];

// React Native 原生组件列表
const REACT_NATIVE_COMPONENTS = [
  'View', 'ScrollView', 'FlatList', 'SectionList', 'VirtualizedList',
  'Alert', 'Dimensions', 'Keyboard', 'Linking', 'Platform',
  'ActivityIndicator', 'DrawerLayoutAndroid', 'KeyboardAvoidingView',
  'Modal', 'RefreshControl', 'StatusBar', 'WebView', 'Animated',
  'Easing', 'Image', 'ImageBackground', 'TouchableHighlight',
  'TouchableOpacity', 'TouchableWithoutFeedback', 'StyleSheet'
];

function fixImportSeparation(content) {
  const lines = content.split('\n');
  let modifiedContent = content;
  let hasChanges = false;
  
  // 查找 react-native 导入语句
  const reactNativeImportRegex = /import\s*\{([^}]+)\}\s*from\s*['"`]react-native['"`];?/;
  const match = content.match(reactNativeImportRegex);
  
  if (!match) {
    return { content, hasChanges: false };
  }
  
  const importedItems = match[1]
    .split(',')
    .map(item => item.trim())
    .filter(Boolean);
  
  // 分离 React Native 和 Gluestack UI 组件
  const reactNativeItems = [];
  const gluestackItems = [];
  const otherItems = [];
  
  importedItems.forEach(item => {
    const cleanItem = item.trim();
    if (REACT_NATIVE_COMPONENTS.includes(cleanItem)) {
      reactNativeItems.push(cleanItem);
    } else if (GLUESTACK_UI_COMPONENTS.includes(cleanItem)) {
      gluestackItems.push(cleanItem);
    } else {
      // 默认认为是 React Native 的
      reactNativeItems.push(cleanItem);
    }
  });
  
  // 如果没有混合导入，不需要修改
  if (gluestackItems.length === 0) {
    return { content, hasChanges: false };
  }
  
  // 构建新的导入语句
  let newImports = [];
  
  // React Native 导入
  if (reactNativeItems.length > 0) {
    const reactNativeImport = `import {\n  ${reactNativeItems.join(',\n  ')}\n} from 'react-native';`;
    newImports.push(reactNativeImport);
  }
  
  // Gluestack UI 导入
  if (gluestackItems.length > 0) {
    const gluestackImport = `import {\n  ${gluestackItems.join(',\n  ')}\n} from '@gluestack-ui/themed';`;
    newImports.push(gluestackImport);
  }
  
  // 替换原有的导入语句
  modifiedContent = modifiedContent.replace(reactNativeImportRegex, newImports.join('\n'));
  hasChanges = true;
  
  return { 
    content: modifiedContent, 
    hasChanges,
    reactNativeItems,
    gluestackItems
  };
}

// 处理单个文件
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { 
      content: newContent, 
      hasChanges, 
      reactNativeItems = [],
      gluestackItems = []
    } = fixImportSeparation(content);
    
    if (hasChanges) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      console.log(`   - React Native: ${reactNativeItems.join(', ')}`);
      console.log(`   - Gluestack UI: ${gluestackItems.join(', ')}`);
      return { fixed: true, reactNativeItems, gluestackItems };
    }
    
    return { fixed: false, reactNativeItems: [], gluestackItems: [] };
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return { fixed: false, reactNativeItems: [], gluestackItems: [] };
  }
}

// 主函数
function main() {
  console.log('🔧 检查并修复项目中的导入分离问题...\n');
  
  // 查找所有 TypeScript/JavaScript 文件
  const srcPattern = path.join(__dirname, '../src/**/*.{ts,tsx,js,jsx}');
  const files = glob.sync(srcPattern);
  
  let processedCount = 0;
  let modifiedCount = 0;
  
  const results = [];
  
  files.forEach(filePath => {
    processedCount++;
    const result = processFile(filePath);
    
    if (result.fixed) {
      modifiedCount++;
      results.push({
        file: path.relative(path.join(__dirname, '..'), filePath),
        reactNativeItems: result.reactNativeItems,
        gluestackItems: result.gluestackItems
      });
    }
  });
  
  console.log(`\n📊 检查完成:`);
  console.log(`   - 扫描文件: ${processedCount}`);
  console.log(`   - 修复文件: ${modifiedCount}`);
  
  if (results.length > 0) {
    console.log('\n📋 修复详情:');
    results.forEach(result => {
      console.log(`   🔧 ${result.file}`);
      console.log(`      React Native: ${result.reactNativeItems.join(', ')}`);
      console.log(`      Gluestack UI: ${result.gluestackItems.join(', ')}`);
    });
  }
  
  if (modifiedCount > 0) {
    console.log('\n✅ 修复完成！所有导入现在都正确分离了。');
  } else {
    console.log('\n✅ 所有文件的导入都是正确分离的！');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { fixImportSeparation, processFile };
