import React, { useState } from 'react';
import {
  VS<PERSON>ck,
  HStack,
  Box,
  Text,
  Pressable,
  Icon,
  Badge,
  BadgeText,
  ScrollView,
  Button,
  ButtonText,
  ButtonIcon,
} from '@gluestack-ui/themed';
import {
  Users,
  Briefcase,
  Heart,
  Home,
  Star,
  Bookmark,
  Settings,
  ChevronRight,
  Filter,
} from 'lucide-react-native';
import { useContactGroupsSelectors } from '../../store/contactGroupsStore';
import { useContactsStore } from '../../store/contactsStore';
import { ContactGroup } from '../../types';

interface ContactGroupSelectorProps {
  onGroupSelect: (groupId: string | null) => void;
  onManageGroups: () => void;
  selectedGroupId?: string | null;
  showContactCounts?: boolean;
}

/**
 * 联系人分组选择器组件
 * 显示所有分组并允许用户选择
 */
const ContactGroupSelector: React.FC<ContactGroupSelectorProps> = ({
  onGroupSelect,
  onManageGroups,
  selectedGroupId,
  showContactCounts = true,
}) => {
  const groups = useContactGroupsSelectors.useGroups();
  const { contacts } = useContactsStore();

  // 图标映射
  const iconMap = {
    users: Users,
    briefcase: Briefcase,
    heart: Heart,
    home: Home,
    star: Star,
    bookmark: Bookmark,
  };

  // 计算每个分组的联系人数量
  const getContactCount = (group: ContactGroup) => {
    if (group.name === '全部联系人') {
      return contacts.length;
    }
    if (group.name === '收藏') {
      return contacts.filter(c => c.isFavorite).length;
    }
    return contacts.filter(c => c.groupIds?.includes(group.id)).length;
  };

  // 特殊分组处理
  const getSpecialGroupCount = (type: 'favorites' | 'recent' | 'archived') => {
    switch (type) {
      case 'favorites':
        return contacts.filter(c => c.isFavorite).length;
      case 'recent':
        // 最近7天创建的联系人
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return contacts.filter(c => new Date(c.createdAt) > weekAgo).length;
      case 'archived':
        return contacts.filter(c => c.isArchived).length;
      default:
        return 0;
    }
  };

  const renderGroupItem = (group: ContactGroup, isSelected: boolean) => {
    const IconComponent = iconMap[group.icon as keyof typeof iconMap] || Users;
    const contactCount = showContactCounts ? getContactCount(group) : group.contactCount;

    return (
      <Pressable
        key={group.id}
        onPress={() => onGroupSelect(group.id)}
        p="$3"
        borderRadius="$md"
        bg={isSelected ? '$primary100' : 'transparent'}
        borderWidth={isSelected ? 1 : 0}
        borderColor="$primary500"
        sx={{
          _dark: {
            bg: isSelected ? '$primary900' : 'transparent',
          },
          _hover: {
            bg: isSelected ? '$primary100' : '$backgroundLight100',
          },
        }}
      >
        <HStack justifyContent="space-between" alignItems="center">
          <HStack space="md" alignItems="center" flex={1}>
            <Box
              w="$8"
              h="$8"
              borderRadius="$full"
              bg={group.color}
              justifyContent="center"
              alignItems="center"
            >
              <Icon as={IconComponent} size="sm" color="white" />
            </Box>
            
            <VStack flex={1}>
              <Text
                fontWeight={isSelected ? '$bold' : '$medium'}
                color={isSelected ? '$primary600' : '$textLight900'}
                sx={{
                  _dark: {
                    color: isSelected ? '$primary400' : '$textDark100',
                  },
                }}
              >
                {group.name}
              </Text>
              
              {group.description && (
                <Text
                  size="xs"
                  color="$textLight600"
                  sx={{ _dark: { color: '$textDark400' } }}
                >
                  {group.description}
                </Text>
              )}
            </VStack>
          </HStack>

          <HStack space="sm" alignItems="center">
            {showContactCounts && (
              <Badge
                size="sm"
                variant={isSelected ? 'solid' : 'outline'}
                action={isSelected ? 'primary' : 'muted'}
              >
                <BadgeText>{contactCount}</BadgeText>
              </Badge>
            )}
            
            {isSelected && (
              <Icon as={ChevronRight} size="sm" color="$primary600" />
            )}
          </HStack>
        </HStack>
      </Pressable>
    );
  };

  // 特殊分组项
  const renderSpecialGroupItem = (
    id: string,
    name: string,
    icon: any,
    color: string,
    type: 'favorites' | 'recent' | 'archived'
  ) => {
    const isSelected = selectedGroupId === id;
    const contactCount = getSpecialGroupCount(type);

    return (
      <Pressable
        key={id}
        onPress={() => onGroupSelect(id)}
        p="$3"
        borderRadius="$md"
        bg={isSelected ? '$primary100' : 'transparent'}
        borderWidth={isSelected ? 1 : 0}
        borderColor="$primary500"
        sx={{
          _dark: {
            bg: isSelected ? '$primary900' : 'transparent',
          },
          _hover: {
            bg: isSelected ? '$primary100' : '$backgroundLight100',
          },
        }}
      >
        <HStack justifyContent="space-between" alignItems="center">
          <HStack space="md" alignItems="center" flex={1}>
            <Box
              w="$8"
              h="$8"
              borderRadius="$full"
              bg={color}
              justifyContent="center"
              alignItems="center"
            >
              <Icon as={icon} size="sm" color="white" />
            </Box>
            
            <Text
              fontWeight={isSelected ? '$bold' : '$medium'}
              color={isSelected ? '$primary600' : '$textLight900'}
              sx={{
                _dark: {
                  color: isSelected ? '$primary400' : '$textDark100',
                },
              }}
            >
              {name}
            </Text>
          </HStack>

          <HStack space="sm" alignItems="center">
            {showContactCounts && (
              <Badge
                size="sm"
                variant={isSelected ? 'solid' : 'outline'}
                action={isSelected ? 'primary' : 'muted'}
              >
                <BadgeText>{contactCount}</BadgeText>
              </Badge>
            )}
            
            {isSelected && (
              <Icon as={ChevronRight} size="sm" color="$primary600" />
            )}
          </HStack>
        </HStack>
      </Pressable>
    );
  };

  return (
    <VStack space="md" p="$4" bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark900' } }}>
      {/* 标题和管理按钮 */}
      <HStack justifyContent="space-between" alignItems="center">
        <Text fontWeight="$bold" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
          联系人分组
        </Text>
        <Button size="xs" variant="outline" onPress={onManageGroups}>
          <ButtonIcon as={Settings} />
          <ButtonText>管理</ButtonText>
        </Button>
      </HStack>

      <ScrollView maxHeight="$64" showsVerticalScrollIndicator={false}>
        <VStack space="xs">
          {/* 特殊分组 */}
          {renderSpecialGroupItem('all', '全部联系人', Users, '#6B7280', 'recent')}
          {renderSpecialGroupItem('favorites', '收藏联系人', Star, '#EF4444', 'favorites')}
          {renderSpecialGroupItem('recent', '最近添加', Filter, '#10B981', 'recent')}

          {/* 分隔线 */}
          {groups.length > 0 && (
            <Box h="$px" bg="$borderLight200" my="$2" sx={{ _dark: { bg: '$borderDark700' } }} />
          )}

          {/* 用户分组 */}
          {groups.map((group) => renderGroupItem(group, selectedGroupId === group.id))}

          {/* 空状态 */}
          {groups.length === 0 && (
            <Box p="$4" alignItems="center">
              <Text
                size="sm"
                color="$textLight500"
                sx={{ _dark: { color: '$textDark500' } }}
                textAlign="center"
              >
                还没有自定义分组{'\n'}点击"管理"创建分组
              </Text>
            </Box>
          )}
        </VStack>
      </ScrollView>
    </VStack>
  );
};

export default ContactGroupSelector;
