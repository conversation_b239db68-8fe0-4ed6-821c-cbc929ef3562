import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ContactsStore, Contact, TagItem } from './types';

// Mock data for development
const mockContacts: Contact[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Tech Corp',
    position: 'Software Engineer',
    notes: 'Met at tech conference',
    tags: ['work', 'tech'],
    avatar: 'https://api.a0.dev/assets/image?text=JD&aspect=1:1',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    isFavorite: true,
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Design Studio',
    position: 'UX Designer',
    notes: 'Collaborated on mobile app project',
    tags: ['work', 'design'],
    avatar: 'https://api.a0.dev/assets/image?text=JS&aspect=1:1',
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
    isFavorite: false,
  },
];

// Helper functions
const generateId = () => Date.now().toString();

const filterContacts = (
  contacts: Contact[],
  searchQuery: string,
  selectedTags: string[]
): Contact[] => {
  return contacts.filter((contact) => {
    // Search filter
    const matchesSearch = searchQuery === '' || 
      contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.company?.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Tags filter
    const matchesTags = selectedTags.length === 0 ||
      selectedTags.some(tag => contact.tags.includes(tag));
    
    return matchesSearch && matchesTags;
  });
};

const sortContacts = (
  contacts: Contact[],
  sortBy: 'name' | 'createdAt' | 'updatedAt',
  sortOrder: 'asc' | 'desc'
): Contact[] => {
  return [...contacts].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'createdAt':
        comparison = a.createdAt.getTime() - b.createdAt.getTime();
        break;
      case 'updatedAt':
        comparison = a.updatedAt.getTime() - b.updatedAt.getTime();
        break;
    }
    
    return sortOrder === 'asc' ? comparison : -comparison;
  });
};

const extractTags = (contacts: Contact[]): string[] => {
  const tagSet = new Set<string>();
  contacts.forEach(contact => {
    contact.tags.forEach(tag => tagSet.add(tag));
  });
  return Array.from(tagSet).sort();
};

/**
 * Contacts Store
 * 管理联系人数据和相关操作
 */
export const useContactsStore = create<ContactsStore>()(
  persist(
    (set, get) => ({
      // Initial state
      contacts: mockContacts,
      filteredContacts: mockContacts,
      selectedContact: null,
      searchQuery: '',
      selectedTags: [],
      availableTags: extractTags(mockContacts),
      tags: [],
      sortBy: 'name',
      sortOrder: 'asc',
      isLoading: false,
      error: null,

      // Extended state for new features
      selectedGroupId: null,
      groupFilter: 'all',
      relationshipFilter: null,
      priorityFilter: null,
      viewMode: 'list',
      showArchived: false,
      
      // CRUD operations
      addContact: (contactData) => {
        const newContact: Contact = {
          ...contactData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        set((state) => {
          const updatedContacts = [...state.contacts, newContact];
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
          };
        });
      },
      
      updateContact: (id, updates) => {
        set((state) => {
          const updatedContacts = state.contacts.map(contact =>
            contact.id === id
              ? { ...contact, ...updates, updatedAt: new Date() }
              : contact
          );
          
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
            selectedContact: state.selectedContact?.id === id
              ? { ...state.selectedContact, ...updates, updatedAt: new Date() }
              : state.selectedContact,
          };
        });
      },
      
      deleteContact: (id) => {
        set((state) => {
          const updatedContacts = state.contacts.filter(contact => contact.id !== id);
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
            selectedContact: state.selectedContact?.id === id ? null : state.selectedContact,
          };
        });
      },
      
      toggleFavorite: (id) => {
        set((state) => {
          const updatedContacts = state.contacts.map(contact =>
            contact.id === id
              ? { ...contact, isFavorite: !contact.isFavorite, updatedAt: new Date() }
              : contact
          );
          
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            selectedContact: state.selectedContact?.id === id
              ? { ...state.selectedContact, isFavorite: !state.selectedContact.isFavorite }
              : state.selectedContact,
          };
        });
      },
      
      // Search and filter
      setSearchQuery: (query) => {
        set((state) => {
          const filteredContacts = sortContacts(
            filterContacts(state.contacts, query, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            searchQuery: query,
            filteredContacts,
          };
        });
      },
      
      setSelectedTags: (tags) => {
        set((state) => {
          const filteredContacts = sortContacts(
            filterContacts(state.contacts, state.searchQuery, tags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            selectedTags: tags,
            filteredContacts,
          };
        });
      },
      
      setSortBy: (sortBy) => {
        set((state) => {
          const filteredContacts = sortContacts(
            state.filteredContacts,
            sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            sortBy,
            filteredContacts,
          };
        });
      },
      
      setSortOrder: (order) => {
        set((state) => {
          const filteredContacts = sortContacts(
            state.filteredContacts,
            state.sortBy,
            order
          );
          
          return {
            ...state,
            sortOrder: order,
            filteredContacts,
          };
        });
      },
      
      // Selection
      setSelectedContact: (contact) => {
        set((state) => ({
          ...state,
          selectedContact: contact,
        }));
      },
      
      // Bulk operations
      deleteMultipleContacts: (ids) => {
        set((state) => {
          const updatedContacts = state.contacts.filter(contact => !ids.includes(contact.id));
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
            selectedContact: ids.includes(state.selectedContact?.id || '') ? null : state.selectedContact,
          };
        });
      },
      
      addTagToMultiple: (ids, tag) => {
        set((state) => {
          const updatedContacts = state.contacts.map(contact =>
            ids.includes(contact.id)
              ? { ...contact, tags: Array.from(new Set([...contact.tags, tag])), updatedAt: new Date() }
              : contact
          );
          
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );
          
          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
          };
        });
      },
      
      // Data management (placeholder implementations)
      loadContacts: async () => {
        set((state) => ({ ...state, isLoading: true, error: null }));
        try {
          // TODO: Implement actual API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          set((state) => ({ ...state, isLoading: false }));
        } catch (error) {
          set((state) => ({ 
            ...state, 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to load contacts' 
          }));
        }
      },
      
      syncContacts: async () => {
        // TODO: Implement sync logic
        console.log('Syncing contacts...');
      },
      
      exportContacts: async () => {
        const { contacts } = get();
        return JSON.stringify(contacts, null, 2);
      },
      
      importContacts: async (data) => {
        set((state) => {
          const updatedContacts = [...state.contacts, ...data];
          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );

          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
          };
        });
      },

      // Tag management
      addTag: (tag) => {
        set((state) => ({
          ...state,
          tags: [...state.tags, tag],
        }));
      },

      updateTag: (updatedTag) => {
        set((state) => ({
          ...state,
          tags: state.tags.map(tag =>
            tag.id === updatedTag.id ? updatedTag : tag
          ),
        }));
      },

      deleteTag: (tagId) => {
        set((state) => {
          const tagToDelete = state.tags.find(tag => tag.id === tagId);
          if (!tagToDelete) return state;

          // Remove tag from all contacts
          const updatedContacts = state.contacts.map(contact => ({
            ...contact,
            tags: contact.tags.filter(tag => tag !== tagToDelete.name),
            updatedAt: new Date(),
          }));

          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );

          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
            tags: state.tags.filter(tag => tag.id !== tagId),
          };
        });
      },

      addTagToContact: (contactId, tagName) => {
        set((state) => {
          const updatedContacts = state.contacts.map(contact =>
            contact.id === contactId
              ? {
                  ...contact,
                  tags: Array.from(new Set([...contact.tags, tagName])),
                  updatedAt: new Date()
                }
              : contact
          );

          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );

          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
          };
        });
      },

      removeTagFromContact: (contactId, tagName) => {
        set((state) => {
          const updatedContacts = state.contacts.map(contact =>
            contact.id === contactId
              ? {
                  ...contact,
                  tags: contact.tags.filter(tag => tag !== tagName),
                  updatedAt: new Date()
                }
              : contact
          );

          const availableTags = extractTags(updatedContacts);
          const filteredContacts = sortContacts(
            filterContacts(updatedContacts, state.searchQuery, state.selectedTags),
            state.sortBy,
            state.sortOrder
          );

          return {
            ...state,
            contacts: updatedContacts,
            filteredContacts,
            availableTags,
          };
        });
      },

      // Placeholder implementations for new features
      setSelectedGroup: (groupId) => {
        set((state) => ({ ...state, selectedGroupId: groupId }));
      },

      setGroupFilter: (filter) => {
        set((state) => ({ ...state, groupFilter: filter }));
      },

      setRelationshipFilter: (relationship) => {
        set((state) => ({ ...state, relationshipFilter: relationship }));
      },

      setPriorityFilter: (priority) => {
        set((state) => ({ ...state, priorityFilter: priority }));
      },

      setViewMode: (mode) => {
        set((state) => ({ ...state, viewMode: mode }));
      },

      setShowArchived: (show) => {
        set((state) => ({ ...state, showArchived: show }));
      },

      // Additional placeholder methods
      archiveContact: (id) => {
        console.log('Archive contact:', id);
      },

      unarchiveContact: (id) => {
        console.log('Unarchive contact:', id);
      },

      addPhoneNumber: (contactId, phone) => {
        console.log('Add phone number:', contactId, phone);
      },

      updatePhoneNumber: (contactId, phoneId, updates) => {
        console.log('Update phone number:', contactId, phoneId, updates);
      },

      removePhoneNumber: (contactId, phoneId) => {
        console.log('Remove phone number:', contactId, phoneId);
      },

      addEmailAddress: (contactId, email) => {
        console.log('Add email address:', contactId, email);
      },

      updateEmailAddress: (contactId, emailId, updates) => {
        console.log('Update email address:', contactId, emailId, updates);
      },

      removeEmailAddress: (contactId, emailId) => {
        console.log('Remove email address:', contactId, emailId);
      },

      addAddress: (contactId, address) => {
        console.log('Add address:', contactId, address);
      },

      updateAddress: (contactId, addressId, updates) => {
        console.log('Update address:', contactId, addressId, updates);
      },

      removeAddress: (contactId, addressId) => {
        console.log('Remove address:', contactId, addressId);
      },

      addSocialLink: (contactId, socialLink) => {
        console.log('Add social link:', contactId, socialLink);
      },

      updateSocialLink: (contactId, linkId, updates) => {
        console.log('Update social link:', contactId, linkId, updates);
      },

      removeSocialLink: (contactId, linkId) => {
        console.log('Remove social link:', contactId, linkId);
      },

      addImportantDate: (contactId, date) => {
        console.log('Add important date:', contactId, date);
      },

      updateImportantDate: (contactId, dateId, updates) => {
        console.log('Update important date:', contactId, dateId, updates);
      },

      removeImportantDate: (contactId, dateId) => {
        console.log('Remove important date:', contactId, dateId);
      },

      addCustomField: (contactId, field) => {
        console.log('Add custom field:', contactId, field);
      },

      updateCustomField: (contactId, fieldId, updates) => {
        console.log('Update custom field:', contactId, fieldId, updates);
      },

      removeCustomField: (contactId, fieldId) => {
        console.log('Remove custom field:', contactId, fieldId);
      },

      addContactToGroup: (contactId, groupId) => {
        console.log('Add contact to group:', contactId, groupId);
      },

      removeContactFromGroup: (contactId, groupId) => {
        console.log('Remove contact from group:', contactId, groupId);
      },

      moveContactToGroup: (contactId, fromGroupId, toGroupId) => {
        console.log('Move contact to group:', contactId, fromGroupId, toGroupId);
      },

      addToGroupMultiple: (ids, groupId) => {
        console.log('Add multiple to group:', ids, groupId);
      },

      removeFromGroupMultiple: (ids, groupId) => {
        console.log('Remove multiple from group:', ids, groupId);
      },

      updateLastContactDate: (contactId, date) => {
        console.log('Update last contact date:', contactId, date);
      },

      setNextFollowUpDate: (contactId, date) => {
        console.log('Set next follow up date:', contactId, date);
      },

      incrementInteractionCount: (contactId) => {
        console.log('Increment interaction count:', contactId);
      },

      clearFilters: () => {
        set((state) => ({
          ...state,
          searchQuery: '',
          selectedTags: [],
          selectedGroupId: null,
          groupFilter: 'all',
          relationshipFilter: null,
          priorityFilter: null,
        }));
      },

      resetContactsState: () => {
        set((state) => ({
          ...state,
          contacts: [],
          filteredContacts: [],
          selectedContact: null,
          searchQuery: '',
          selectedTags: [],
          availableTags: [],
          tags: [],
          selectedGroupId: null,
          groupFilter: 'all',
          relationshipFilter: null,
          priorityFilter: null,
          viewMode: 'list',
          showArchived: false,
        }));
      },
    }),
    {
      name: 'contacts-storage',
      storage: createJSONStorage(() => AsyncStorage),
      // 只持久化数据，不持久化actions
      partialize: (state) => ({
        contacts: state.contacts,
        selectedTags: state.selectedTags,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
      }),
    }
  )
);
