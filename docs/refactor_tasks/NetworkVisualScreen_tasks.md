# NetworkVisualScreen Refactor Tasks

- **重构状态:** ToDo
- **主要区域/视图:**
    - **1. 顶部导航/操作区 (Header)**
        - **描述:** 可能包含返回按钮、屏幕标题 (\"Network Visualization\")、筛选或设置按钮。
        - **现有主要组件:** `View`, `TouchableOpacity`, `Ionicons`, `Text`. (Assuming a standard header structure)
        - **Gluestack UI 迁移建议:**
            - `View` (header container) -> `HStack` or `Box` with `justifyContent=\"space-between\"`, `alignItems=\"center\"`.
            - `TouchableOpacity` (buttons) -> `Pressable` or `Button` (icon only or text, `variant=\"link\"` or `solid`).
            - `Ionicons` -> `lucide-react-native` icons (e.g., `ChevronLeft`, `Filter`, `Settings2`).
            - `Text` (screen title) -> `Heading`.
        - **设计规范参考:** 页面顶部标题与内容间距, 主色 (`#007AFF`).

    - **2. 网络可视化核心区域 (Network Graph Area)**
        - **描述:** 显示交互式的节点和边组成的关系网络图。支持平移、缩放等手势。
        - **现有主要组件:** Custom rendering logic, likely using `react-native-svg`, `react-native-gesture-handler`, `react-native-reanimated`.
        - **Gluestack UI 迁移建议:**
            - **核心图表逻辑:** 这部分自定义代码将保持不变。`gluestack-ui` 不提供此类复杂的图表组件。
            - **节点/边样式:** 如果节点或边的视觉元素（如标签、背景）当前是用 React Native `View`/`Text` 实现的，可以考虑将这些基础元素替换为 `gluestack-ui` 的 `Box`, `Text`, `Badge` 等，以便它们能继承主题样式。但这需要仔细评估，避免破坏现有布局和性能。
            - **手势处理:** `react-native-gesture-handler` 和 `react-native-reanimated` 的集成将保持不变。
        - **设计规范参考:** N/A for the graph itself, but any text or simple shapes within nodes could potentially use theme typography/colors.

    - **3. 控制/筛选面板 (Controls/Filters Panel)**
        - **描述:** 可能存在的用于控制图表显示（例如，层级深度、关系类型筛选）的UI元素。可能是一个侧边栏、底部面板或浮动按钮触发的模态框。
        - **现有主要组件:** `View`, `Button`, `Switch`, `Slider`, `Picker`, `Text`.
        - **Gluestack UI 迁移建议:**
            - `View` (panel container) -> `Box`, `VStack`, or `HStack`.
            - `Button` (core RN) -> `Button` from `gluestack-ui`.
            - `Switch` (core RN) -> `Switch` from `gluestack-ui`.
            - `Slider` (core RN or third-party) -> If using a third-party slider, wrap or style its trigger/labels with `gluestack-ui`. `gluestack-ui` has a `Slider` component.
            - `Picker` (core RN or third-party) -> `Select` from `gluestack-ui`.
            - `Text` (labels) -> `Text` from `gluestack-ui`.
        - **设计规范参考:** 按钮样式, 开关样式, 滑块样式, 选择器样式, 文本样式.

    - **4. 节点/联系人详情模态框 (Node/Contact Detail Modal)**
        - **描述:** 点击图表中的节点时，弹出显示该节点（联系人或实体）的详细信息。
        - **现有主要组件:** `Modal`, `View`, `Text`, `Image`, `Button`, `Ionicons`.
        - **Gluestack UI 迁移建议:**
            - `Modal` -> `Modal` component from `gluestack-ui`.
                - `Modal.Backdrop`, `Modal.Content`, `Modal.Header` (with `Heading`), `Modal.Body` (with `VStack`, `Text`, `Avatar`), `Modal.Footer` (with `Button`s), `Modal.CloseButton`.
            - `Image` (avatar) -> `Avatar`.
            - `Text` (details) -> `Text`.
            - `Button` (actions, e.g., \"View Full Profile\") -> `Button`.
            - `Ionicons` -> `lucide-react-native` icons.
        - **设计规范参考:** 模态框样式, 头像, 文本, 按钮.

    - **5. 缩放/导航控件 (Zoom/Navigation Controls - Optional)**
        - **描述:** 如果有显式的UI按钮用于缩放或重置视图。
        - **现有主要组件:** `View`, `TouchableOpacity`, `Ionicons`.
        - **Gluestack UI 迁移建议:**
            - `View` (container for controls) -> `Box` or `HStack`/`VStack`, possibly with absolute positioning.
            - `TouchableOpacity` (zoom in/out, reset) -> `Button` (icon only, `variant=\"outline\"` or `solid`).
            - `Ionicons` -> `lucide-react-native` icons (e.g., `ZoomIn`, `ZoomOut`, `Maximize`).
        - **设计规范参考:** 按钮样式, 图标.

- **整体注意事项:**
    - **性能:** 网络可视化通常对性能敏感。在将小型UI元素（如图表内的标签）替换为 `gluestack-ui` 组件时，需密切关注性能影响。
    - **图标替换:** All `Ionicons`, `MaterialIcons`, `FontAwesome5` etc. to `lucide-react-native`.
    - **颜色和样式:** 任何非核心图表部分的UI（如模态框、控制面板）应使用 `gluestack-ui` 组件和主题token。
    - **复杂逻辑:** 核心的图表渲染、布局算法、手势和动画逻辑 (`react-native-reanimated`, `react-native-gesture-handler`) 将保持不变。
    - **Babel 配置:** 确保 `babel.config.js` 正确配置了 `react-native-reanimated/plugin` (应为最后一个插件) 和 `nativewind/babel` (如果 `gluestack-ui` 依赖 NativeWind，根据项目配置)。参考已保存的 Babel 配置记忆。
