# HomeScreen Refactor Tasks

- **重构状态:** ToDo
- **主要区域/视图:**
    - **1. 顶部问候与统计区域 (Header & Stats)**
        - **描述:** 显示用户问候语、日期和关键统计数据 (如总联系人数、本周新增、即将到期的提醒)。
        - **现有主要组件:** `View`, `Text`, `TouchableOpacity` (for stats cards).
        - **Gluestack UI 迁移建议:**
            - `View` (main container) -> `Box`.
            - `Text` (greeting, date) -> `Heading`, `Text`.
            - `View` (stats row) -> `HStack` with `space=\"md\"` or `justifyContent=\"space-around\"`.
            - `TouchableOpacity` (stat card) -> `Pressable` or `Box` styled as a card (`bg=\"$primary50\"`, `p=\"$3\"`, `rounded=\"$md\"`).
            - `Text` (stat number, stat label) -> `Heading` (for number), `Text` (for label).
        - **设计规范参考:** 字体大小与字重, 卡片间距 (16px), 主色 (`#007AFF`) for highlights.

    - **2. 快速操作按钮区域 (Quick Actions)**
        - **描述:** 一组水平排列的圆形按钮，用于快速访问核心功能 (如添加联系人、记录会议、查看图谱)。
        - **现有主要组件:** `View` (container), `TouchableOpacity` (action button), `Ionicons`, `Text` (button label).
        - **Gluestack UI 迁移建议:**
            - `View` (container) -> `HStack` with `space=\"lg\"` or `justifyContent=\"space-around\"`.
            - `TouchableOpacity` (action button) -> `Button` (`variant=\"solid\"` or custom with `VStack` for icon and text), possibly circular or with `rounded=\"$full\"` for icon part.
            - `Ionicons` -> `lucide-react-native` icons.
            - `Text` (button label) -> `ButtonText` or `Text`.
        - **设计规范参考:** 圆形按钮直径 (56px), 图标与文本间距 (8px), 主色 (`#007AFF`).

    - **3. 最近联系人列表 (Recent Contacts)**
        - **描述:** 水平滚动的卡片列表，显示最近互动或添加的联系人。
        - **现有主要组件:** `View` (section container), `Text` (section title), `ScrollView` (horizontal), `TouchableOpacity` (contact card), `Image` (avatar), `Text` (name, company).
        - **Gluestack UI 迁移建议:**
            - `Text` (section title) -> `Heading` (`size=\"lg\"`).
            - `ScrollView` -> Retain `ScrollView` with `horizontal={true}`.
            - `TouchableOpacity` (contact card) -> `Pressable` or `Box` styled as a card (`bg=\"$backgroundLight0\"`, `p=\"$3\"`, `rounded=\"$lg\"`, `width` fixed, `mr=\"$3\"`).
            - `Image` (avatar) -> `Avatar` component.
            - `Text` (name, company) -> `Text` (`fontWeight=\"$bold\"`), `Text` (`size=\"sm\"`).
        - **设计规范参考:** 卡片宽度和高度, 卡片间距 (16px), 头像尺寸.

    - **4. 提醒事项列表 (Reminders)**
        - **描述:** 垂直列表，显示即将到来的提醒事项。
        - **现有主要组件:** `View` (section container), `Text` (section title, \"View All\"), `View` (reminder item), `Ionicons` (reminder icon), `Text` (reminder text, time).
        - **Gluestack UI 迁移建议:**
            - `Text` (section title) -> `Heading` (`size=\"lg\"`).
            - `Text` (\"View All\") -> `Link` or `Button` (`variant=\"link\"`).
            - `View` (reminder item) -> `HStack` or `Box` styled as a list item (`p=\"$3\"`, `rounded=\"$md\"`, `mb=\"$2\"`).
            - `Ionicons` (reminder icon) -> `lucide-react-native` icon.
            - `Text` (reminder text, time) -> `Text`.
        - **设计规范参考:** 列表项样式, 图标颜色, 文本样式.

    - **5. 网络图谱预览区域 (Network Graph Preview)**
        - **描述:** 主屏幕上显示网络图谱的一个小预览或入口。
        - **现有主要组件:** `View` (container), `Text` (title), `Image` (preview image/placeholder), `TouchableOpacity` (view full graph).
        - **Gluestack UI 迁移建议:**
            - `View` (container) -> `Box` styled as a card.
            - `Text` (title) -> `Heading`.
            - `Image` (preview) -> `Image` component or a placeholder `Box`.
            - `TouchableOpacity` (view full graph) -> `Button` (`variant=\"solid\"`).
        - **设计规范参考:** 卡片样式, 按钮样式.

- **整体注意事项:**
    - **图标替换:** 所有 `Ionicons` 替换为 `lucide-react-native`.
    - **颜色和样式:** 迁移所有 `StyleSheet` 样式到 `gluestack-ui` 的 props 或 `sx` prop，使用主题中的颜色 token.
    - **导航:** 确保所有导航链接 (`navigation.navigate`) 保持不变。
    - **可点击反馈:** 确保所有 `TouchableOpacity` 替换为 `Pressable` 或 `Button` 后有适当的点击反馈。
