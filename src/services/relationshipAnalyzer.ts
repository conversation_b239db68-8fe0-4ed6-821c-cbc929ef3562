/**
 * 关系强度分析引擎
 * 提供更加智能和全面的关系强度计算和分析
 */

import { Contact, CommunicationRecord, Meeting } from '../types';

export interface RelationshipMetrics {
  // 核心指标
  connectionStrength: number;      // 连接强度 (0-100)
  communicationQuality: number;    // 沟通质量 (0-100)
  interactionFrequency: number;    // 互动频率 (0-100)
  relationshipTrend: number;       // 关系趋势 (-100 to 100)
  
  // 细分指标
  recencyScore: number;           // 最近性得分 (0-100)
  frequencyScore: number;         // 频率得分 (0-100)
  qualityScore: number;           // 质量得分 (0-100)
  diversityScore: number;         // 多样性得分 (0-100)
  mutualityScore: number;         // 互动性得分 (0-100)
  
  // 时间衰减因子
  timeDecayFactor: number;        // 时间衰减系数 (0-1)
  
  // 预测指标
  riskLevel: 'low' | 'medium' | 'high';  // 关系风险等级
  nextContactPrediction: Date | null;     // 预测下次联系时间
  relationshipPhase: 'building' | 'stable' | 'declining' | 'dormant'; // 关系阶段
}

export interface RelationshipInsight {
  type: 'strength' | 'frequency' | 'quality' | 'trend' | 'risk';
  level: 'positive' | 'neutral' | 'negative';
  title: string;
  description: string;
  recommendation?: string;
  confidence: number; // 置信度 (0-1)
}

export interface RelationshipAnalysis {
  contactId: string;
  metrics: RelationshipMetrics;
  insights: RelationshipInsight[];
  lastAnalyzed: Date;
  dataQuality: number; // 数据质量评分 (0-100)
}

export class RelationshipAnalyzer {
  
  /**
   * 分析联系人关系强度
   */
  analyzeRelationship(
    contact: Contact,
    communications: CommunicationRecord[],
    meetings: Meeting[] = []
  ): RelationshipAnalysis {
    
    const now = new Date();
    const contactComms = communications.filter(c => c.primaryContactId === contact.id);
    const contactMeetings = meetings.filter(m => m.participants?.includes(contact.id));
    
    // 计算各项指标
    const recencyScore = this.calculateRecencyScore(contactComms, contactMeetings, now);
    const frequencyScore = this.calculateFrequencyScore(contactComms, contactMeetings, contact);
    const qualityScore = this.calculateQualityScore(contactComms, contactMeetings);
    const diversityScore = this.calculateDiversityScore(contactComms, contactMeetings);
    const mutualityScore = this.calculateMutualityScore(contactComms);
    
    // 计算时间衰减因子
    const timeDecayFactor = this.calculateTimeDecayFactor(contactComms, contactMeetings, now);
    
    // 计算综合指标
    const connectionStrength = this.calculateConnectionStrength(
      recencyScore, frequencyScore, qualityScore, diversityScore, mutualityScore, timeDecayFactor, contact
    );
    
    const communicationQuality = this.calculateCommunicationQuality(contactComms);
    const interactionFrequency = this.calculateInteractionFrequency(contactComms, contactMeetings);
    const relationshipTrend = this.calculateRelationshipTrend(contactComms, contactMeetings);
    
    // 构建指标对象
    const metrics: RelationshipMetrics = {
      connectionStrength,
      communicationQuality,
      interactionFrequency,
      relationshipTrend,
      recencyScore,
      frequencyScore,
      qualityScore,
      diversityScore,
      mutualityScore,
      timeDecayFactor,
      riskLevel: this.assessRiskLevel(connectionStrength, relationshipTrend, recencyScore),
      nextContactPrediction: this.predictNextContact(contactComms, contactMeetings, contact),
      relationshipPhase: this.determineRelationshipPhase(connectionStrength, relationshipTrend, recencyScore),
    };
    
    // 生成洞察
    const insights = this.generateInsights(contact, metrics, contactComms, contactMeetings);
    
    // 评估数据质量
    const dataQuality = this.assessDataQuality(contactComms, contactMeetings, contact);
    
    return {
      contactId: contact.id,
      metrics,
      insights,
      lastAnalyzed: now,
      dataQuality,
    };
  }
  
  /**
   * 计算最近性得分
   */
  private calculateRecencyScore(
    communications: CommunicationRecord[],
    meetings: Meeting[],
    now: Date
  ): number {
    const allInteractions = [
      ...communications.map(c => c.startTime),
      ...meetings.map(m => m.date),
    ].sort((a, b) => b.getTime() - a.getTime());
    
    if (allInteractions.length === 0) return 0;
    
    const lastInteraction = allInteractions[0];
    const daysSinceLastInteraction = Math.floor(
      (now.getTime() - lastInteraction.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    // 使用指数衰减函数，30天内为满分，之后逐渐衰减
    const score = Math.max(0, 100 * Math.exp(-daysSinceLastInteraction / 30));
    return Math.round(score);
  }
  
  /**
   * 计算频率得分
   */
  private calculateFrequencyScore(
    communications: CommunicationRecord[],
    meetings: Meeting[],
    contact: Contact
  ): number {
    const totalInteractions = communications.length + meetings.length;
    
    if (totalInteractions === 0) return 0;
    
    // 根据联系人类型设定期望频率
    const expectedFrequency = this.getExpectedFrequency(contact);
    
    // 计算实际频率（每月互动次数）
    const now = new Date();
    const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);
    
    const recentInteractions = [
      ...communications.filter(c => c.startTime >= sixMonthsAgo),
      ...meetings.filter(m => m.date >= sixMonthsAgo),
    ];
    
    const actualFrequency = (recentInteractions.length / 6) || 0; // 每月平均次数
    
    // 计算频率得分
    const frequencyRatio = Math.min(actualFrequency / expectedFrequency, 2); // 最高200%
    const score = Math.min(100, frequencyRatio * 50);
    
    return Math.round(score);
  }
  
  /**
   * 计算质量得分
   */
  private calculateQualityScore(
    communications: CommunicationRecord[],
    meetings: Meeting[]
  ): number {
    if (communications.length === 0 && meetings.length === 0) return 0;
    
    let totalQuality = 0;
    let qualityCount = 0;
    
    // 沟通记录质量
    communications.forEach(comm => {
      if (comm.effectiveness !== undefined) {
        totalQuality += comm.effectiveness * 10; // 转换为0-100分
        qualityCount++;
      }
      
      // 根据情感调整质量
      if (comm.sentiment) {
        const sentimentBonus = this.getSentimentBonus(comm.sentiment);
        totalQuality += sentimentBonus;
        qualityCount++;
      }
    });
    
    // 会议质量（假设会议质量较高）
    meetings.forEach(meeting => {
      if (meeting.status === 'completed') {
        totalQuality += 75; // 完成的会议默认75分
        qualityCount++;
      }
    });
    
    return qualityCount > 0 ? Math.round(totalQuality / qualityCount) : 50;
  }
  
  /**
   * 计算多样性得分
   */
  private calculateDiversityScore(
    communications: CommunicationRecord[],
    meetings: Meeting[]
  ): number {
    const communicationTypes = new Set(communications.map(c => c.type));
    const hasMeetings = meetings.length > 0;
    
    let diversityScore = 0;
    
    // 基础沟通类型多样性
    diversityScore += Math.min(communicationTypes.size * 15, 60); // 最多4种类型，每种15分
    
    // 会议加分
    if (hasMeetings) {
      diversityScore += 25;
    }
    
    // 如果有深度沟通（会议或长时间通话）
    const hasDeepCommunication = communications.some(c => 
      (c.type === 'call' || c.type === 'video_call') && (c.duration || 0) > 30
    ) || hasMeetings;
    
    if (hasDeepCommunication) {
      diversityScore += 15;
    }
    
    return Math.min(100, diversityScore);
  }
  
  /**
   * 计算互动性得分
   */
  private calculateMutualityScore(communications: CommunicationRecord[]): number {
    if (communications.length === 0) return 0;
    
    const outgoingCount = communications.filter(c => c.direction === 'outgoing').length;
    const incomingCount = communications.filter(c => c.direction === 'incoming').length;
    const mutualCount = communications.filter(c => c.direction === 'mutual').length;
    
    const totalCount = communications.length;
    
    // 计算双向互动比例
    const mutualRatio = mutualCount / totalCount;
    const balanceRatio = Math.min(outgoingCount, incomingCount) / Math.max(outgoingCount, incomingCount, 1);
    
    // 互动性得分 = 双向互动比例 * 50 + 平衡比例 * 50
    const score = mutualRatio * 50 + balanceRatio * 50;
    
    return Math.round(score);
  }
  
  /**
   * 计算时间衰减因子
   */
  private calculateTimeDecayFactor(
    communications: CommunicationRecord[],
    meetings: Meeting[],
    now: Date
  ): number {
    const allInteractions = [
      ...communications.map(c => ({ date: c.startTime, weight: 1 })),
      ...meetings.map(m => ({ date: m.date, weight: 1.5 })), // 会议权重更高
    ].sort((a, b) => b.date.getTime() - a.date.getTime());
    
    if (allInteractions.length === 0) return 0;
    
    let weightedSum = 0;
    let totalWeight = 0;
    
    allInteractions.forEach(interaction => {
      const daysSince = Math.floor(
        (now.getTime() - interaction.date.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      // 使用指数衰减，半衰期为90天
      const decayFactor = Math.exp(-daysSince / 90);
      const weight = interaction.weight * decayFactor;
      
      weightedSum += weight;
      totalWeight += interaction.weight;
    });
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }
  
  /**
   * 计算综合连接强度
   */
  private calculateConnectionStrength(
    recencyScore: number,
    frequencyScore: number,
    qualityScore: number,
    diversityScore: number,
    mutualityScore: number,
    timeDecayFactor: number,
    contact: Contact
  ): number {
    // 基础权重
    let weights = {
      recency: 0.25,
      frequency: 0.20,
      quality: 0.25,
      diversity: 0.15,
      mutuality: 0.15,
    };
    
    // 根据联系人类型调整权重
    switch (contact.relationshipType) {
      case 'family':
        weights.quality += 0.1;
        weights.frequency += 0.05;
        break;
      case 'friend':
        weights.mutuality += 0.1;
        weights.diversity += 0.05;
        break;
      case 'colleague':
      case 'client':
        weights.frequency += 0.1;
        weights.quality += 0.05;
        break;
    }
    
    // 计算加权得分
    const weightedScore = 
      recencyScore * weights.recency +
      frequencyScore * weights.frequency +
      qualityScore * weights.quality +
      diversityScore * weights.diversity +
      mutualityScore * weights.mutuality;
    
    // 应用时间衰减因子
    const finalScore = weightedScore * (0.7 + 0.3 * timeDecayFactor);
    
    // 根据联系人优先级调整
    const priorityBonus = this.getPriorityBonus(contact.priority);
    
    return Math.min(100, Math.round(finalScore + priorityBonus));
  }
  
  /**
   * 计算沟通质量
   */
  private calculateCommunicationQuality(communications: CommunicationRecord[]): number {
    if (communications.length === 0) return 0;
    
    let totalQuality = 0;
    let count = 0;
    
    communications.forEach(comm => {
      if (comm.effectiveness !== undefined) {
        totalQuality += comm.effectiveness * 10;
        count++;
      }
      
      // 根据沟通结果调整
      if (comm.outcome) {
        const outcomeScore = this.getOutcomeScore(comm.outcome);
        totalQuality += outcomeScore;
        count++;
      }
    });
    
    return count > 0 ? Math.round(totalQuality / count) : 50;
  }
  
  /**
   * 计算互动频率
   */
  private calculateInteractionFrequency(
    communications: CommunicationRecord[],
    meetings: Meeting[]
  ): number {
    const totalInteractions = communications.length + meetings.length;
    
    if (totalInteractions === 0) return 0;
    
    // 计算最近6个月的互动频率
    const now = new Date();
    const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);
    
    const recentInteractions = [
      ...communications.filter(c => c.startTime >= sixMonthsAgo),
      ...meetings.filter(m => m.date >= sixMonthsAgo),
    ];
    
    const monthlyFrequency = recentInteractions.length / 6;
    
    // 将频率转换为0-100分
    // 假设每月4次互动为满分
    const score = Math.min(100, (monthlyFrequency / 4) * 100);
    
    return Math.round(score);
  }
  
  /**
   * 计算关系趋势
   */
  private calculateRelationshipTrend(
    communications: CommunicationRecord[],
    meetings: Meeting[]
  ): number {
    const allInteractions = [
      ...communications.map(c => ({ date: c.startTime, quality: c.effectiveness || 5 })),
      ...meetings.map(m => ({ date: m.date, quality: 7 })), // 会议默认质量较高
    ].sort((a, b) => a.date.getTime() - b.date.getTime());
    
    if (allInteractions.length < 2) return 0;
    
    const now = new Date();
    const threeMonthsAgo = new Date(now.getTime() - 3 * 30 * 24 * 60 * 60 * 1000);
    const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);
    
    // 计算最近3个月和前3个月的平均质量
    const recent = allInteractions.filter(i => i.date >= threeMonthsAgo);
    const previous = allInteractions.filter(i => i.date >= sixMonthsAgo && i.date < threeMonthsAgo);
    
    if (recent.length === 0 && previous.length === 0) return 0;
    
    const recentAvg = recent.length > 0 
      ? recent.reduce((sum, i) => sum + i.quality, 0) / recent.length 
      : 0;
    
    const previousAvg = previous.length > 0 
      ? previous.reduce((sum, i) => sum + i.quality, 0) / previous.length 
      : recentAvg;
    
    // 计算趋势 (-100 到 100)
    const trend = ((recentAvg - previousAvg) / Math.max(previousAvg, 1)) * 100;
    
    return Math.max(-100, Math.min(100, Math.round(trend)));
  }
  
  /**
   * 评估关系风险等级
   */
  private assessRiskLevel(
    connectionStrength: number,
    relationshipTrend: number,
    recencyScore: number
  ): 'low' | 'medium' | 'high' {
    if (connectionStrength >= 70 && relationshipTrend >= 0 && recencyScore >= 50) {
      return 'low';
    } else if (connectionStrength >= 40 && relationshipTrend >= -20 && recencyScore >= 20) {
      return 'medium';
    } else {
      return 'high';
    }
  }
  
  /**
   * 预测下次联系时间
   */
  private predictNextContact(
    communications: CommunicationRecord[],
    meetings: Meeting[],
    contact: Contact
  ): Date | null {
    const allInteractions = [
      ...communications.map(c => c.startTime),
      ...meetings.map(m => m.date),
    ].sort((a, b) => b.getTime() - a.getTime());
    
    if (allInteractions.length < 2) return null;
    
    // 计算平均间隔
    const intervals: number[] = [];
    for (let i = 0; i < Math.min(allInteractions.length - 1, 5); i++) {
      const interval = allInteractions[i].getTime() - allInteractions[i + 1].getTime();
      intervals.push(interval);
    }
    
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    
    // 根据联系人类型调整预测
    const expectedFrequency = this.getExpectedFrequency(contact);
    const expectedInterval = (30 / expectedFrequency) * 24 * 60 * 60 * 1000; // 转换为毫秒
    
    // 使用加权平均
    const predictedInterval = (avgInterval * 0.7 + expectedInterval * 0.3);
    
    const lastInteraction = allInteractions[0];
    return new Date(lastInteraction.getTime() + predictedInterval);
  }
  
  /**
   * 确定关系阶段
   */
  private determineRelationshipPhase(
    connectionStrength: number,
    relationshipTrend: number,
    recencyScore: number
  ): 'building' | 'stable' | 'declining' | 'dormant' {
    if (recencyScore < 10) {
      return 'dormant';
    } else if (relationshipTrend > 20) {
      return 'building';
    } else if (relationshipTrend < -20 || connectionStrength < 30) {
      return 'declining';
    } else {
      return 'stable';
    }
  }
  
  /**
   * 生成关系洞察
   */
  private generateInsights(
    contact: Contact,
    metrics: RelationshipMetrics,
    communications: CommunicationRecord[],
    meetings: Meeting[]
  ): RelationshipInsight[] {
    const insights: RelationshipInsight[] = [];
    
    // 连接强度洞察
    if (metrics.connectionStrength >= 80) {
      insights.push({
        type: 'strength',
        level: 'positive',
        title: '关系非常稳固',
        description: `与${contact.name}的关系强度很高，保持了良好的沟通质量和频率。`,
        confidence: 0.9,
      });
    } else if (metrics.connectionStrength < 30) {
      insights.push({
        type: 'strength',
        level: 'negative',
        title: '关系需要加强',
        description: `与${contact.name}的关系强度较低，建议增加互动频率。`,
        recommendation: '主动发起联系，安排面对面会议或深度沟通。',
        confidence: 0.8,
      });
    }
    
    // 频率洞察
    if (metrics.frequencyScore < 30) {
      insights.push({
        type: 'frequency',
        level: 'negative',
        title: '沟通频率偏低',
        description: '最近的沟通频率低于预期，可能影响关系维护。',
        recommendation: '建议定期安排沟通，保持联系的连续性。',
        confidence: 0.7,
      });
    }
    
    // 质量洞察
    if (metrics.qualityScore >= 80) {
      insights.push({
        type: 'quality',
        level: 'positive',
        title: '沟通质量优秀',
        description: '沟通效果良好，双方互动积极。',
        confidence: 0.8,
      });
    } else if (metrics.qualityScore < 40) {
      insights.push({
        type: 'quality',
        level: 'negative',
        title: '沟通质量待提升',
        description: '最近的沟通效果不够理想，需要改进沟通方式。',
        recommendation: '尝试不同的沟通方式，关注对方的反馈和需求。',
        confidence: 0.7,
      });
    }
    
    // 趋势洞察
    if (metrics.relationshipTrend > 30) {
      insights.push({
        type: 'trend',
        level: 'positive',
        title: '关系持续改善',
        description: '关系质量呈上升趋势，互动效果越来越好。',
        confidence: 0.8,
      });
    } else if (metrics.relationshipTrend < -30) {
      insights.push({
        type: 'trend',
        level: 'negative',
        title: '关系有下降趋势',
        description: '最近的互动质量有所下降，需要关注。',
        recommendation: '主动了解对方近况，寻找改善关系的机会。',
        confidence: 0.8,
      });
    }
    
    // 风险洞察
    if (metrics.riskLevel === 'high') {
      insights.push({
        type: 'risk',
        level: 'negative',
        title: '关系风险较高',
        description: '关系可能面临疏远的风险，需要及时采取行动。',
        recommendation: '尽快安排深度沟通，了解对方需求，重新建立联系。',
        confidence: 0.9,
      });
    }
    
    return insights;
  }
  
  /**
   * 评估数据质量
   */
  private assessDataQuality(
    communications: CommunicationRecord[],
    meetings: Meeting[],
    contact: Contact
  ): number {
    let score = 0;
    
    // 基础信息完整性
    if (contact.name) score += 10;
    if (contact.phone) score += 10;
    if (contact.email) score += 10;
    if (contact.company) score += 10;
    if (contact.relationshipType) score += 10;
    
    // 沟通记录数量
    const totalInteractions = communications.length + meetings.length;
    score += Math.min(30, totalInteractions * 2);
    
    // 沟通记录质量
    const qualityRecords = communications.filter(c => 
      c.effectiveness !== undefined || c.sentiment !== undefined
    ).length;
    score += Math.min(20, qualityRecords * 2);
    
    return Math.min(100, score);
  }
  
  // 辅助方法
  private getExpectedFrequency(contact: Contact): number {
    switch (contact.relationshipType) {
      case 'family': return 8; // 每月8次
      case 'friend': return 4; // 每月4次
      case 'colleague': return 6; // 每月6次
      case 'client': return 3; // 每月3次
      case 'mentor': case 'mentee': return 2; // 每月2次
      default: return 1; // 每月1次
    }
  }
  
  private getSentimentBonus(sentiment: string): number {
    switch (sentiment) {
      case 'very_positive': return 20;
      case 'positive': return 10;
      case 'neutral': return 0;
      case 'negative': return -10;
      case 'very_negative': return -20;
      default: return 0;
    }
  }
  
  private getPriorityBonus(priority?: string): number {
    switch (priority) {
      case 'critical': return 10;
      case 'high': return 5;
      case 'medium': return 0;
      case 'low': return -5;
      default: return 0;
    }
  }
  
  private getOutcomeScore(outcome: string): number {
    switch (outcome) {
      case 'successful': return 80;
      case 'partial': return 60;
      case 'follow_up_needed': return 50;
      case 'unsuccessful': return 30;
      case 'no_response': return 20;
      case 'postponed': return 40;
      default: return 50;
    }
  }
}

// 导出单例实例
export const relationshipAnalyzer = new RelationshipAnalyzer();
export default relationshipAnalyzer;
