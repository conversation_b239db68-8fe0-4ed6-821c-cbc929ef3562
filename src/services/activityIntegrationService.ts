/**
 * 活动集成服务
 * 处理沟通记录、活动和关系之间的关联
 */

import { Contact, BusinessEvent, Communication, ContactRelationship } from '../types';

// 活动关联类型
export type ActivityAssociationType = 
  | 'communication_to_event'    // 沟通记录关联到活动
  | 'event_to_communication'    // 活动生成沟通记录
  | 'event_to_relationship'     // 活动建立关系
  | 'relationship_to_event';    // 关系来源于活动

// 活动关联接口
export interface ActivityAssociation {
  id: string;
  type: ActivityAssociationType;
  sourceId: string;
  targetId: string;
  context?: string;
  confidence: number; // 关联置信度 0-1
  createdAt: Date;
  metadata?: Record<string, any>;
}

// 活动洞察
export interface ActivityInsight {
  id: string;
  type: 'relationship_opportunity' | 'follow_up_needed' | 'network_gap' | 'engagement_pattern';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionable: boolean;
  relatedEntities: {
    contacts?: string[];
    events?: string[];
    communications?: string[];
  };
  suggestedActions: string[];
  createdAt: Date;
  expiresAt?: Date;
}

/**
 * 活动集成服务类
 */
export class ActivityIntegrationService {
  private associations: ActivityAssociation[] = [];
  private insights: ActivityInsight[] = [];

  /**
   * 分析沟通记录与活动的关联
   */
  analyzeCommunicationEventAssociations(
    communications: Communication[],
    events: BusinessEvent[]
  ): ActivityAssociation[] {
    const associations: ActivityAssociation[] = [];

    communications.forEach(comm => {
      events.forEach(event => {
        const confidence = this.calculateCommunicationEventConfidence(comm, event);
        
        if (confidence > 0.6) {
          associations.push({
            id: `comm_event_${comm.id}_${event.id}`,
            type: 'communication_to_event',
            sourceId: comm.id,
            targetId: event.id,
            confidence,
            createdAt: new Date(),
            metadata: {
              communicationType: comm.type,
              eventType: event.eventType,
              timeDifference: Math.abs(new Date(comm.date).getTime() - new Date(event.date).getTime())
            }
          });
        }
      });
    });

    return associations;
  }

  /**
   * 分析活动建立的关系
   */
  analyzeEventRelationships(
    events: BusinessEvent[],
    relationships: ContactRelationship[]
  ): ActivityAssociation[] {
    const associations: ActivityAssociation[] = [];

    events.forEach(event => {
      // 分析活动参与者之间的关系
      const participantIds = event.participants.map(p => p.contactId);
      
      relationships.forEach(rel => {
        if (participantIds.includes(rel.fromContactId) && 
            participantIds.includes(rel.toContactId)) {
          
          const confidence = this.calculateEventRelationshipConfidence(event, rel);
          
          if (confidence > 0.5) {
            associations.push({
              id: `event_rel_${event.id}_${rel.id}`,
              type: 'event_to_relationship',
              sourceId: event.id,
              targetId: rel.id,
              confidence,
              createdAt: new Date(),
              metadata: {
                eventType: event.eventType,
                relationshipType: rel.type,
                participantCount: event.participants.length
              }
            });
          }
        }
      });
    });

    return associations;
  }

  /**
   * 生成活动洞察
   */
  generateActivityInsights(
    contacts: Contact[],
    events: BusinessEvent[],
    communications: Communication[],
    relationships: ContactRelationship[]
  ): ActivityInsight[] {
    const insights: ActivityInsight[] = [];

    // 关系机会洞察
    const relationshipOpportunities = this.findRelationshipOpportunities(contacts, events);
    insights.push(...relationshipOpportunities);

    // 跟进需求洞察
    const followUpNeeds = this.findFollowUpNeeds(events, communications);
    insights.push(...followUpNeeds);

    // 网络缺口洞察
    const networkGaps = this.findNetworkGaps(contacts, events);
    insights.push(...networkGaps);

    // 参与模式洞察
    const engagementPatterns = this.analyzeEngagementPatterns(contacts, events, communications);
    insights.push(...engagementPatterns);

    return insights.sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * 建议活动后的沟通记录
   */
  suggestPostEventCommunications(
    event: BusinessEvent,
    contacts: Contact[]
  ): Array<{
    contactId: string;
    suggestedType: Communication['type'];
    suggestedContent: string;
    priority: 'low' | 'medium' | 'high';
    timing: 'immediate' | 'within_24h' | 'within_week';
  }> {
    const suggestions: Array<{
      contactId: string;
      suggestedType: Communication['type'];
      suggestedContent: string;
      priority: 'low' | 'medium' | 'high';
      timing: 'immediate' | 'within_24h' | 'within_week';
    }> = [];

    event.participants.forEach(participant => {
      const contact = contacts.find(c => c.id === participant.contactId);
      if (!contact) return;

      let priority: 'low' | 'medium' | 'high' = 'medium';
      let timing: 'immediate' | 'within_24h' | 'within_week' = 'within_24h';
      let suggestedContent = '';

      // 根据互动级别确定优先级和内容
      switch (participant.interactionLevel) {
        case 'extensive':
          priority = 'high';
          timing = 'immediate';
          suggestedContent = `感谢您在"${event.title}"活动中的精彩交流，期待进一步合作。`;
          break;
        case 'moderate':
          priority = 'medium';
          timing = 'within_24h';
          suggestedContent = `很高兴在"${event.title}"活动中认识您，希望保持联系。`;
          break;
        case 'brief':
          priority = 'low';
          timing = 'within_week';
          suggestedContent = `感谢参加"${event.title}"活动，期待未来有机会深入交流。`;
          break;
        default:
          priority = 'low';
          timing = 'within_week';
          suggestedContent = `感谢参加"${event.title}"活动。`;
      }

      // 根据关系上下文调整
      if (participant.relationshipContext === 'first_met') {
        priority = Math.max(priority === 'low' ? 1 : priority === 'medium' ? 2 : 3, 2) === 2 ? 'medium' : 'high';
        suggestedContent = `很高兴在"${event.title}"活动中初次认识您，${suggestedContent}`;
      }

      suggestions.push({
        contactId: participant.contactId,
        suggestedType: participant.businessCardExchanged ? 'email' : 'message',
        suggestedContent,
        priority,
        timing
      });
    });

    return suggestions;
  }

  /**
   * 获取联系人的活动参与历史
   */
  getContactEventHistory(
    contactId: string,
    events: BusinessEvent[]
  ): Array<{
    event: BusinessEvent;
    participationDetails: BusinessEvent['participants'][0];
    relationshipsFormed: number;
    followUpCompleted: boolean;
  }> {
    return events
      .filter(event => event.participants.some(p => p.contactId === contactId))
      .map(event => {
        const participation = event.participants.find(p => p.contactId === contactId)!;
        const relationshipsFormed = event.participants.filter(p => 
          p.contactId !== contactId && p.relationshipContext === 'first_met'
        ).length;
        
        const followUpCompleted = event.followUps.some(f => 
          f.contactId === contactId && f.isCompleted
        );

        return {
          event,
          participationDetails: participation,
          relationshipsFormed,
          followUpCompleted
        };
      })
      .sort((a, b) => new Date(b.event.date).getTime() - new Date(a.event.date).getTime());
  }

  /**
   * 计算沟通记录与活动的关联置信度
   */
  private calculateCommunicationEventConfidence(
    communication: Communication,
    event: BusinessEvent
  ): number {
    let confidence = 0;

    // 时间相关性
    const timeDiff = Math.abs(new Date(communication.date).getTime() - new Date(event.date).getTime());
    const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
    
    if (daysDiff <= 1) confidence += 0.4;
    else if (daysDiff <= 3) confidence += 0.3;
    else if (daysDiff <= 7) confidence += 0.2;
    else if (daysDiff <= 14) confidence += 0.1;

    // 参与者相关性
    const isParticipant = event.participants.some(p => p.contactId === communication.primaryContactId);
    if (isParticipant) confidence += 0.3;

    // 内容相关性（简化版本）
    const eventKeywords = [event.title, event.category, ...event.tags].join(' ').toLowerCase();
    const commContent = communication.content.toLowerCase();
    
    if (eventKeywords.split(' ').some(keyword => commContent.includes(keyword))) {
      confidence += 0.2;
    }

    // 沟通类型相关性
    if (communication.type === 'email' && event.isVirtual) confidence += 0.1;
    if (communication.type === 'phone' && !event.isVirtual) confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  /**
   * 计算活动与关系的关联置信度
   */
  private calculateEventRelationshipConfidence(
    event: BusinessEvent,
    relationship: ContactRelationship
  ): number {
    let confidence = 0;

    // 时间相关性
    const timeDiff = Math.abs(new Date(relationship.createdAt).getTime() - new Date(event.date).getTime());
    const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
    
    if (daysDiff <= 7) confidence += 0.4;
    else if (daysDiff <= 30) confidence += 0.2;

    // 关系类型相关性
    if (relationship.type === 'introduced_by' || relationship.type === 'introduced_to') {
      confidence += 0.3;
    }

    // 活动类型相关性
    if (event.eventType === 'networking' || event.eventType === 'exhibition') {
      confidence += 0.2;
    }

    // 介绍人参与
    if (relationship.introducedBy && 
        event.participants.some(p => p.contactId === relationship.introducedBy)) {
      confidence += 0.3;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * 寻找关系机会
   */
  private findRelationshipOpportunities(
    contacts: Contact[],
    events: BusinessEvent[]
  ): ActivityInsight[] {
    const insights: ActivityInsight[] = [];

    // 分析即将到来的活动中的潜在连接
    const upcomingEvents = events.filter(event => 
      new Date(event.date) > new Date() && 
      event.status === 'confirmed'
    );

    upcomingEvents.forEach(event => {
      const participantIds = event.participants.map(p => p.contactId);
      const participants = contacts.filter(c => participantIds.includes(c.id));
      
      // 寻找同行业但未建立关系的参与者
      const industryGroups = new Map<string, Contact[]>();
      participants.forEach(contact => {
        if (contact.industry) {
          if (!industryGroups.has(contact.industry)) {
            industryGroups.set(contact.industry, []);
          }
          industryGroups.get(contact.industry)!.push(contact);
        }
      });

      industryGroups.forEach((industryContacts, industry) => {
        if (industryContacts.length >= 2) {
          insights.push({
            id: `rel_opp_${event.id}_${industry}`,
            type: 'relationship_opportunity',
            title: `${industry}行业网络机会`,
            description: `在"${event.title}"活动中，有${industryContacts.length}位${industry}行业的参与者，这是建立行业网络的好机会。`,
            priority: 'medium',
            actionable: true,
            relatedEntities: {
              contacts: industryContacts.map(c => c.id),
              events: [event.id]
            },
            suggestedActions: [
              '主动介绍同行业参与者',
              '组织行业小组讨论',
              '交换联系方式'
            ],
            createdAt: new Date()
          });
        }
      });
    });

    return insights;
  }

  /**
   * 寻找跟进需求
   */
  private findFollowUpNeeds(
    events: BusinessEvent[],
    communications: Communication[]
  ): ActivityInsight[] {
    const insights: ActivityInsight[] = [];

    // 分析最近完成的活动
    const recentCompletedEvents = events.filter(event => 
      event.status === 'completed' &&
      new Date(event.date) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
    );

    recentCompletedEvents.forEach(event => {
      const participantsNeedingFollowUp = event.participants.filter(p => 
        p.followUpRequired && p.interactionLevel !== 'none'
      );

      if (participantsNeedingFollowUp.length > 0) {
        insights.push({
          id: `followup_${event.id}`,
          type: 'follow_up_needed',
          title: `"${event.title}"活动跟进`,
          description: `有${participantsNeedingFollowUp.length}位参与者需要跟进，建议在活动结束后48小时内联系。`,
          priority: 'high',
          actionable: true,
          relatedEntities: {
            contacts: participantsNeedingFollowUp.map(p => p.contactId),
            events: [event.id]
          },
          suggestedActions: [
            '发送感谢邮件',
            '分享活动资料',
            '安排后续会面'
          ],
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3天后过期
        });
      }
    });

    return insights;
  }

  /**
   * 寻找网络缺口
   */
  private findNetworkGaps(
    contacts: Contact[],
    events: BusinessEvent[]
  ): ActivityInsight[] {
    const insights: ActivityInsight[] = [];

    // 分析行业分布
    const industryCount = new Map<string, number>();
    contacts.forEach(contact => {
      if (contact.industry) {
        industryCount.set(contact.industry, (industryCount.get(contact.industry) || 0) + 1);
      }
    });

    // 分析活动参与的行业分布
    const eventIndustries = new Set<string>();
    events.forEach(event => {
      if (event.category) {
        eventIndustries.add(event.category);
      }
    });

    // 找出参与活动但联系人较少的行业
    eventIndustries.forEach(industry => {
      const contactCount = industryCount.get(industry) || 0;
      if (contactCount < 3) {
        insights.push({
          id: `network_gap_${industry}`,
          type: 'network_gap',
          title: `${industry}行业网络缺口`,
          description: `您在${industry}行业只有${contactCount}个联系人，但参与了相关活动，建议扩展该行业网络。`,
          priority: 'medium',
          actionable: true,
          relatedEntities: {
            contacts: contacts.filter(c => c.industry === industry).map(c => c.id)
          },
          suggestedActions: [
            `参加更多${industry}行业活动`,
            `寻找${industry}行业引荐`,
            `加入${industry}专业社群`
          ],
          createdAt: new Date()
        });
      }
    });

    return insights;
  }

  /**
   * 分析参与模式
   */
  private analyzeEngagementPatterns(
    contacts: Contact[],
    events: BusinessEvent[],
    communications: Communication[]
  ): ActivityInsight[] {
    const insights: ActivityInsight[] = [];

    // 分析活动参与频率
    const monthlyEventCount = events.filter(event => 
      new Date(event.date) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length;

    if (monthlyEventCount < 2) {
      insights.push({
        id: 'low_event_participation',
        type: 'engagement_pattern',
        title: '活动参与度较低',
        description: `最近30天只参与了${monthlyEventCount}个活动，建议增加活动参与以扩展网络。`,
        priority: 'low',
        actionable: true,
        relatedEntities: {},
        suggestedActions: [
          '寻找相关行业活动',
          '参加在线研讨会',
          '组织小型聚会'
        ],
        createdAt: new Date()
      });
    }

    return insights;
  }
}

// 导出默认实例
export const activityIntegrationService = new ActivityIntegrationService();
