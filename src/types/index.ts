// src/types/index.ts

// 联系人分组
export interface ContactGroup {
  id: string;
  name: string;
  description?: string;
  color: string; // 分组颜色
  icon: string; // 分组图标名称
  isDefault: boolean; // 是否为默认分组
  contactCount: number; // 分组内联系人数量
  createdAt: Date;
  updatedAt: Date;
}

// 社交媒体链接
export interface SocialLink {
  id: string;
  platform: 'linkedin' | 'twitter' | 'facebook' | 'instagram' | 'github' | 'website' | 'other';
  url: string;
  username?: string;
  displayName?: string;
}

// 联系方式
export interface ContactMethod {
  id: string;
  type: 'phone' | 'email' | 'address';
  label: 'work' | 'home' | 'mobile' | 'other';
  value: string;
  isPrimary: boolean;
}

// 地址信息
export interface Address {
  id: string;
  label: 'work' | 'home' | 'other';
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  isPrimary: boolean;
}

// 重要日期
export interface ImportantDate {
  id: string;
  type: 'birthday' | 'anniversary' | 'work_anniversary' | 'other';
  date: string; // ISO date string
  label?: string;
  reminder?: boolean; // 是否设置提醒
}

// 自定义字段
export interface CustomField {
  id: string;
  name: string;
  value: string;
  type: 'text' | 'number' | 'date' | 'url' | 'email';
}

export interface Tag {
  id: string;
  name: string;
  color?: string; // Color for UI display, can be optional
}

export type TagCategory = 'industry' | 'relationship' | 'personal';

// 关系类型
export type RelationshipType =
  | 'colleague'
  | 'friend'
  | 'family'
  | 'client'
  | 'vendor'
  | 'mentor'
  | 'mentee'
  | 'acquaintance'
  | 'other';

// 联系人重要程度
export type ContactPriority = 'low' | 'medium' | 'high' | 'critical';

// 会议相关类型
export interface Meeting {
  id: string;
  title: string;
  description?: string;
  date: Date;
  startTime: Date;
  endTime?: Date;
  location?: string;
  isVirtual: boolean;
  meetingLink?: string;
  participantIds: string[];
  organizerId: string;
  notes?: string;
  tasks: MeetingTask[];
  status: MeetingStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface MeetingTask {
  id: string;
  title: string;
  description?: string;
  assigneeId?: string;
  dueDate?: Date;
  isCompleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type MeetingStatus = 'scheduled' | 'in-progress' | 'completed' | 'cancelled';

export interface MeetingParticipant {
  contactId: string;
  status: 'invited' | 'accepted' | 'declined' | 'tentative';
  responseDate?: Date;
}

export interface Contact {
  id: string;

  // 基本信息
  name: string; // Combined first and last name, or primary display name
  firstName?: string;
  lastName?: string;
  middleName?: string;
  nickname?: string;
  title?: string; // Mr., Ms., Dr., etc.

  // 头像和照片
  avatar: string; // URL or local path for display
  photo?: string; // Potentially raw URI from image picker

  // 工作信息
  company?: string;
  position?: string; // Job title
  department?: string;
  role?: string; // Role in relation to user

  // 联系方式 - 使用新的结构化方式
  phones: ContactMethod[]; // 多个电话号码
  emails: ContactMethod[]; // 多个邮箱地址
  addresses: Address[]; // 多个地址

  // 兼容性字段（逐步迁移）
  email?: string; // 主要邮箱，向后兼容
  phone?: string; // 主要电话，向后兼容
  address?: string; // 主要地址，向后兼容
  location?: string; // 位置信息，向后兼容

  // 社交媒体和网站
  socialLinks: SocialLink[];
  website?: string;

  // 重要日期
  importantDates: ImportantDate[];
  birthday?: string; // ISO date string, 向后兼容

  // 关系和分类
  relationshipType: RelationshipType;
  priority: ContactPriority;
  groupIds: string[]; // 所属分组ID列表
  tags?: Tag[]; // 标签

  // 交互信息
  lastContactDate?: string; // 最后联系时间
  nextFollowUpDate?: string; // 下次跟进时间
  contactFrequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'rarely';

  // 自定义字段
  customFields: CustomField[];

  // 备注和状态
  notes?: string;
  isFavorite?: boolean;
  isArchived?: boolean; // 是否归档
  isBlocked?: boolean; // 是否屏蔽

  // 元数据
  source?: string; // 联系人来源（手动添加、导入、名片扫描等）
  createdAt: Date;
  updatedAt: Date;
  lastViewedAt?: Date; // 最后查看时间

  // 统计信息
  interactionCount?: number; // 交互次数
  connectionStrength?: number; // 连接强度 (0-100)
}

// Example of centralizing other types if needed in the future

// export interface Reminder {
//   id: string;
//   title: string;
//   dueDate: string; // ISO date string
//   priority: 'high' | 'medium' | 'low';
//   isCompleted: boolean;
// }


export type BottomTabParamList = {
  HomeTab: undefined;
  ContactsTab: undefined;
  CreateTab: undefined; // This is for the button, not a screen
  GraphTab: undefined;
  TasksTab: undefined;
};

export type RootStackParamList = {
  MainTabs: { screen: keyof BottomTabParamList };
  Home: undefined;
  Settings: undefined;
  ContactDetail: { contactId?: string; contact?: Contact; isEditedJustNow?: boolean };
  ContactCreate: { contact?: Contact };
  Contacts: { newContact?: Contact }; // Added for passing new contact data
  MeetingRecord: { contactId?: string };
  NetworkVisual: { focusedContactId?: string };
  ReminderCenter: undefined;
  ReminderDetail: { reminderId: string };
  ReminderCreate: { contactId?: string; type?: string };
};

// Export reminder types
export * from './reminder';

// Export communication types
export * from './communication';

