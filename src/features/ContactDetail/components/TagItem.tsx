import React from 'react';
import { Pressable, Badge, BadgeText, BadgeIcon, HStack, VStack, Text, Icon } from '@gluestack-ui/themed';
import { Briefcase, DollarSign } from 'lucide-react-native';
import { Tag } from '../hooks/useContactDetail';

interface TagItemProps {
  tag: Tag;
  onPress: (tag: Tag) => void;
}

const TagItem: React.FC<TagItemProps> = ({ tag, onPress }) => {
  // Define a base color from the tag, which should be a Gluestack color token.
  const baseColor = tag.color || '$primary500';

  return (
    <Pressable onPress={() => onPress(tag)} mb={'$2'} mr={'$2'}>
      <Badge 
        size="md" 
        variant="solid" // Use solid variant for a filled look
        action="muted"
        sx={{
          // Use theme tokens for colors. The `resolveToken` utility or direct usage is preferred.
          // Here, we assume `tag.color` is a valid token like '$primary500'.
          // To create a lighter background, we can target a lighter shade from the theme, e.g., '$primary100'.
          // For simplicity, let's use a slight opacity on the base color if it's a hex, but ideally, we'd use tokens.
          bg: `${baseColor}20`, // Acknowledging this is a workaround if not using tokens
          borderColor: 'transparent', // No border for a cleaner look
          borderRadius: '$full',
          _text: {
            color: baseColor,
            fontWeight: '$medium',
          },
        }}
      >
        <HStack alignItems="center">
          <BadgeText>{tag.name}</BadgeText>
          {/* Render icons inside the main HStack for better alignment */}
          {tag.projectName && (
            <HStack alignItems="center" ml={'$2'}>
              <Icon as={Briefcase} size="xs" color={baseColor} />
              <Text size="xs" color={baseColor} ml={'$1'}>{tag.projectName}</Text>
            </HStack>
          )}
          {tag.opportunityName && (
            <HStack alignItems="center" ml={'$2'}>
              <Icon as={DollarSign} size="xs" color={baseColor} />
              <Text size="xs" color={baseColor} ml={'$1'}>{tag.opportunityName}</Text>
            </HStack>
          )}
        </HStack>
      </Badge>
    </Pressable>
  );
};



export default TagItem;
