import React, { useState, useEffect } from 'react';
import {
  Platform
} from 'react-native';
import {
  Modal as GSModal,
  ModalBackdrop,
  ModalContent,
  <PERSON><PERSON><PERSON>eader,
  ModalCloseButton,
  ModalBody,
  Modal<PERSON>ooter,
  Heading,
  Text,
  Textarea,
  TextareaInput,
  Button,
  ButtonText,
  Icon,
  VStack,
  HStack,
  Box,
  Pressable
} from '@gluestack-ui/themed';
import { XCircle } from 'lucide-react-native'; // For close icon

interface NotesEditModalProps {
  visible: boolean;
  initialNotes: string;
  onSave: (newNotes: string) => void;
  onClose: () => void;
}

const NotesEditModal: React.FC<NotesEditModalProps> = ({ visible, initialNotes, onSave, onClose }) => {
  const [notes, setNotes] = useState(initialNotes);

  useEffect(() => {
    if (visible) {
      setNotes(initialNotes);
    }
  }, [visible, initialNotes]);

  const handleSave = () => {
    onSave(notes);
  };

  return (
    <GSModal isOpen={visible} onClose={onClose} finalFocusRef={undefined}>
      <ModalBackdrop />
      <ModalContent w={'$full'} maxW={'$lg'} m={'$4'}>
        <ModalHeader>
          <Heading size="lg">Edit Notes</Heading>
          <ModalCloseButton>
            <Icon as={XCircle} />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody>
          <Textarea
            size="md"
            isReadOnly={false}
            isInvalid={false}
            isDisabled={false}
            w={'$full'}
            h={200} // Adjust height as needed
          >
            <TextareaInput
              placeholder="Enter your notes here..."
              value={notes}
              onChangeText={setNotes}
              textAlignVertical="top"
              role={Platform.OS === 'web' ? 'textbox' : undefined} // Accessibility for web
            />
          </Textarea>
        </ModalBody>
        <ModalFooter>
          <HStack space="sm">
            <Button variant="outline" action="secondary" onPress={onClose}>
              <ButtonText>Cancel</ButtonText>
            </Button>
            <Button onPress={handleSave}>
              <ButtonText>Save</ButtonText>
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </GSModal>
  );
};

export default NotesEditModal;
