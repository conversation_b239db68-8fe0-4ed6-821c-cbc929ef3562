import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';

// 导入语言文件
import en from './locales/en.json';
import zh from './locales/zh.json';

const resources = {
  en: {
    translation: en.translation, // 使用导入的 en.json 内容
  },
  zh: {
    translation: zh.translation, // 使用导入的 zh.json 内容
  },
};

// 获取设备语言，并只取语言代码部分 (e.g., 'en' from 'en-US')
const deviceLanguage = Localization.getLocales()[0]?.languageCode || 'en';

i18n
  .use(initReactI18next) // 将 i18n 实例传递给 react-i18next
  .init({
    resources,
    lng: deviceLanguage, // 设置初始语言为设备语言或英语备用
    fallbackLng: 'en', // 如果当前语言没有对应的翻译，则回退到英语
    interpolation: {
      escapeValue: false, // React 已经做了 XSS 防护
    },
    supportedLngs: ['en', 'zh'], // 声明支持的语言
  });

export default i18n;
