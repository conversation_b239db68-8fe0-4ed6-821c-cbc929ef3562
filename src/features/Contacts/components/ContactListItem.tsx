import React from 'react';
import { Pressable, HStack, VStack, Avatar, Text, Icon, AvatarFallbackText, AvatarImage, Badge, BadgeText, useToast, Toast, ToastTitle, ToastDescription } from '@gluestack-ui/themed';
import { ChevronRight, Heart, Star, Edit, Trash2, Phone, MessageCircle, Calendar } from 'lucide-react-native';
import { Contact } from '../../../types';
import { useContactsStore } from '../../../store';
import { SwipeableRow, SwipeActions, ContextMenu, ContextMenuItems } from '../../../components/ui';
import { useNavigation } from '@react-navigation/native';

interface ContactListItemProps {
  contact: Contact;
  onPress: () => void;
  showFavoriteButton?: boolean;
  enableSwipeActions?: boolean;
  enableContextMenu?: boolean;
}

const ContactListItem: React.FC<ContactListItemProps> = ({
  contact,
  onPress,
  showFavoriteButton = true,
  enableSwipeActions = true,
  enableContextMenu = true,
}) => {
  const { toggleFavorite, deleteContact } = useContactsStore();
  const navigation = useNavigation();
  const toast = useToast();

  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleToggleFavorite = (e?: any) => {
    e?.stopPropagation(); // Prevent triggering onPress
    toggleFavorite(contact.id);

    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={`toast-${id}`} action="success" variant="accent">
          <ToastTitle>{contact.isFavorite ? '已取消收藏' : '已添加收藏'}</ToastTitle>
          <ToastDescription>
            {contact.name} {contact.isFavorite ? '已从收藏中移除' : '已添加到收藏'}
          </ToastDescription>
        </Toast>
      )
    });
  };

  const handleEdit = () => {
    navigation.navigate('ContactCreate', { contact });
  };

  const handleDelete = () => {
    deleteContact(contact.id);
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={`toast-${id}`} action="success" variant="accent">
          <ToastTitle>联系人已删除</ToastTitle>
          <ToastDescription>{contact.name} 已从联系人列表中删除</ToastDescription>
        </Toast>
      )
    });
  };

  const handleCall = () => {
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={`toast-${id}`} action="info" variant="accent">
          <ToastTitle>拨打电话</ToastTitle>
          <ToastDescription>正在拨打 {contact.name} 的电话</ToastDescription>
        </Toast>
      )
    });
  };

  const handleMessage = () => {
    toast.show({
      placement: "top",
      render: ({ id }) => (
        <Toast nativeID={`toast-${id}`} action="info" variant="accent">
          <ToastTitle>发送消息</ToastTitle>
          <ToastDescription>正在给 {contact.name} 发送消息</ToastDescription>
        </Toast>
      )
    });
  };

  const handleCreateEvent = () => {
    navigation.navigate('BusinessEventRecord', { contactId: contact.id });
  };

  // 滑动操作配置
  const leftActions = [
    SwipeActions.call(handleCall),
    SwipeActions.message(handleMessage),
  ];

  const rightActions = [
    SwipeActions.favorite(handleToggleFavorite, contact.isFavorite),
    SwipeActions.edit(handleEdit),
    SwipeActions.delete(handleDelete),
  ];

  // 长按菜单配置
  const contextMenuItems = [
    ContextMenuItems.viewDetails(onPress),
    ContextMenuItems.edit(handleEdit),
    ContextMenuItems.call(handleCall),
    ContextMenuItems.message(handleMessage),
    ContextMenuItems.createEvent(handleCreateEvent),
    ContextMenuItems.favorite(handleToggleFavorite, contact.isFavorite),
    ContextMenuItems.delete(handleDelete),
  ];

  const ContactCard = (
    <Pressable
      onPress={onPress}
      p="$4"
      borderBottomWidth="$1"
      borderColor="$trueGray200"
      sx={{
        ':hover': {
          bg: '$trueGray100',
        },
        '_dark': {
          borderColor: '$trueGray700',
          ':hover': {
            bg: '$trueGray800',
          },
        },
      }}
    >
      <HStack space="md" alignItems="center">
        <Avatar size="md" bg="$primary300">
          <AvatarFallbackText>{getInitials(contact.name)}</AvatarFallbackText>
          {contact.avatar && <AvatarImage source={{ uri: contact.avatar }} alt={contact.name} />}
        </Avatar>

        <VStack flex={1} space="xs">
          <HStack alignItems="center" space="sm">
            <Text size="md" fontWeight="$bold" color="$coolGray800" sx={{ _dark: { color: '$coolGray100' } }}>
              {contact.name}
            </Text>
            {contact.isFavorite && (
              <Icon as={Star} size="sm" color="$yellow500" />
            )}
          </HStack>

          <Text size="sm" color="$coolGray600" sx={{ _dark: { color: '$coolGray400' } }}>
            {contact.company && contact.position
              ? `${contact.position} at ${contact.company}`
              : contact.company || contact.position || 'No company info'
            }
          </Text>

          {contact.email && (
            <Text size="xs" color="$coolGray500" sx={{ _dark: { color: '$coolGray500' } }}>
              {contact.email}
            </Text>
          )}

          {contact.tags && contact.tags.length > 0 && (
            <HStack space="xs" flexWrap="wrap">
              {contact.tags.slice(0, 2).map((tag, index) => (
                <Badge key={index} size="sm" variant="outline" action="muted">
                  <BadgeText>{typeof tag === 'string' ? tag : tag.name}</BadgeText>
                </Badge>
              ))}
              {contact.tags.length > 2 && (
                <Badge size="sm" variant="outline" action="muted">
                  <BadgeText>+{contact.tags.length - 2}</BadgeText>
                </Badge>
              )}
            </HStack>
          )}
        </VStack>

        <HStack alignItems="center" space="sm">
          {showFavoriteButton && (
            <Pressable onPress={handleToggleFavorite} p="$2">
              <Icon
                as={Heart}
                size="sm"
                color={contact.isFavorite ? "$red500" : "$coolGray400"}
                fill={contact.isFavorite ? "$red500" : "transparent"}
              />
            </Pressable>
          )}
          <Icon as={ChevronRight} color="$coolGray400" />
        </HStack>
      </HStack>
    </Pressable>
  );

  // 根据配置决定是否启用滑动和长按功能
  if (enableSwipeActions && enableContextMenu) {
    return (
      <SwipeableRow
        leftActions={leftActions}
        rightActions={rightActions}
      >
        <ContextMenu items={contextMenuItems}>
          {ContactCard}
        </ContextMenu>
      </SwipeableRow>
    );
  } else if (enableSwipeActions) {
    return (
      <SwipeableRow
        leftActions={leftActions}
        rightActions={rightActions}
      >
        {ContactCard}
      </SwipeableRow>
    );
  } else if (enableContextMenu) {
    return (
      <ContextMenu items={contextMenuItems}>
        {ContactCard}
      </ContextMenu>
    );
  }

  return ContactCard;
};

export default ContactListItem;
