/**
 * 沟通记录状态管理
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  CommunicationRecord, 
  CommunicationStats, 
  CommunicationAnalysis,
  CommunicationFilter,
  CommunicationTemplate,
  CommunicationType,
  CommunicationDirection,
  CommunicationSentiment,
  CommunicationOutcome
} from '../types';

interface CommunicationState {
  // 数据状态
  communications: CommunicationRecord[];
  templates: CommunicationTemplate[];
  analyses: Record<string, CommunicationAnalysis>; // contactId -> analysis
  
  // UI状态
  isLoading: boolean;
  error: string | null;
  selectedCommunication: CommunicationRecord | null;
  
  // 过滤和排序
  filter: CommunicationFilter;
  sortBy: 'date' | 'type' | 'sentiment' | 'effectiveness';
  sortOrder: 'asc' | 'desc';
  
  // 分析状态
  isAnalyzing: boolean;
  lastAnalysisUpdate: Date | null;
}

interface CommunicationActions {
  // 沟通记录CRUD操作
  addCommunication: (communication: Omit<CommunicationRecord, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateCommunication: (id: string, updates: Partial<CommunicationRecord>) => Promise<void>;
  deleteCommunication: (id: string) => Promise<void>;
  
  // 批量操作
  addMultipleCommunications: (communications: Omit<CommunicationRecord, 'id' | 'createdAt' | 'updatedAt'>[]) => Promise<void>;
  deleteMultipleCommunications: (ids: string[]) => Promise<void>;
  archiveCommunications: (ids: string[]) => Promise<void>;
  
  // 模板管理
  addTemplate: (template: Omit<CommunicationTemplate, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateTemplate: (id: string, updates: Partial<CommunicationTemplate>) => void;
  deleteTemplate: (id: string) => void;
  
  // 过滤和搜索
  setFilter: (filter: Partial<CommunicationFilter>) => void;
  clearFilter: () => void;
  setSortBy: (sortBy: 'date' | 'type' | 'sentiment' | 'effectiveness') => void;
  setSortOrder: (order: 'asc' | 'desc') => void;
  
  // 分析功能
  analyzeContactCommunications: (contactId: string) => Promise<CommunicationAnalysis>;
  updateAnalysis: (contactId: string, analysis: CommunicationAnalysis) => void;
  refreshAllAnalyses: () => Promise<void>;
  
  // 统计功能
  getContactStats: (contactId: string) => CommunicationStats;
  getOverallStats: () => CommunicationStats;
  getCommunicationTrends: (contactId?: string) => any;
  
  // 快速操作
  recordQuickCall: (contactId: string, duration: number, sentiment?: CommunicationSentiment) => Promise<void>;
  recordQuickMessage: (contactId: string, content: string, sentiment?: CommunicationSentiment) => Promise<void>;
  recordQuickMeeting: (contactId: string, duration: number, topics: string[]) => Promise<void>;
  
  // 选择和UI
  setSelectedCommunication: (communication: CommunicationRecord | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 数据管理
  importCommunications: (communications: CommunicationRecord[]) => Promise<void>;
  exportCommunications: (contactId?: string) => Promise<CommunicationRecord[]>;
  clearAllCommunications: () => void;
}

type CommunicationStore = CommunicationState & CommunicationActions;

// 辅助函数
const generateId = () => `comm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const sortCommunications = (
  communications: CommunicationRecord[], 
  sortBy: 'date' | 'type' | 'sentiment' | 'effectiveness', 
  sortOrder: 'asc' | 'desc'
): CommunicationRecord[] => {
  return [...communications].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'date':
        comparison = a.startTime.getTime() - b.startTime.getTime();
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
      case 'sentiment':
        const sentimentOrder = { very_negative: 1, negative: 2, neutral: 3, positive: 4, very_positive: 5 };
        comparison = (sentimentOrder[a.sentiment || 'neutral'] || 3) - (sentimentOrder[b.sentiment || 'neutral'] || 3);
        break;
      case 'effectiveness':
        comparison = (a.effectiveness || 0) - (b.effectiveness || 0);
        break;
    }
    
    return sortOrder === 'asc' ? comparison : -comparison;
  });
};

const filterCommunications = (
  communications: CommunicationRecord[],
  filter: CommunicationFilter
): CommunicationRecord[] => {
  return communications.filter(comm => {
    // 联系人过滤
    if (filter.contactIds && filter.contactIds.length > 0) {
      if (!filter.contactIds.includes(comm.primaryContactId)) return false;
    }
    
    // 类型过滤
    if (filter.types && filter.types.length > 0) {
      if (!filter.types.includes(comm.type)) return false;
    }
    
    // 方向过滤
    if (filter.directions && filter.directions.length > 0) {
      if (!filter.directions.includes(comm.direction)) return false;
    }
    
    // 情感过滤
    if (filter.sentiments && filter.sentiments.length > 0) {
      if (!comm.sentiment || !filter.sentiments.includes(comm.sentiment)) return false;
    }
    
    // 结果过滤
    if (filter.outcomes && filter.outcomes.length > 0) {
      if (!comm.outcome || !filter.outcomes.includes(comm.outcome)) return false;
    }
    
    // 日期范围过滤
    if (filter.dateRange) {
      const commDate = comm.startTime;
      if (commDate < filter.dateRange.start || commDate > filter.dateRange.end) return false;
    }
    
    // 重要程度过滤
    if (filter.importance) {
      const importance = comm.importance || 0;
      if (importance < filter.importance.min || importance > filter.importance.max) return false;
    }
    
    // 效果过滤
    if (filter.effectiveness) {
      const effectiveness = comm.effectiveness || 0;
      if (effectiveness < filter.effectiveness.min || effectiveness > filter.effectiveness.max) return false;
    }
    
    // 后续跟进过滤
    if (filter.hasFollowUp !== undefined) {
      if (comm.followUpRequired !== filter.hasFollowUp) return false;
    }
    
    // 归档状态过滤
    if (filter.isArchived !== undefined) {
      if (comm.isArchived !== filter.isArchived) return false;
    }
    
    // 搜索查询过滤
    if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase();
      const searchableText = [
        comm.subject,
        comm.content,
        ...(comm.topics || []),
        ...(comm.keywords || [])
      ].join(' ').toLowerCase();
      
      if (!searchableText.includes(query)) return false;
    }
    
    return true;
  });
};

// 分析函数
const analyzeCommunications = (communications: CommunicationRecord[], contactId: string): CommunicationAnalysis => {
  const contactComms = communications.filter(c => c.primaryContactId === contactId);
  
  if (contactComms.length === 0) {
    return {
      contactId,
      preferredTypes: [],
      preferredTimes: [],
      responseTime: { average: 0, fastest: 0, slowest: 0 },
      relationshipStrength: 0,
      engagementLevel: 0,
      communicationQuality: 0,
      successRate: 0,
      averageEffectiveness: 0,
      sentimentDistribution: {
        very_positive: 0,
        positive: 0,
        neutral: 0,
        negative: 0,
        very_negative: 0
      },
      commonTopics: [],
      topKeywords: [],
      insights: [],
      recommendations: [],
      lastAnalyzedAt: new Date()
    };
  }

  // 计算偏好的沟通方式
  const typeCount: Record<CommunicationType, number> = {} as any;
  contactComms.forEach(c => {
    typeCount[c.type] = (typeCount[c.type] || 0) + 1;
  });
  const preferredTypes = Object.entries(typeCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([type]) => type as CommunicationType);

  // 计算平均效果
  const effectiveComms = contactComms.filter(c => c.effectiveness !== undefined);
  const averageEffectiveness = effectiveComms.length > 0 
    ? effectiveComms.reduce((sum, c) => sum + (c.effectiveness || 0), 0) / effectiveComms.length
    : 0;

  // 计算成功率
  const outcomeCounts = contactComms.reduce((acc, c) => {
    if (c.outcome) {
      acc[c.outcome] = (acc[c.outcome] || 0) + 1;
    }
    return acc;
  }, {} as Record<CommunicationOutcome, number>);
  
  const successfulCount = (outcomeCounts.successful || 0) + (outcomeCounts.partial || 0);
  const successRate = contactComms.length > 0 ? (successfulCount / contactComms.length) * 100 : 0;

  // 计算情感分布
  const sentimentDistribution = contactComms.reduce((acc, c) => {
    if (c.sentiment) {
      acc[c.sentiment] = (acc[c.sentiment] || 0) + 1;
    }
    return acc;
  }, {
    very_positive: 0,
    positive: 0,
    neutral: 0,
    negative: 0,
    very_negative: 0
  });

  // 计算关系强度（基于沟通频率、质量、最近性）
  const now = new Date();
  const lastComm = contactComms.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())[0];
  const daysSinceLastComm = lastComm ? Math.floor((now.getTime() - lastComm.startTime.getTime()) / (1000 * 60 * 60 * 24)) : 365;
  
  const frequency = contactComms.length;
  const recency = Math.max(0, 100 - daysSinceLastComm);
  const quality = averageEffectiveness * 10;
  
  const relationshipStrength = Math.min(100, (frequency * 2 + recency + quality) / 4);

  // 生成洞察和建议
  const insights: string[] = [];
  const recommendations: string[] = [];

  if (successRate < 50) {
    insights.push('沟通成功率较低，可能需要调整沟通策略');
    recommendations.push('尝试不同的沟通方式或时间');
  }

  if (daysSinceLastComm > 30) {
    insights.push('最近沟通较少，关系可能疏远');
    recommendations.push('主动发起联系，保持关系活跃');
  }

  if (averageEffectiveness > 7) {
    insights.push('沟通效果良好，关系维护得当');
  }

  return {
    contactId,
    preferredTypes,
    preferredTimes: [], // TODO: 实现时间偏好分析
    responseTime: { average: 0, fastest: 0, slowest: 0 }, // TODO: 实现响应时间分析
    relationshipStrength,
    engagementLevel: Math.min(100, frequency * 10),
    communicationQuality: quality,
    successRate,
    averageEffectiveness,
    sentimentDistribution,
    commonTopics: [], // TODO: 实现话题分析
    topKeywords: [], // TODO: 实现关键词分析
    insights,
    recommendations,
    lastAnalyzedAt: new Date()
  };
};

export const useCommunicationStore = create<CommunicationStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      communications: [],
      templates: [],
      analyses: {},
      isLoading: false,
      error: null,
      selectedCommunication: null,
      filter: {},
      sortBy: 'date',
      sortOrder: 'desc',
      isAnalyzing: false,
      lastAnalysisUpdate: null,

      // 沟通记录CRUD操作
      addCommunication: async (communicationData) => {
        const communication: CommunicationRecord = {
          ...communicationData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          communications: [...state.communications, communication],
        }));

        // 更新相关联系人的分析
        const { analyzeContactCommunications } = get();
        await analyzeContactCommunications(communication.primaryContactId);
      },

      updateCommunication: async (id, updates) => {
        set((state) => ({
          communications: state.communications.map(comm =>
            comm.id === id
              ? { ...comm, ...updates, updatedAt: new Date() }
              : comm
          ),
        }));

        // 如果更新了主要联系人，重新分析
        const updatedComm = get().communications.find(c => c.id === id);
        if (updatedComm && updates.primaryContactId) {
          const { analyzeContactCommunications } = get();
          await analyzeContactCommunications(updates.primaryContactId);
        }
      },

      deleteCommunication: async (id) => {
        const communication = get().communications.find(c => c.id === id);
        
        set((state) => ({
          communications: state.communications.filter(comm => comm.id !== id),
          selectedCommunication: state.selectedCommunication?.id === id ? null : state.selectedCommunication,
        }));

        // 重新分析相关联系人
        if (communication) {
          const { analyzeContactCommunications } = get();
          await analyzeContactCommunications(communication.primaryContactId);
        }
      },

      // 批量操作
      addMultipleCommunications: async (communicationsData) => {
        const communications = communicationsData.map(data => ({
          ...data,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        }));

        set((state) => ({
          communications: [...state.communications, ...communications],
        }));

        // 批量更新分析
        const contactIds = [...new Set(communications.map(c => c.primaryContactId))];
        const { analyzeContactCommunications } = get();
        for (const contactId of contactIds) {
          await analyzeContactCommunications(contactId);
        }
      },

      deleteMultipleCommunications: async (ids) => {
        const communications = get().communications.filter(c => ids.includes(c.id));
        const contactIds = [...new Set(communications.map(c => c.primaryContactId))];

        set((state) => ({
          communications: state.communications.filter(comm => !ids.includes(comm.id)),
        }));

        // 重新分析相关联系人
        const { analyzeContactCommunications } = get();
        for (const contactId of contactIds) {
          await analyzeContactCommunications(contactId);
        }
      },

      archiveCommunications: async (ids) => {
        set((state) => ({
          communications: state.communications.map(comm =>
            ids.includes(comm.id)
              ? { ...comm, isArchived: true, updatedAt: new Date() }
              : comm
          ),
        }));
      },

      // 模板管理
      addTemplate: (templateData) => {
        const template: CommunicationTemplate = {
          ...templateData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          templates: [...state.templates, template],
        }));
      },

      updateTemplate: (id, updates) => {
        set((state) => ({
          templates: state.templates.map(template =>
            template.id === id
              ? { ...template, ...updates, updatedAt: new Date() }
              : template
          ),
        }));
      },

      deleteTemplate: (id) => {
        set((state) => ({
          templates: state.templates.filter(template => template.id !== id),
        }));
      },

      // 过滤和搜索
      setFilter: (newFilter) => {
        set((state) => ({
          filter: { ...state.filter, ...newFilter },
        }));
      },

      clearFilter: () => {
        set({ filter: {} });
      },

      setSortBy: (sortBy) => set({ sortBy }),
      setSortOrder: (sortOrder) => set({ sortOrder }),

      // 分析功能
      analyzeContactCommunications: async (contactId) => {
        set({ isAnalyzing: true });
        
        try {
          const { communications } = get();
          const analysis = analyzeCommunications(communications, contactId);
          
          set((state) => ({
            analyses: { ...state.analyses, [contactId]: analysis },
            isAnalyzing: false,
            lastAnalysisUpdate: new Date(),
          }));

          return analysis;
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '分析失败',
            isAnalyzing: false 
          });
          throw error;
        }
      },

      updateAnalysis: (contactId, analysis) => {
        set((state) => ({
          analyses: { ...state.analyses, [contactId]: analysis },
        }));
      },

      refreshAllAnalyses: async () => {
        set({ isAnalyzing: true });
        
        try {
          const { communications } = get();
          const contactIds = [...new Set(communications.map(c => c.primaryContactId))];
          const analyses: Record<string, CommunicationAnalysis> = {};

          for (const contactId of contactIds) {
            analyses[contactId] = analyzeCommunications(communications, contactId);
          }

          set({
            analyses,
            isAnalyzing: false,
            lastAnalysisUpdate: new Date(),
          });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '批量分析失败',
            isAnalyzing: false 
          });
        }
      },

      // 统计功能
      getContactStats: (contactId) => {
        const { communications } = get();
        const contactComms = communications.filter(c => c.primaryContactId === contactId);
        
        const now = new Date();
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const oneQuarterAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

        const stats: CommunicationStats = {
          totalCommunications: contactComms.length,
          totalDuration: contactComms.reduce((sum, c) => sum + (c.duration || 0), 0),
          averageFrequency: 0, // TODO: 计算平均频率
          
          byType: contactComms.reduce((acc, c) => {
            acc[c.type] = (acc[c.type] || 0) + 1;
            return acc;
          }, {} as Record<CommunicationType, number>),
          
          byDirection: contactComms.reduce((acc, c) => {
            acc[c.direction] = (acc[c.direction] || 0) + 1;
            return acc;
          }, {} as Record<CommunicationDirection, number>),
          
          bySentiment: contactComms.reduce((acc, c) => {
            if (c.sentiment) {
              acc[c.sentiment] = (acc[c.sentiment] || 0) + 1;
            }
            return acc;
          }, {} as Record<CommunicationSentiment, number>),
          
          byOutcome: contactComms.reduce((acc, c) => {
            if (c.outcome) {
              acc[c.outcome] = (acc[c.outcome] || 0) + 1;
            }
            return acc;
          }, {} as Record<CommunicationOutcome, number>),
          
          thisWeek: contactComms.filter(c => c.startTime >= oneWeekAgo).length,
          thisMonth: contactComms.filter(c => c.startTime >= oneMonthAgo).length,
          thisQuarter: contactComms.filter(c => c.startTime >= oneQuarterAgo).length,
          thisYear: contactComms.filter(c => c.startTime >= oneYearAgo).length,
          
          frequencyTrend: 'stable', // TODO: 计算趋势
          sentimentTrend: 'stable',
          effectivenessTrend: 'stable',
          
          lastCommunicationDate: contactComms.length > 0 
            ? contactComms.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())[0].startTime
            : undefined,
          daysSinceLastCommunication: contactComms.length > 0 
            ? Math.floor((now.getTime() - contactComms.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())[0].startTime.getTime()) / (1000 * 60 * 60 * 24))
            : undefined,
          mostFrequentType: Object.entries(contactComms.reduce((acc, c) => {
            acc[c.type] = (acc[c.type] || 0) + 1;
            return acc;
          }, {} as Record<CommunicationType, number>))
            .sort(([,a], [,b]) => b - a)[0]?.[0] as CommunicationType,
          averageSentiment: 0, // TODO: 计算平均情感
          averageEffectiveness: contactComms.filter(c => c.effectiveness).length > 0
            ? contactComms.filter(c => c.effectiveness).reduce((sum, c) => sum + (c.effectiveness || 0), 0) / contactComms.filter(c => c.effectiveness).length
            : 0,
        };

        return stats;
      },

      getOverallStats: () => {
        const { communications } = get();
        return get().getContactStats(''); // 使用空字符串获取全部统计
      },

      getCommunicationTrends: (contactId) => {
        // TODO: 实现趋势分析
        return {};
      },

      // 快速操作
      recordQuickCall: async (contactId, duration, sentiment = 'neutral') => {
        await get().addCommunication({
          type: 'call',
          direction: 'outgoing',
          primaryContactId: contactId,
          participants: [{ contactId, name: '', participationLevel: 'primary' }],
          startTime: new Date(),
          duration,
          sentiment,
          outcome: 'successful',
        });
      },

      recordQuickMessage: async (contactId, content, sentiment = 'neutral') => {
        await get().addCommunication({
          type: 'message',
          direction: 'outgoing',
          primaryContactId: contactId,
          participants: [{ contactId, name: '', participationLevel: 'primary' }],
          startTime: new Date(),
          content,
          sentiment,
          outcome: 'successful',
        });
      },

      recordQuickMeeting: async (contactId, duration, topics) => {
        await get().addCommunication({
          type: 'meeting',
          direction: 'mutual',
          primaryContactId: contactId,
          participants: [{ contactId, name: '', participationLevel: 'primary' }],
          startTime: new Date(),
          duration,
          topics,
          sentiment: 'positive',
          outcome: 'successful',
        });
      },

      // 选择和UI
      setSelectedCommunication: (communication) => set({ selectedCommunication: communication }),
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),

      // 数据管理
      importCommunications: async (communications) => {
        set((state) => ({
          communications: [...state.communications, ...communications],
        }));

        // 重新分析所有相关联系人
        const contactIds = [...new Set(communications.map(c => c.primaryContactId))];
        const { analyzeContactCommunications } = get();
        for (const contactId of contactIds) {
          await analyzeContactCommunications(contactId);
        }
      },

      exportCommunications: async (contactId) => {
        const { communications } = get();
        return contactId 
          ? communications.filter(c => c.primaryContactId === contactId)
          : communications;
      },

      clearAllCommunications: () => {
        set({
          communications: [],
          analyses: {},
          selectedCommunication: null,
          error: null,
        });
      },
    }),
    {
      name: 'communication-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        communications: state.communications,
        templates: state.templates,
        analyses: state.analyses,
      }),
    }
  )
);

// 导出选择器
export const useCommunicationSelectors = {
  useFilteredCommunications: () => {
    const { communications, filter, sortBy, sortOrder } = useCommunicationStore();
    const filtered = filterCommunications(communications, filter);
    return sortCommunications(filtered, sortBy, sortOrder);
  },
  
  useContactCommunications: (contactId: string) => {
    const communications = useCommunicationStore(state => state.communications);
    return communications.filter(c => c.primaryContactId === contactId);
  },
  
  useContactAnalysis: (contactId: string) => {
    const analyses = useCommunicationStore(state => state.analyses);
    return analyses[contactId];
  },
  
  useContactStats: (contactId: string) => {
    const getContactStats = useCommunicationStore(state => state.getContactStats);
    return getContactStats(contactId);
  },
};
