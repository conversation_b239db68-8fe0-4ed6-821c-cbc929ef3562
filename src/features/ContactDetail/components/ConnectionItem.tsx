import React from 'react';
import {
  Pressable,
  HStack,
  VStack,
  Avatar,
  AvatarFallbackText,
  AvatarImage,
  Text,
  Badge,
  BadgeText,
  Icon,
  Box
} from '@gluestack-ui/themed';
import { ChevronRight } from 'lucide-react-native';
import { Connection } from '../hooks/useContactDetail';

interface ConnectionItemProps {
  connection: Connection;
  onPress: () => void;
}

const ConnectionItem: React.FC<ConnectionItemProps> = ({ connection, onPress }) => {
  // Function to get initials from name for Avatar Fallback
  const getInitials = (name: string) => {
    const names = name.split(' ');
    let initials = names[0].substring(0, 1).toUpperCase();
    if (names.length > 1) {
      initials += names[names.length - 1].substring(0, 1).toUpperCase();
    }
    return initials;
  };

  return (
    <Pressable 
      onPress={onPress} 
      flexDirection="row" 
      alignItems="center" 
      p={'$3'} 
      mb={'$3'} 
      borderBottomWidth={1} 
      borderColor={'$backgroundLight200'}
    >
      <Avatar size="md" mr={'$3'}>
        <AvatarFallbackText>{getInitials(connection.name)}</AvatarFallbackText>
        {connection.avatar && <AvatarImage source={{ uri: connection.avatar }} alt={`${connection.name} avatar`} />}
      </Avatar>
      <VStack flex={1}>
        <Text fontWeight={'$semibold'} fontSize={'$md'}>{connection.name}</Text>
        <Text fontSize={'$sm'} color={'$textLight500'}>{connection.position}</Text>
        {connection.relationship && (
          <Badge size="sm" action="muted" variant="solid" mt={'$1'} alignSelf='flex-start'>
            <BadgeText>{connection.relationship}</BadgeText>
          </Badge>
        )}
      </VStack>
      <Icon as={ChevronRight} size="md" color="$textLight400" />
    </Pressable>
  );
};

export default ConnectionItem;
