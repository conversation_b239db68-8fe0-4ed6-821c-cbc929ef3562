# SettingsScreen Refactor Tasks

- **重构状态:** ToDo
- **主要区域/视图:**
    - **1. 顶部导航区 (Header)**
        - **描述:** 包含屏幕标题 \"Settings\"。
        - **现有主要组件:** `View`, `Text`. (Standard header, may not have back if it's a root tab screen).
        - **Gluestack UI 迁移建议:**
            - `View` (header container) -> `Box` or `HStack` with `alignItems=\"center\"`, `p=\"$4\"`.
            - `Text` (screen title) -> `Heading` (`size=\"lg\"`).
        - **设计规范参考:** 页面顶部标题与内容间距.

    - **2. 用户信息区 (User Profile Section)**
        - **描述:** 显示用户头像、名称、邮箱。可能包含编辑入口。
        - **现有主要组件:** `View`, `Image` (avatar), `Text` (name, email), `TouchableOpacity` (edit icon).
        - **Gluestack UI 迁移建议:**
            - `View` (section container) -> `HStack` or `Box` with `alignItems=\"center\"`, `p=\"$4\"`, `mb=\"$4\"`.
            - `Image` (avatar) -> `Avatar` (`size=\"md\"` or `lg\"`).
            - `View` (text container) -> `VStack` (`ml=\"$3\"`).
            - `Text` (name) -> `Heading` (`size=\"md\"`).
            - `Text` (email) -> `Text` (`size=\"sm\"`).
            - `TouchableOpacity` (edit icon) -> `Button` (`variant=\"link\"`, iconOnly with `lucide-react-native` `Edit3` icon).
        - **设计规范参考:** 头像尺寸, 文本层级, 间距.

    - **3. 设置项列表区 (Settings List)**
        - **描述:** 包含多个设置项，如通知开关、偏好设置、账户操作等。每个设置项通常包含一个标签、一个控件（如开关、箭头）和可选的描述。
        - **现有主要组件:** `ScrollView`, `View` (setting item row), `Text` (label, description), `Switch`, `Ionicons` (chevron, specific action icons).
        - **Gluestack UI 迁移建议:**
            - `ScrollView` -> `ScrollView`.
            - **设置项分组 (Settings Group):**
                - `View` (group container) -> `VStack` or `Box` with `mb=\"$4\"`.
                - `Text` (group title) -> `Heading` (`size=\"sm\"`, `mb=\"$2\"`, `color=\"$textLight600\"`).
            - **单个设置项 (Setting Item):**
                - `View` (item row) -> `Pressable` (if clickable) or `HStack`, with `justifyContent=\"space-between\"`, `alignItems=\"center\"`, `p=\"$3\"`, `borderBottomWidth=\"$1\"`, `borderColor=\"$borderLight300\"`.
                - `View` (label & description container) -> `VStack`.
                - `Text` (label) -> `Text` (`fontWeight=\"$medium\"`).
                - `Text` (description - optional) -> `Text` (`size=\"xs\"`, `color=\"$textLight500\"`).
                - `Switch` (core RN) -> `Switch` from `gluestack-ui`.
                - `Ionicons` (chevron for navigation) -> `Icon` with `lucide-react-native` `ChevronRight` icon.
                - `Ionicons` (action specific icons) -> `Icon` with corresponding `lucide-react-native` icon.
            - **具体设置项示例:**
                - **Notification Toggle:** `Text` (label) + `Switch`.
                - **Appearance/Theme:** `Text` (label) + `Icon` (ChevronRight) -> navigates to a new screen or opens `Actionsheet`.
                - **Language:** `Text` (label) + `Text` (current language) + `Icon` (ChevronRight).
                - **Export Data:** `Text` (label) + `Icon` (ChevronRight or `DownloadCloud`).
                - **Logout Button:** `Button` (`variant=\"outline\"`, `action=\"negative\"` or custom styling for destructive action, full width or within a list item).
                - **Delete Account Button:** `Button` (`variant=\"solid\"`, `action=\"negative\"` or custom styling, full width or within a list item).
        - **设计规范参考:** 列表项样式, 开关样式, 图标, 文本, 间距, 分割线.

    - **4. 应用信息区 (App Info Section)**
        - **描述:** 显示应用版本号、隐私政策、服务条款等链接。
        - **现有主要组件:** `View`, `Text`.
        - **Gluestack UI 迁移建议:**
            - `View` (section container) -> `VStack` or `Box` with `p=\"$4\"`, `mt=\"$4\"`, `alignItems=\"center\"`.
            - `Text` (app version) -> `Text` (`size=\"sm\"`, `color=\"$textLight500\"`).
            - Links (`Privacy Policy`, `Terms of Service`): `Link` component or `Button` (`variant=\"link\"`).
        - **设计规范参考:** 文本样式, 链接样式.

    - **5. 模态框/对话框 (Modals/Dialogs - e.g., Logout Confirmation, Delete Account Confirmation)**
        - **描述:** 用于确认危险操作。
        - **现有主要组件:** `Alert` (native), custom Modals.
        - **Gluestack UI 迁移建议:**
            - `Alert` (native) -> `AlertDialog` component from `gluestack-ui`.
                - `AlertDialog.Backdrop`, `AlertDialog.Content`, `AlertDialog.Header` (with `Heading`), `AlertDialog.Body` (with `Text`), `AlertDialog.Footer` (with `ButtonGroup` containing `Button`s for \"Cancel\" and \"Confirm\").
            - Custom Modals -> `Modal` component if more complex content is needed.
        - **设计规范参考:** 警告对话框样式, 按钮.

- **整体注意事项:**
    - **图标替换:** All `Ionicons`, `MaterialIcons`, `FontAwesome` to `lucide-react-native`.
    - **颜色和样式:** Migrate `StyleSheet` to `gluestack-ui` props/`sx`, using theme tokens.
    - **导航:** Settings items might navigate to other screens or trigger actions. Navigation logic (`navigation.navigate`) remains.
    - **状态管理:** State for switches, user preferences, etc., will be preserved.
    - **Accessibility:** Ensure proper accessibility props for interactive elements like switches and buttons.
