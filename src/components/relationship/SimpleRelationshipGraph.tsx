/**
 * 简化版关系图谱组件
 * 使用React Native基础组件实现，不依赖SVG
 */

import React, { useState, useMemo } from 'react';
import { Dimensions, ScrollView } from 'react-native';
import {
  Box,
  Text,
  VStack,
  HStack,
  Heading,
  Pressable,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  Badge,
  BadgeText,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
} from '@gluestack-ui/themed';
import {
  X,
  Users,
  ArrowRight,
  Eye,
  Target,
  Plus,
} from 'lucide-react-native';

import { StandardButton, StandardIcon, CircularButton } from '../ui';
import {
  NetworkGraph,
  NetworkGraphNode,
  NetworkGraphEdge,
  Contact,
  ContactRelationship,
  ContactRelationshipType,
  BusinessEvent
} from '../../types';
import { relationshipGraphService, PathAnalysis } from '../../services/relationshipGraphService';

interface SimpleRelationshipGraphProps {
  centerContactId: string;
  contacts: Contact[];
  relationships: ContactRelationship[];
  events?: BusinessEvent[];
  maxDepth?: number;
  onContactPress?: (contactId: string) => void;
  onAddRelationship?: (fromId: string, toId: string) => void;
  showEventConnections?: boolean;
}

export const SimpleRelationshipGraph: React.FC<SimpleRelationshipGraphProps> = ({
  centerContactId,
  contacts,
  relationships,
  events = [],
  maxDepth = 2, // 简化版只显示2层
  onContactPress,
  onAddRelationship,
  showEventConnections = true,
}) => {
  const toast = useToast();
  const { width } = Dimensions.get('window');
  
  // 状态管理
  const [selectedNode, setSelectedNode] = useState<NetworkGraphNode | null>(null);
  const [showNodeModal, setShowNodeModal] = useState(false);
  const [showPathModal, setShowPathModal] = useState(false);
  const [pathAnalysis, setPathAnalysis] = useState<PathAnalysis | null>(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const [sharedEvents, setSharedEvents] = useState<BusinessEvent[]>([]);

  // 构建网络图谱
  const networkGraph = useMemo(() => {
    try {
      return relationshipGraphService.buildNetworkGraph(
        centerContactId,
        contacts,
        relationships,
        maxDepth
      );
    } catch (error) {
      console.error('构建网络图谱失败:', error);
      return { nodes: [], edges: [], centerNodeId: centerContactId, maxDepth };
    }
  }, [centerContactId, contacts, relationships, maxDepth]);

  // 按层级分组节点
  const nodesByLevel = useMemo(() => {
    const groups: Record<number, NetworkGraphNode[]> = {};
    networkGraph.nodes.forEach(node => {
      const level = node.level || 0;
      if (!groups[level]) groups[level] = [];
      groups[level].push(node);
    });
    return groups;
  }, [networkGraph.nodes]);

  // 处理节点点击
  const handleNodePress = (node: NetworkGraphNode) => {
    setSelectedNode(node);
    setShowNodeModal(true);
  };

  // 处理路径分析
  const handlePathAnalysis = (targetNodeId: string) => {
    if (targetNodeId === centerContactId) return;

    const analysis = relationshipGraphService.analyzeRelationshipPath(
      centerContactId,
      targetNodeId,
      contacts,
      relationships
    );

    setPathAnalysis(analysis);
    setShowPathModal(true);
  };

  // 分析共同参与的活动
  const analyzeSharedEvents = (contactId1: string, contactId2: string): BusinessEvent[] => {
    return events.filter(event =>
      event.participants.some(p => p.contactId === contactId1) &&
      event.participants.some(p => p.contactId === contactId2)
    );
  };

  // 处理活动连接分析
  const handleEventAnalysis = (targetNodeId: string) => {
    const shared = analyzeSharedEvents(centerContactId, targetNodeId);
    setSharedEvents(shared);
    setShowEventModal(true);
  };

  // 获取节点的活动连接数量
  const getEventConnectionCount = (nodeId: string): number => {
    return analyzeSharedEvents(centerContactId, nodeId).length;
  };

  // 获取关系类型颜色
  const getRelationshipColor = (type: ContactRelationshipType) => {
    const colors: Record<ContactRelationshipType, string> = {
      introduced_by: '$success500',
      introduced_to: '$success600',
      mutual_friend: '$primary500',
      colleague: '$blue500',
      family_friend: '$purple500',
      business_partner: '$orange500',
      mentor_mentee: '$indigo500',
      client_vendor: '$teal500',
      other: '$gray500',
    };
    return colors[type] || '$gray500';
  };

  // 获取关系类型标签
  const getRelationshipLabel = (type: ContactRelationshipType) => {
    const labels: Record<ContactRelationshipType, string> = {
      introduced_by: '介绍',
      introduced_to: '被介绍',
      mutual_friend: '共同朋友',
      colleague: '同事',
      family_friend: '家庭朋友',
      business_partner: '商业伙伴',
      mentor_mentee: '师生',
      client_vendor: '客户关系',
      other: '其他',
    };
    return labels[type] || type;
  };

  // 获取节点颜色
  const getNodeColor = (node: NetworkGraphNode) => {
    if (node.id === centerContactId) return '$primary600';
    
    switch (node.level) {
      case 1: return '$blue500';
      case 2: return '$purple500';
      default: return '$gray500';
    }
  };

  // 获取姓名首字母
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // 渲染节点
  const renderNode = (node: NetworkGraphNode, index: number) => {
    const nodeSize = node.id === centerContactId ? 70 : 60;
    const nodeColor = getNodeColor(node);

    return (
      <Pressable
        key={node.id}
        onPress={() => handleNodePress(node)}
        onLongPress={() => handlePathAnalysis(node.id)}
        m="$2"
        alignItems="center"
      >
        <VStack space="sm" alignItems="center">
          <Box
            width={nodeSize}
            height={nodeSize}
            borderRadius={nodeSize / 2}
            bg={nodeColor}
            borderWidth={2}
            borderColor="white"
            justifyContent="center"
            alignItems="center"
            sx={{
              shadowColor: '$neutral900',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
              elevation: 4,
            }}
          >
            {node.avatar ? (
              <Avatar size={node.id === centerContactId ? 'xl' : 'lg'} bg={nodeColor}>
                <AvatarImage source={{ uri: node.avatar }} alt={node.name} />
                <AvatarFallbackText>{getInitials(node.name)}</AvatarFallbackText>
              </Avatar>
            ) : (
              <Text
                fontWeight="$bold"
                color="white"
                size={node.id === centerContactId ? 'lg' : 'md'}
              >
                {getInitials(node.name)}
              </Text>
            )}
          </Box>

          {/* 节点标签 */}
          <VStack space="xs" alignItems="center" maxWidth={nodeSize + 20}>
            <Text
              size="xs"
              fontWeight="$medium"
              color="$textLight700"
              textAlign="center"
              numberOfLines={2}
              sx={{ _dark: { color: '$textDark300' } }}
            >
              {node.name}
            </Text>

            {/* 层级指示和活动连接 */}
            <HStack space="xs">
              {node.level > 0 && (
                <Badge size="xs" variant="outline">
                  <BadgeText>{node.level}度</BadgeText>
                </Badge>
              )}

              {/* 活动连接指示 */}
              {showEventConnections && node.id !== centerContactId && (
                (() => {
                  const eventCount = getEventConnectionCount(node.id);
                  return eventCount > 0 ? (
                    <Pressable onPress={() => handleEventAnalysis(node.id)}>
                      <Badge size="xs" variant="solid" action="success">
                        <BadgeText>{eventCount}活动</BadgeText>
                      </Badge>
                    </Pressable>
                  ) : null;
                })()
              )}
            </HStack>
          </VStack>
        </VStack>
      </Pressable>
    );
  };

  // 渲染连接关系信息
  const renderConnectionInfo = (fromLevel: number, toLevel: number) => {
    const connections = networkGraph.edges.filter(edge => {
      const fromNode = networkGraph.nodes.find(n => n.id === edge.fromId);
      const toNode = networkGraph.nodes.find(n => n.id === edge.toId);
      return fromNode?.level === fromLevel && toNode?.level === toLevel;
    });

    if (connections.length === 0) return null;

    return (
      <VStack space="xs" alignItems="center" my="$2">
        <StandardIcon as={ArrowRight} size="sm" color="$textLight500" />
        <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
          {connections.length} 个关系
        </Text>
      </VStack>
    );
  };

  return (
    <Box flex={1} bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark950' } }}>
      {/* 工具栏 */}
      <Box p="$4" bg="$backgroundLight0" sx={{ _dark: { bg: '$backgroundDark900' } }}>
        <VStack space="md">
          <HStack justifyContent="space-between" alignItems="center">
            <Heading size="sm">关系图谱</Heading>
            <CircularButton
              icon={Plus}
              size="sm"
              variant="outline"
              onPress={() => onAddRelationship?.(centerContactId, '')}
            />
          </HStack>

          {/* 统计信息 */}
          <HStack space="lg" justifyContent="space-around">
            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$primary600">
                {networkGraph.nodes.length}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                联系人
              </Text>
            </VStack>
            
            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$success600">
                {networkGraph.edges.length}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                关系
              </Text>
            </VStack>
            
            <VStack alignItems="center">
              <Text fontWeight="$bold" size="lg" color="$warning600">
                {Math.max(...networkGraph.nodes.map(n => n.level || 0))}
              </Text>
              <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                最大层级
              </Text>
            </VStack>
          </HStack>
        </VStack>
      </Box>

      {/* 图谱可视化区域 */}
      <ScrollView flex={1} showsVerticalScrollIndicator={false}>
        {networkGraph.nodes.length > 0 ? (
          <VStack space="lg" p="$4" alignItems="center">
            {/* 按层级渲染节点 */}
            {Object.keys(nodesByLevel)
              .map(Number)
              .sort((a, b) => a - b)
              .map(level => (
                <VStack key={level} space="md" alignItems="center">
                  {/* 层级标题 */}
                  {level > 0 && (
                    <VStack space="sm" alignItems="center">
                      <Badge size="sm" variant="solid" bg={level === 1 ? '$blue500' : '$purple500'}>
                        <BadgeText>{level}度联系人</BadgeText>
                      </Badge>
                      {renderConnectionInfo(level - 1, level)}
                    </VStack>
                  )}
                  
                  {/* 该层级的节点 */}
                  <HStack space="md" flexWrap="wrap" justifyContent="center">
                    {nodesByLevel[level].map((node, index) => renderNode(node, index))}
                  </HStack>
                </VStack>
              ))}
          </VStack>
        ) : (
          <Box flex={1} justifyContent="center" alignItems="center" p="$8">
            <VStack space="lg" alignItems="center">
              <StandardIcon as={Users} size="4xl" color="$textLight400" />
              <VStack space="sm" alignItems="center">
                <Text 
                  textAlign="center" 
                  fontWeight="$medium" 
                  color="$textLight500" 
                  sx={{ _dark: { color: '$textDark500' } }}
                >
                  暂无关系数据
                </Text>
                <Text 
                  textAlign="center" 
                  color="$textLight400" 
                  sx={{ _dark: { color: '$textDark600' } }}
                >
                  开始添加联系人之间的关系
                </Text>
              </VStack>
              <StandardButton
                variant="outline"
                leftIcon={Plus}
                onPress={() => onAddRelationship?.(centerContactId, '')}
              >
                添加关系
              </StandardButton>
            </VStack>
          </Box>
        )}
      </ScrollView>

      {/* 操作提示 */}
      <Box 
        position="absolute" 
        bottom="$4" 
        left="$4" 
        right="$4"
        bg="$backgroundLight0"
        p="$3"
        borderRadius="$lg"
        sx={{
          _dark: { bg: '$backgroundDark900' },
          shadowColor: '$neutral900',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 4,
        }}
      >
        <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }} textAlign="center">
          点击节点查看详情 • 长按分析关系路径
        </Text>
      </Box>

      {/* 节点详情模态框 */}
      {selectedNode && (
        <Modal isOpen={showNodeModal} onClose={() => setShowNodeModal(false)}>
          <ModalBackdrop />
          <ModalContent maxWidth="$96">
            <ModalHeader>
              <VStack space="md" alignItems="center">
                <Avatar size="xl" bg={getNodeColor(selectedNode)}>
                  <AvatarFallbackText>{getInitials(selectedNode.name)}</AvatarFallbackText>
                  {selectedNode.avatar && (
                    <AvatarImage source={{ uri: selectedNode.avatar }} alt={selectedNode.name} />
                  )}
                </Avatar>
                <VStack space="xs" alignItems="center">
                  <Heading size="lg">{selectedNode.name}</Heading>
                  {selectedNode.company && (
                    <Text color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                      {selectedNode.position ? `${selectedNode.position} at ${selectedNode.company}` : selectedNode.company}
                    </Text>
                  )}
                  <Badge size="sm" variant="outline">
                    <BadgeText>{selectedNode.level}度联系人</BadgeText>
                  </Badge>
                </VStack>
              </VStack>
              <ModalCloseButton>
                <StandardIcon as={X} />
              </ModalCloseButton>
            </ModalHeader>

            <ModalBody>
              <VStack space="md">
                {/* 连接信息 */}
                <VStack space="sm">
                  <Text fontWeight="$bold">连接信息</Text>
                  <HStack justifyContent="space-between">
                    <Text>连接强度:</Text>
                    <Text fontWeight="$medium">{selectedNode.connectionStrength || 0}%</Text>
                  </HStack>
                  <HStack justifyContent="space-between">
                    <Text>关系层级:</Text>
                    <Text fontWeight="$medium">{selectedNode.level}度</Text>
                  </HStack>
                </VStack>
              </VStack>
            </ModalBody>

            <ModalFooter>
              <HStack space="md" flex={1}>
                <StandardButton
                  variant="outline"
                  onPress={() => handlePathAnalysis(selectedNode.id)}
                  flex={1}
                  leftIcon={Target}
                >
                  分析路径
                </StandardButton>
                <StandardButton
                  variant="solid"
                  action="primary"
                  onPress={() => {
                    setShowNodeModal(false);
                    onContactPress?.(selectedNode.id);
                  }}
                  flex={1}
                  leftIcon={Eye}
                >
                  查看详情
                </StandardButton>
              </HStack>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {/* 路径分析模态框 */}
      {pathAnalysis && (
        <Modal isOpen={showPathModal} onClose={() => setShowPathModal(false)}>
          <ModalBackdrop />
          <ModalContent maxWidth="$96">
            <ModalHeader>
              <Heading size="lg">关系路径分析</Heading>
              <ModalCloseButton>
                <StandardIcon as={X} />
              </ModalCloseButton>
            </ModalHeader>

            <ModalBody>
              <VStack space="lg">
                {/* 最短路径 */}
                {pathAnalysis.shortestPath.length > 0 && (
                  <VStack space="sm">
                    <Text fontWeight="$bold">最短路径</Text>
                    <HStack space="xs" alignItems="center" flexWrap="wrap">
                      {pathAnalysis.shortestPath.map((contactId, index) => {
                        const contact = contacts.find(c => c.id === contactId);
                        return (
                          <React.Fragment key={contactId}>
                            <Text fontWeight="$medium">{contact?.name || contactId}</Text>
                            {index < pathAnalysis.shortestPath.length - 1 && (
                              <StandardIcon as={ArrowRight} size="xs" color="$textLight500" />
                            )}
                          </React.Fragment>
                        );
                      })}
                    </HStack>
                    <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                      {pathAnalysis.shortestPath.length - 1} 度分离
                    </Text>
                  </VStack>
                )}

                {/* 共同联系人 */}
                {pathAnalysis.commonConnections.length > 0 && (
                  <VStack space="sm">
                    <Text fontWeight="$bold">共同联系人</Text>
                    <VStack space="xs">
                      {pathAnalysis.commonConnections.slice(0, 5).map(contactId => {
                        const contact = contacts.find(c => c.id === contactId);
                        return (
                          <Text key={contactId} color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                            • {contact?.name || contactId}
                          </Text>
                        );
                      })}
                    </VStack>
                  </VStack>
                )}
              </VStack>
            </ModalBody>

            <ModalFooter>
              <StandardButton
                variant="solid"
                action="primary"
                onPress={() => setShowPathModal(false)}
                flex={1}
              >
                关闭
              </StandardButton>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {/* 共同活动模态框 */}
      <Modal isOpen={showEventModal} onClose={() => setShowEventModal(false)}>
        <ModalBackdrop />
        <ModalContent maxWidth="$96">
          <ModalHeader>
            <Heading size="lg">共同参与的活动</Heading>
            <ModalCloseButton>
              <StandardIcon as={X} />
            </ModalCloseButton>
          </ModalHeader>

          <ModalBody>
            <VStack space="md">
              {sharedEvents.length > 0 ? (
                sharedEvents.map((event) => (
                  <Box
                    key={event.id}
                    p="$3"
                    bg="$backgroundLight100"
                    borderRadius="$md"
                    sx={{ _dark: { bg: '$backgroundDark800' } }}
                  >
                    <VStack space="sm">
                      <HStack justifyContent="space-between" alignItems="flex-start">
                        <VStack flex={1} space="xs">
                          <Text fontWeight="$bold" size="sm">
                            {event.title}
                          </Text>
                          <Text size="xs" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                            {new Date(event.date).toLocaleDateString('zh-CN')} • {event.category}
                          </Text>
                        </VStack>
                        <Badge size="xs" variant="outline">
                          <BadgeText>{event.eventType}</BadgeText>
                        </Badge>
                      </HStack>

                      {event.description && (
                        <Text size="xs" color="$textLight700" sx={{ _dark: { color: '$textDark300' } }}>
                          {event.description}
                        </Text>
                      )}

                      <HStack space="sm" alignItems="center">
                        <StandardIcon as={Users} size="xs" color="$textLight500" />
                        <Text size="xs" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                          {event.participants.length} 位参与者
                        </Text>
                      </HStack>
                    </VStack>
                  </Box>
                ))
              ) : (
                <VStack space="md" alignItems="center" py="$8">
                  <StandardIcon as={Users} size="2xl" color="$textLight400" />
                  <Text textAlign="center" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                    暂无共同参与的活动
                  </Text>
                </VStack>
              )}
            </VStack>
          </ModalBody>

          <ModalFooter>
            <StandardButton
              variant="outline"
              onPress={() => setShowEventModal(false)}
              flex={1}
            >
              关闭
            </StandardButton>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default SimpleRelationshipGraph;
