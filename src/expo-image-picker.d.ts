// src/expo-image-picker.d.ts
declare module 'expo-image-picker' {
  export * from 'expo-image-picker/src/ImagePicker';
  // You might need to export specific types if the above doesn't cover everything
  // For example:
  // export interface ImagePickerResult { cancelled: boolean; uri?: string; width?: number; height?: number; type?: 'image' | 'video'; base64?: string; exif?: any; assets?: { uri: string }[] | null; }
  // export enum MediaTypeOptions { All = 'All', Videos = 'Videos', Images = 'Images' }
  // export function launchImageLibraryAsync(options?: ImageLibraryOptions): Promise<ImagePickerResult>;
  // export interface ImageLibraryOptions { mediaTypes?: MediaTypeOptions; allowsEditing?: boolean; aspect?: [number, number]; quality?: number; base64?: boolean; exif?: boolean; }
}
