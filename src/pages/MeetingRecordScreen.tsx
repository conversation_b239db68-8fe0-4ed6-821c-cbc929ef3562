import React, { useState, useEffect } from 'react';
import { Platform } from 'react-native';
import {
  Box,
  Text,
  Heading,
  VStack,
  HStack,
  Button,
  ButtonText,
  ButtonIcon,
  Pressable,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  Input,
  InputField,
  InputSlot,
  InputIcon,
  Textarea,
  TextareaInput,
  Switch,
  ScrollView,
  FlatList,
  KeyboardAvoidingView,
  useToast,
  Toast,
  ToastTitle,
  ToastDescription,
  Badge,
  BadgeText,
  Divider,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Checkbox,
  CheckboxIndicator,
  CheckboxIcon,
  CheckboxLabel,
  CheckIcon,
} from '@gluestack-ui/themed';
import { useNavigation } from '@react-navigation/native';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Plus,
  Minus,
  ArrowLeft,
  Search,
  Edit,
  Trash2,
  CheckCircle,
  X,
  Video,
  FileText,
} from 'lucide-react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';

// Import global state and hooks
import { useContactsStore, useMeetingsStore, useMeetingsSelectors } from '../store';
import { usePerformanceMonitor, useNetworkError } from '../hooks';
import { ErrorBoundary, StandardButton, StandardIcon, CircularButton } from '../components/ui';
import { Meeting, MeetingTask, Contact } from '../types';

type MeetingRecordScreenNavigationProp = any; // TODO: 添加正确的导航类型

interface MeetingFormData {
  title: string;
  description: string;
  date: Date;
  startTime: Date;
  endTime: Date | null;
  location: string;
  isVirtual: boolean;
  meetingLink: string;
  notes: string;
  participantIds: string[];
  tasks: Omit<MeetingTask, 'id' | 'createdAt' | 'updatedAt'>[];
}

/**
 * 会议记录屏幕 - 完全重构版本
 * 使用Gluestack UI和全局状态管理
 */
const MeetingRecordScreen: React.FC = () => {
  const navigation = useNavigation<MeetingRecordScreenNavigationProp>();
  const toast = useToast();

  // Performance monitoring
  usePerformanceMonitor('MeetingRecordScreen');

  // Global state
  const { contacts } = useContactsStore();
  const {
    addMeeting,
    updateMeeting,
    deleteMeeting,
    addMeetingTask,
    toggleTaskCompletion,
    setLoading
  } = useMeetingsStore();
  const upcomingMeetings = useMeetingsSelectors.useUpcomingMeetings();
  const pastMeetings = useMeetingsSelectors.usePastMeetings();
  const { showError, clearError } = useNetworkError();

  // Local state
  const [activeTab, setActiveTab] = useState<'new' | 'history'>('new');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [timePickerMode, setTimePickerMode] = useState<'start' | 'end'>('start');
  const [showParticipantModal, setShowParticipantModal] = useState(false);
  const [participantSearchQuery, setParticipantSearchQuery] = useState('');

  // Form data state
  const [formData, setFormData] = useState<MeetingFormData>({
    title: '',
    description: '',
    date: new Date(),
    startTime: new Date(),
    endTime: null,
    location: '',
    isVirtual: false,
    meetingLink: '',
    notes: '',
    participantIds: [],
    tasks: [],
  });

  // Form validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  const resetForm = () => {
    const now = new Date();
    setFormData({
      title: '',
      description: '',
      date: now,
      startTime: now,
      endTime: null,
      location: '',
      isVirtual: false,
      meetingLink: '',
      notes: '',
      participantIds: [],
      tasks: [],
    });
    setErrors({});
    setParticipantSearchQuery('');
  };
  
  // Form update helpers
  const updateFormData = (updates: Partial<MeetingFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    // Clear related errors when user starts typing
    Object.keys(updates).forEach(key => {
      if (errors[key]) {
        setErrors(prev => ({ ...prev, [key]: undefined }));
      }
    });
  };

  // Date and time handlers
  const handleDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      updateFormData({ date: selectedDate });
    }
  };

  const handleTimeChange = (event: DateTimePickerEvent, selectedTime?: Date) => {
    setShowTimePicker(false);
    if (selectedTime) {
      if (timePickerMode === 'start') {
        updateFormData({ startTime: selectedTime });
      } else {
        updateFormData({ endTime: selectedTime });
      }
    }
  };

  // Participant management
  const toggleParticipant = (contactId: string) => {
    const currentParticipants = formData.participantIds;
    if (currentParticipants.includes(contactId)) {
      updateFormData({
        participantIds: currentParticipants.filter(id => id !== contactId)
      });
    } else {
      updateFormData({
        participantIds: [...currentParticipants, contactId]
      });
    }
  };

  // Task management
  const addTask = () => {
    updateFormData({
      tasks: [...formData.tasks, { title: '', description: '', isCompleted: false }]
    });
  };

  const updateTask = (index: number, updates: Partial<MeetingTask>) => {
    const newTasks = [...formData.tasks];
    newTasks[index] = { ...newTasks[index], ...updates };
    updateFormData({ tasks: newTasks });
  };

  const removeTask = (index: number) => {
    if (formData.tasks.length > 0) {
      const newTasks = formData.tasks.filter((_, i) => i !== index);
      updateFormData({ tasks: newTasks });
    }
  };
  
  // Form validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = '会议标题不能为空';
    }

    if (formData.participantIds.length === 0) {
      newErrors.participants = '请至少选择一位参与者';
    }

    if (formData.isVirtual && !formData.meetingLink.trim()) {
      newErrors.meetingLink = '虚拟会议需要提供会议链接';
    }

    if (!formData.isVirtual && !formData.location.trim()) {
      newErrors.location = '线下会议需要提供会议地点';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Save meeting
  const handleSaveMeeting = async () => {
    if (!validateForm()) {
      toast.show({
        placement: 'top',
        render: ({ id }) => (
          <Toast nativeID={id} action="error" variant="accent">
            <ToastTitle>表单验证失败</ToastTitle>
            <ToastDescription>请检查并修正表单中的错误</ToastDescription>
          </Toast>
        ),
      });
      return;
    }

    setLoading(true);
    clearError();

    try {
      const meetingData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        date: formData.date,
        startTime: formData.startTime,
        endTime: formData.endTime,
        location: formData.isVirtual ? formData.meetingLink : formData.location,
        isVirtual: formData.isVirtual,
        meetingLink: formData.isVirtual ? formData.meetingLink : undefined,
        participantIds: formData.participantIds,
        organizerId: 'current-user', // TODO: 从用户状态获取
        notes: formData.notes.trim(),
        tasks: formData.tasks.filter(task => task.title.trim()),
        status: 'scheduled' as const,
      };

      addMeeting(meetingData);

      toast.show({
        placement: 'top',
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>会议创建成功</ToastTitle>
            <ToastDescription>会议已成功保存到日程中</ToastDescription>
          </Toast>
        ),
      });

      resetForm();
      setActiveTab('history');
    } catch (error) {
      console.error('Error saving meeting:', error);
      showError({
        message: '保存会议时出错，请重试',
        code: 'SAVE_ERROR'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    resetForm();
    navigation.goBack();
  };

  // Filtered contacts for participant selection
  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(participantSearchQuery.toLowerCase()) ||
    (contact.company && contact.company.toLowerCase().includes(participantSearchQuery.toLowerCase()))
  );

  // Get selected participants details
  const selectedParticipants = contacts.filter(contact =>
    formData.participantIds.includes(contact.id)
  );

  // Utility functions
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDateTime = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Render new meeting form
  const renderNewMeetingTab = () => (
    <KeyboardAvoidingView
      flex={1}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView flex={1} p="$4">
        <VStack space="lg">
          {/* Meeting Title */}
          <VStack space="sm">
            <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              会议标题 *
            </Text>
            <Input
              variant={errors.title ? 'outline' : 'outline'}
              size="md"
              isInvalid={!!errors.title}
            >
              <InputField
                placeholder="输入会议标题"
                value={formData.title}
                onChangeText={(text) => updateFormData({ title: text })}
              />
            </Input>
            {errors.title && (
              <Text size="sm" color="$error600">{errors.title}</Text>
            )}
          </VStack>

          {/* Meeting Description */}
          <VStack space="sm">
            <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              会议描述
            </Text>
            <Textarea>
              <TextareaInput
                placeholder="输入会议描述（可选）"
                value={formData.description}
                onChangeText={(text) => updateFormData({ description: text })}
                numberOfLines={3}
              />
            </Textarea>
          </VStack>

          {/* Date Selection */}
          <VStack space="sm">
            <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              会议日期 *
            </Text>
            <Pressable onPress={() => setShowDatePicker(true)}>
              <Box
                p="$3"
                borderWidth="$1"
                borderColor="$borderLight200"
                borderRadius="$md"
                bg="$backgroundLight0"
                sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
              >
                <HStack justifyContent="space-between" alignItems="center">
                  <Text color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                    {formatDate(formData.date)}
                  </Text>
                  <StandardIcon as={Calendar} size="md" color="$primary500" />
                </HStack>
              </Box>
            </Pressable>
            {showDatePicker && (
              <DateTimePicker
                value={formData.date}
                mode="date"
                display="default"
                onChange={handleDateChange}
              />
            )}
          </VStack>

          {/* Time Selection */}
          <HStack space="md">
            <VStack space="sm" flex={1}>
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                开始时间 *
              </Text>
              <Pressable onPress={() => {
                setTimePickerMode('start');
                setShowTimePicker(true);
              }}>
                <Box
                  p="$3"
                  borderWidth="$1"
                  borderColor="$borderLight200"
                  borderRadius="$md"
                  bg="$backgroundLight0"
                  sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
                >
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                      {formatTime(formData.startTime)}
                    </Text>
                    <StandardIcon as={Clock} size="sm" color="$primary500" />
                  </HStack>
                </Box>
              </Pressable>
            </VStack>

            <VStack space="sm" flex={1}>
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                结束时间
              </Text>
              <Pressable onPress={() => {
                setTimePickerMode('end');
                setShowTimePicker(true);
              }}>
                <Box
                  p="$3"
                  borderWidth="$1"
                  borderColor="$borderLight200"
                  borderRadius="$md"
                  bg="$backgroundLight0"
                  sx={{ _dark: { bg: '$backgroundDark900', borderColor: '$borderDark700' } }}
                >
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                      {formData.endTime ? formatTime(formData.endTime) : '选择时间'}
                    </Text>
                    <StandardIcon as={Clock} size="sm" color="$primary500" />
                  </HStack>
                </Box>
              </Pressable>
            </VStack>
          </HStack>

          {showTimePicker && (
            <DateTimePicker
              value={timePickerMode === 'start' ? formData.startTime : (formData.endTime || new Date())}
              mode="time"
              display="default"
              onChange={handleTimeChange}
            />
          )}

          {/* Virtual Meeting Toggle */}
          <VStack space="sm">
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                虚拟会议
              </Text>
              <HStack space="sm" alignItems="center">
                <StandardIcon
                  as={formData.isVirtual ? Video : MapPin}
                  size="sm"
                  color={formData.isVirtual ? '$primary500' : '$textLight600'}
                />
                <Switch
                  value={formData.isVirtual}
                  onValueChange={(value) => updateFormData({ isVirtual: value })}
                  trackColor={{ false: '$neutral300', true: '$primary200' }}
                  thumbColor={formData.isVirtual ? '$primary500' : '$neutral500'}
                />
              </HStack>
            </HStack>
          </VStack>

          {/* Location/Meeting Link */}
          <VStack space="sm">
            <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              {formData.isVirtual ? '会议链接 *' : '会议地点 *'}
            </Text>
            <Input
              variant="outline"
              size="md"
              isInvalid={!!(formData.isVirtual ? errors.meetingLink : errors.location)}
            >
              <InputSlot pl="$3">
                <InputIcon as={formData.isVirtual ? Video : MapPin} />
              </InputSlot>
              <InputField
                placeholder={formData.isVirtual ? "输入会议链接或平台" : "输入会议地点"}
                value={formData.isVirtual ? formData.meetingLink : formData.location}
                onChangeText={(text) => updateFormData(
                  formData.isVirtual
                    ? { meetingLink: text }
                    : { location: text }
                )}
              />
            </Input>
            {(formData.isVirtual ? errors.meetingLink : errors.location) && (
              <Text size="sm" color="$error600">
                {formData.isVirtual ? errors.meetingLink : errors.location}
              </Text>
            )}
          </VStack>

          {/* Participants Section */}
          <VStack space="sm">
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                参与者 * ({formData.participantIds.length})
              </Text>
              <StandardButton
                size="sm"
                variant="outline"
                onPress={() => setShowParticipantModal(true)}
                leftIcon={Plus}
              >
                添加参与者
              </StandardButton>
            </HStack>

            {/* Selected Participants Display */}
            {selectedParticipants.length > 0 ? (
              <VStack space="sm">
                {selectedParticipants.map((contact) => (
                  <HStack key={contact.id} space="md" alignItems="center" p="$2" bg="$backgroundLight100" borderRadius="$md">
                    <Avatar size="sm" bg="$primary300">
                      <AvatarFallbackText>{getInitials(contact.name)}</AvatarFallbackText>
                      {contact.avatar && <AvatarImage source={{ uri: contact.avatar }} alt={contact.name} />}
                    </Avatar>
                    <VStack flex={1}>
                      <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                        {contact.name}
                      </Text>
                      <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                        {contact.company && contact.position
                          ? `${contact.position} at ${contact.company}`
                          : contact.company || contact.position || '无公司信息'
                        }
                      </Text>
                    </VStack>
                    <Pressable onPress={() => toggleParticipant(contact.id)} p="$1">
                      <StandardIcon as={X} size="sm" color="$error600" />
                    </Pressable>
                  </HStack>
                ))}
              </VStack>
            ) : (
              <Box p="$4" borderWidth="$1" borderColor="$borderLight200" borderRadius="$md" borderStyle="dashed">
                <Text textAlign="center" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                  点击"添加参与者"选择会议参与者
                </Text>
              </Box>
            )}

            {errors.participants && (
              <Text size="sm" color="$error600">{errors.participants}</Text>
            )}
          </VStack>

          {/* Meeting Notes */}
          <VStack space="sm">
            <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              会议备注
            </Text>
            <Textarea>
              <TextareaInput
                placeholder="输入会议备注、要点、行动项目..."
                value={formData.notes}
                onChangeText={(text) => updateFormData({ notes: text })}
                numberOfLines={4}
              />
            </Textarea>
          </VStack>

          {/* Follow-up Tasks */}
          <VStack space="sm">
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                跟进任务 ({formData.tasks.length})
              </Text>
              <StandardButton
                size="sm"
                variant="outline"
                onPress={addTask}
                leftIcon={Plus}
              >
                添加任务
              </StandardButton>
            </HStack>

            {formData.tasks.length > 0 ? (
              <VStack space="sm">
                {formData.tasks.map((task, index) => (
                  <HStack key={index} space="sm" alignItems="center">
                    <Input flex={1} variant="outline" size="md">
                      <InputField
                        placeholder="输入任务内容"
                        value={task.title}
                        onChangeText={(text) => updateTask(index, { title: text })}
                      />
                    </Input>
                    <Pressable onPress={() => removeTask(index)} p="$2">
                      <StandardIcon as={Minus} size="md" color="$error600" />
                    </Pressable>
                  </HStack>
                ))}
              </VStack>
            ) : (
              <Box p="$4" borderWidth="$1" borderColor="$borderLight200" borderRadius="$md" borderStyle="dashed">
                <Text textAlign="center" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                  点击"添加任务"创建跟进任务
                </Text>
              </Box>
            )}
          </VStack>

          {/* Form Actions */}
          <HStack space="md" mt="$6">
            <StandardButton
              variant="outline"
              onPress={handleCancel}
              flex={1}
            >
              取消
            </StandardButton>
            <StandardButton
              variant="solid"
              action="primary"
              onPress={handleSaveMeeting}
              flex={1}
            >
              保存会议
            </StandardButton>
          </HStack>

          {/* Spacer for keyboard */}
          <Box h="$16" />
        </VStack>
      </ScrollView>
    </KeyboardAvoidingView>
  );

  // Render meeting item for history list
  const renderMeetingItem = ({ item: meeting }: { item: Meeting }) => {
    const participants = contacts.filter(contact =>
      meeting.participantIds.includes(contact.id)
    );

    const handleEditMeeting = () => {
      // TODO: 实现编辑会议功能
      console.log('Edit meeting', meeting.id);
    };

    const handleDeleteMeeting = () => {
      deleteMeeting(meeting.id);
      toast.show({
        placement: 'top',
        render: ({ id }) => (
          <Toast nativeID={id} action="success" variant="accent">
            <ToastTitle>会议已删除</ToastTitle>
            <ToastDescription>会议已从日程中移除</ToastDescription>
          </Toast>
        ),
      });
    };

    return (
      <Pressable
        onPress={() => {
          // TODO: 导航到会议详情页面
          console.log('View meeting details', meeting.id);
        }}
        mb="$4"
      >
        <Box
          p="$4"
          bg="$backgroundLight0"
          borderRadius="$lg"
          borderWidth="$1"
          borderColor="$borderLight200"
          sx={{
            _dark: {
              bg: '$backgroundDark900',
              borderColor: '$borderDark700',
            },
            _pressed: {
              opacity: 0.8,
            },
          }}
        >
          <VStack space="md">
            {/* Meeting Header */}
            <HStack justifyContent="space-between" alignItems="flex-start">
              <VStack flex={1} space="xs">
                <Text fontWeight="$bold" size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                  {meeting.title}
                </Text>
                <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                  {formatDateTime(meeting.date)}
                </Text>
              </VStack>
              <Badge
                size="sm"
                variant="solid"
                action={
                  meeting.status === 'completed' ? 'success' :
                  meeting.status === 'cancelled' ? 'error' :
                  meeting.status === 'in-progress' ? 'warning' : 'info'
                }
              >
                <BadgeText>
                  {meeting.status === 'completed' ? '已完成' :
                   meeting.status === 'cancelled' ? '已取消' :
                   meeting.status === 'in-progress' ? '进行中' : '已安排'}
                </BadgeText>
              </Badge>
            </HStack>

            {/* Location */}
            <HStack space="sm" alignItems="center">
              <StandardIcon
                as={meeting.isVirtual ? Video : MapPin}
                size="sm"
                color="$textLight600"
              />
              <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                {meeting.location || '无地点信息'}
              </Text>
            </HStack>

            {/* Participants */}
            <HStack space="sm" alignItems="center">
              <StandardIcon as={Users} size="sm" color="$textLight600" />
              <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                参与者:
              </Text>
              <HStack space="xs">
                {participants.slice(0, 3).map((contact, index) => (
                  <Avatar key={contact.id} size="xs" bg="$primary300" ml={index > 0 ? "-$1" : "$0"}>
                    <AvatarFallbackText>{getInitials(contact.name)}</AvatarFallbackText>
                    {contact.avatar && <AvatarImage source={{ uri: contact.avatar }} alt={contact.name} />}
                  </Avatar>
                ))}
                {participants.length > 3 && (
                  <Box
                    w="$6"
                    h="$6"
                    borderRadius="$full"
                    bg="$neutral300"
                    justifyContent="center"
                    alignItems="center"
                    ml="-$1"
                  >
                    <Text size="xs" color="$textLight700">
                      +{participants.length - 3}
                    </Text>
                  </Box>
                )}
              </HStack>
            </HStack>

            {/* Tasks */}
            {meeting.tasks.length > 0 && (
              <VStack space="xs">
                <HStack space="sm" alignItems="center">
                  <StandardIcon as={FileText} size="sm" color="$textLight600" />
                  <Text size="sm" fontWeight="$medium" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                    跟进任务:
                  </Text>
                </HStack>
                {meeting.tasks.slice(0, 2).map((task, index) => (
                  <HStack key={task.id} space="sm" alignItems="center" ml="$6">
                    <Checkbox
                      size="sm"
                      isChecked={task.isCompleted}
                      onChange={() => toggleTaskCompletion(meeting.id, task.id)}
                    >
                      <CheckboxIndicator>
                        <CheckboxIcon as={CheckIcon} />
                      </CheckboxIndicator>
                    </Checkbox>
                    <Text
                      size="sm"
                      color={task.isCompleted ? "$textLight500" : "$textLight700"}
                      sx={{
                        _dark: { color: task.isCompleted ? "$textDark500" : "$textDark300" },
                        textDecorationLine: task.isCompleted ? 'line-through' : 'none'
                      }}
                      flex={1}
                    >
                      {task.title}
                    </Text>
                  </HStack>
                ))}
                {meeting.tasks.length > 2 && (
                  <Text size="xs" color="$primary600" ml="$6">
                    +{meeting.tasks.length - 2} 个更多任务
                  </Text>
                )}
              </VStack>
            )}

            <Divider />

            {/* Actions */}
            <HStack justifyContent="flex-end" space="sm">
              <Pressable onPress={handleEditMeeting} p="$2">
                <StandardIcon as={Edit} size="sm" color="$textLight600" />
              </Pressable>
              <Pressable onPress={handleDeleteMeeting} p="$2">
                <StandardIcon as={Trash2} size="sm" color="$error600" />
              </Pressable>
            </HStack>
          </VStack>
        </Box>
      </Pressable>
    );
  };
  
  // Render history tab
  const renderHistoryTab = () => {
    const allMeetings = [...upcomingMeetings, ...pastMeetings].sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    return (
      <Box flex={1} p="$4">
        {allMeetings.length > 0 ? (
          <FlatList
            data={allMeetings}
            keyExtractor={(item) => item.id}
            renderItem={renderMeetingItem}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        ) : (
          <Box flex={1} justifyContent="center" alignItems="center">
            <VStack space="md" alignItems="center">
              <StandardIcon as={Calendar} size="4xl" color="$textLight400" />
              <Text textAlign="center" color="$textLight500" sx={{ _dark: { color: '$textDark500' } }}>
                还没有会议记录
              </Text>
              <Text textAlign="center" color="$textLight400" sx={{ _dark: { color: '$textDark600' } }}>
                创建第一个会议来开始记录
              </Text>
              <StandardButton
                variant="outline"
                onPress={() => setActiveTab('new')}
                mt="$4"
              >
                创建会议
              </StandardButton>
            </VStack>
          </Box>
        )}
      </Box>
    );
  };

  // Render participant selection modal
  const renderParticipantModal = () => (
    <Modal isOpen={showParticipantModal} onClose={() => setShowParticipantModal(false)}>
      <ModalBackdrop />
      <ModalContent maxWidth="$96">
        <ModalHeader>
          <Heading size="lg">选择参与者</Heading>
          <ModalCloseButton>
            <StandardIcon as={X} />
          </ModalCloseButton>
        </ModalHeader>
        <ModalBody>
          <VStack space="md">
            {/* Search Input */}
            <Input>
              <InputSlot pl="$3">
                <InputIcon as={Search} />
              </InputSlot>
              <InputField
                placeholder="搜索联系人"
                value={participantSearchQuery}
                onChangeText={setParticipantSearchQuery}
              />
            </Input>

            {/* Contact List */}
            <ScrollView maxHeight="$80">
              <VStack space="sm">
                {filteredContacts.map((contact) => (
                  <Pressable
                    key={contact.id}
                    onPress={() => toggleParticipant(contact.id)}
                    p="$3"
                    borderRadius="$md"
                    bg={formData.participantIds.includes(contact.id) ? "$primary100" : "transparent"}
                    sx={{
                      _pressed: { opacity: 0.7 },
                      _dark: {
                        bg: formData.participantIds.includes(contact.id) ? "$primary900" : "transparent"
                      }
                    }}
                  >
                    <HStack space="md" alignItems="center">
                      <Avatar size="sm" bg="$primary300">
                        <AvatarFallbackText>{getInitials(contact.name)}</AvatarFallbackText>
                        {contact.avatar && <AvatarImage source={{ uri: contact.avatar }} alt={contact.name} />}
                      </Avatar>
                      <VStack flex={1}>
                        <Text fontWeight="$medium" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
                          {contact.name}
                        </Text>
                        <Text size="sm" color="$textLight600" sx={{ _dark: { color: '$textDark400' } }}>
                          {contact.company && contact.position
                            ? `${contact.position} at ${contact.company}`
                            : contact.company || contact.position || '无公司信息'
                          }
                        </Text>
                      </VStack>
                      {formData.participantIds.includes(contact.id) && (
                        <StandardIcon as={CheckCircle} size="md" color="$primary600" />
                      )}
                    </HStack>
                  </Pressable>
                ))}
              </VStack>
            </ScrollView>
          </VStack>
        </ModalBody>
        <ModalFooter>
          <StandardButton
            variant="outline"
            onPress={() => setShowParticipantModal(false)}
            mr="$3"
          >
            取消
          </StandardButton>
          <StandardButton
            variant="solid"
            action="primary"
            onPress={() => setShowParticipantModal(false)}
          >
            确定 ({formData.participantIds.length})
          </StandardButton>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );

  return (
    <ErrorBoundary>
      <Box flex={1} bg="$backgroundLight50" sx={{ _dark: { bg: '$backgroundDark950' } }}>
        {/* Header */}
        <Box
          bg="$backgroundLight0"
          pt={Platform.OS === 'ios' ? '$12' : '$6'}
          pb="$4"
          px="$4"
          borderBottomWidth="$1"
          borderBottomColor="$borderLight200"
          sx={{
            _dark: {
              bg: '$backgroundDark900',
              borderBottomColor: '$borderDark700',
            },
          }}
        >
          <HStack justifyContent="space-between" alignItems="center">
            <Pressable onPress={() => navigation.goBack()} p="$1">
              <StandardIcon as={ArrowLeft} size="lg" color="$primary600" />
            </Pressable>

            <Heading size="lg" color="$textLight900" sx={{ _dark: { color: '$textDark100' } }}>
              {activeTab === 'new' ? '记录会议' : '会议历史'}
            </Heading>

            <Box w="$8" />
          </HStack>
        </Box>

        {/* Tab Navigation */}
        <Box
          bg="$backgroundLight0"
          borderBottomWidth="$1"
          borderBottomColor="$borderLight200"
          sx={{
            _dark: {
              bg: '$backgroundDark900',
              borderBottomColor: '$borderDark700',
            },
          }}
        >
          <HStack>
            <Pressable
              flex={1}
              py="$4"
              alignItems="center"
              borderBottomWidth="$2"
              borderBottomColor={activeTab === 'new' ? '$primary600' : 'transparent'}
              onPress={() => setActiveTab('new')}
            >
              <VStack space="xs" alignItems="center">
                <StandardIcon
                  as={Plus}
                  size="md"
                  color={activeTab === 'new' ? '$primary600' : '$textLight600'}
                />
                <Text
                  fontWeight="$medium"
                  color={activeTab === 'new' ? '$primary600' : '$textLight600'}
                  sx={{ _dark: { color: activeTab === 'new' ? '$primary400' : '$textDark400' } }}
                >
                  新建会议
                </Text>
              </VStack>
            </Pressable>

            <Pressable
              flex={1}
              py="$4"
              alignItems="center"
              borderBottomWidth="$2"
              borderBottomColor={activeTab === 'history' ? '$primary600' : 'transparent'}
              onPress={() => setActiveTab('history')}
            >
              <VStack space="xs" alignItems="center">
                <StandardIcon
                  as={Clock}
                  size="md"
                  color={activeTab === 'history' ? '$primary600' : '$textLight600'}
                />
                <Text
                  fontWeight="$medium"
                  color={activeTab === 'history' ? '$primary600' : '$textLight600'}
                  sx={{ _dark: { color: activeTab === 'history' ? '$primary400' : '$textDark400' } }}
                >
                  历史记录
                </Text>
              </VStack>
            </Pressable>
          </HStack>
        </Box>

        {/* Content */}
        <Box flex={1}>
          {activeTab === 'new' ? renderNewMeetingTab() : renderHistoryTab()}
        </Box>

        {/* Participant Selection Modal */}
        {renderParticipantModal()}
      </Box>
    </ErrorBoundary>
  );
};

// MeetingRecordScreen 组件完全重构完成
// 使用 Gluestack UI 和全局状态管理，移除了所有 StyleSheet 样式

export default MeetingRecordScreen;