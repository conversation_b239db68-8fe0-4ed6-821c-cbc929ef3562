import React from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ScrollView,
  StyleProp,
  ViewStyle,
  TextStyle,
  Platform
} from 'react-native';
import {
  Text,
  Box,
  Pressable,
  useToken
} from '@gluestack-ui/themed';

const { width } = Dimensions.get('window');

export interface AlertButton {
  text: string;
  onPress: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

interface CustomAlertModalProps {
  visible: boolean;
  title: string;
  message: string;
  buttons: AlertButton[];
  onClose?: () => void; // Optional: for backdrop press or hardware back button
}

const CustomAlertModal: React.FC<CustomAlertModalProps> = ({
  visible,
  title,
  message,
  buttons,
  onClose,
}) => {
  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose} // For Android hardware back button
    >
      <Pressable
        sx={{ bg: '$blackA8' }}
        style={styles.modalOverlay}
        onPress={onClose} // Allow closing by tapping backdrop if onClose is provided
      >
        <Box
          sx={{ bg: '$white' }}
          style={styles.modalContent}
          onStartShouldSetResponder={() => true} // Prevents backdrop press from passing through
        >
          <Text sx={{ color: '$trueGray900' }} style={styles.modalTitle}>{title}</Text>
          <ScrollView style={styles.messageScrollView} contentContainerStyle={styles.messageContentContainer}>
            <Text sx={{ color: '$trueGray700' }} style={styles.modalMessage}>{message}</Text>
          </ScrollView>
          <View style={styles.buttonsContainer}>
            {buttons.map((button, index) => {
              let buttonStyle: StyleProp<ViewStyle> = styles.button;
              let textStyle: StyleProp<TextStyle> = styles.buttonText;
              
              let pressableSx: any = { bg: '$primary500' };
              let textSx: any = { color: '$textLight0' };

              if (button.style === 'destructive') {
                buttonStyle = [styles.button, styles.destructiveButton];
                textStyle = [styles.buttonText, styles.destructiveButtonText];
                pressableSx = { bg: '$error500' };
                textSx = { color: '$textLight0' };
              } else if (button.style === 'cancel') {
                buttonStyle = [styles.button, styles.cancelButton];
                textStyle = [styles.buttonText, styles.cancelButtonText];
                pressableSx = { bg: '$trueGray300' };
                textSx = { color: '$trueGray900' };
              }

              return (
                <Pressable
                  key={index}
                  sx={pressableSx}
                  style={buttonStyle}
                  onPress={() => {
                    button.onPress();
                  }}
                >
                  <Text sx={textSx} style={textStyle}>{button.text}</Text>
                </Pressable>
              );
            })}
          </View>
        </Box>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: width * 0.85,
    maxWidth: 350,
    
    borderRadius: 12,
    padding: 20,
    // Platform-specific shadow styles
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
    } : {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: Platform.OS === 'android' ? 5 : 0,
    }),
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  messageScrollView: {
    maxHeight: Dimensions.get('window').height * 0.3, // Limit message height
    marginBottom: 20,
  },
  messageContentContainer: {
    paddingVertical: 5, // Add some padding if message is short
  },
  modalMessage: {
    fontSize: 16,
    
    textAlign: 'center',
    lineHeight: 22,
  },
  buttonsContainer: {
    flexDirection: 'column',
    width: '100%',
  },
  button: {
    
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  cancelButton: {
    
  },
  cancelButtonText: {
    // Or a different color for cancel text
  },
  destructiveButton: {
    
  },
  destructiveButtonText: {
    color: '#FFFFFF' /* TODO: Replace with Gluestack UI token */,
  },
});

export default CustomAlertModal;
