import React from 'react';
import { Linking } from 'react-native';
import { useTranslation } from 'react-i18next';
import { 
  Box, 
  Text, 
  VStack, 
  HStack, 
  Icon, 
  Pressable, 
  Badge, 
  BadgeText, 
  BadgeIcon,
  Tooltip,
  TooltipContent,
  TooltipText
} from '@gluestack-ui/themed';
import { 
  Mail, 
  Phone, 
  Building2, 
  Globe, 
  Users, 
  Layers, 
  Clock, 
  Edit, 
  PlusCircle, 
  AlertCircle, 
  CheckCircle, 
  Linkedin, 
  Twitter, 
  Github 
} from 'lucide-react-native';
import TagItem from './TagItem'; // Assuming this will be refactored too
import ReminderItem from './ReminderItem'; // Assuming this will be refactored too
import { ContactData, Tag, Reminder, SocialLink } from '../hooks/useContactDetail';

interface ProfileTabProps {
  contact: ContactData;
  onAddTag: () => void;
  onTagPress: (tag: Tag) => void;
  onEditNotes: () => void;
  onAddReminder: () => void;
  onCompleteReminder: (reminderId: string, reminderTitle: string) => void;
  onEditTag: (tag: Tag) => void;
  onEditReminder: (reminder: Reminder) => void; // Added prop for editing reminders
}

const ProfileTab: React.FC<ProfileTabProps> = (props) => {
  return (
    <VStack space="md">
      <ContactDetailsSection contact={props.contact} />
      {props.contact.socialLinks && props.contact.socialLinks.length > 0 && (
        <SocialProfilesSection socialLinks={props.contact.socialLinks} />
      )}
      <TagsSection tags={props.contact.tags} onAddTag={props.onAddTag} onEditTag={props.onEditTag} />
      <NotesSection notes={props.contact.notes} onEditNotes={props.onEditNotes} />
      <RemindersSection reminders={props.contact.reminders} onAddReminder={props.onAddReminder} onCompleteReminder={props.onCompleteReminder} onEditReminder={props.onEditReminder} />
    </VStack>
  );
};

interface SectionProps {
  title: string;
  actionIcon?: React.ElementType;
  onActionPress?: () => void;
  children: React.ReactNode;
}

const Section: React.FC<SectionProps> = ({ title, actionIcon, onActionPress, children }) => (
  <Box bg="$white" borderRadius={'$lg'} p={'$4'} mb={'$4'}>
    <HStack justifyContent="space-between" alignItems="center" mb={'$3'}>
      <Text fontSize={'$lg'} fontWeight={'$semibold'}>{title}</Text>
      {actionIcon && onActionPress && (
        <Pressable onPress={onActionPress} p={'$2'}>
          <Icon as={actionIcon} size="xl" color="$primary500" />
        </Pressable>
      )}
    </HStack>
    {children}
  </Box>
);

interface DetailItemProps {
  icon: React.ElementType;
  text: string;
  isLink?: boolean;
  onLinkPress?: () => void;
  hasPendingUpdate?: boolean;
  isVerified?: boolean;
  verifiedTooltip?: string;
}

const DetailItem: React.FC<DetailItemProps> = ({ icon, text, isLink, onLinkPress, hasPendingUpdate, isVerified, verifiedTooltip }) => (
  <HStack alignItems="center" py={'$2.5'} borderBottomWidth={1} borderBottomColor={'$backgroundLight200'}>
    <Icon as={icon} size="lg" color="$textLight600" w={'$6'} />
    <Pressable onPress={onLinkPress} disabled={!isLink}>
      <Text ml={'$3'} color={isLink ? '$primary500' : '$textLight800'} textDecorationLine={isLink ? 'underline' : 'none'}>
        {text}
      </Text>
    </Pressable>
    {hasPendingUpdate && <Icon as={AlertCircle} size="md" color="$warning500" ml={'$2'} />}
    {isVerified && verifiedTooltip && (
      <Tooltip
        placement="top"
        trigger={(triggerProps) => {
          return (
            <Pressable {...triggerProps}>
              <Icon as={CheckCircle} size="md" color="$success500" ml={'$2'} />
            </Pressable>
          );
        }}
      >
        <TooltipContent>
          <TooltipText>{verifiedTooltip}</TooltipText>
        </TooltipContent>
      </Tooltip>
    )}
  </HStack>
);

interface ContactDetailsSectionProps {
  contact: ContactData;
}

const ContactDetailsSection: React.FC<ContactDetailsSectionProps> = ({ contact }) => {
  const companyDetails = contact.companyDetails;
  const { t } = useTranslation();
  return (
    <Section title={t('contactDetails')}>
      <DetailItem icon={Mail} text={contact.email} hasPendingUpdate={contact.emailHasPendingUpdate} />
      <DetailItem icon={Phone} text={contact.phone} hasPendingUpdate={contact.phoneHasPendingUpdate} />
      <DetailItem 
        icon={Building2} 
        text={`${contact.position} at ${contact.company}`}
        isVerified={companyDetails?.isVerified}
        verifiedTooltip={t('verifiedCompany')}
      />
      {(() => {
        if (companyDetails && companyDetails.website) {
          // After this check, companyDetails.website is guaranteed to be a string.
          const currentWebsite = companyDetails.website;
          return (
            <DetailItem 
              icon={Globe} 
              text={currentWebsite} 
              isLink 
              onLinkPress={() => {
                // currentWebsite is guaranteed to be a string here.
                Linking.openURL(currentWebsite);
              }}
              hasPendingUpdate={companyDetails.websiteHasPendingUpdate}
            />
          );
        }
        return null;
      })()}
      {(() => {
        if (companyDetails && companyDetails.size) {
          const sizeStr: string = companyDetails.size;
          return <DetailItem icon={Users} text={sizeStr} />;
        }
        return null;
      })()}
      {(() => {
        if (companyDetails && companyDetails.industry) {
          const industryStr: string = companyDetails.industry;
          return <DetailItem icon={Layers} text={industryStr} />;
        }
        return null;
      })()}
      <DetailItem icon={Clock} text={`Last contacted on ${contact.lastContact}`} />
    </Section>
  );
};

interface SocialProfilesSectionProps {
  socialLinks: SocialLink[];
}

const SocialProfilesSection: React.FC<SocialProfilesSectionProps> = ({ socialLinks }) => {
  const { t } = useTranslation();
  const getIcon = (iconName: string) => {
    switch(iconName) {
      case 'linkedin': return Linkedin;
      case 'twitter': return Twitter;
      case 'github': return Github;
      default: return Globe;
    }
  }
  return (
    <Section title={t('socialProfiles')}>
      {socialLinks.map((link: SocialLink) => (
        <DetailItem 
          key={link.id} 
          icon={getIcon(link.icon || 'globe')} 
          text={(link.username || link.url) ?? ''} 
          isLink 
          onLinkPress={() => {
            if (link.url) {
              Linking.openURL(link.url);
            } else {
              console.warn('Attempted to open undefined social URL for:', link);
            }
          }} 
        />
      ))}
    </Section>
  );
};

interface TagsSectionProps {
  tags: Tag[];
  onAddTag: () => void;
  onEditTag: (tag: Tag) => void;
}

const TagsSection: React.FC<TagsSectionProps> = ({ tags, onAddTag, onEditTag }) => (
  <Section title="Tags" actionIcon={PlusCircle} onActionPress={onAddTag}>
    <HStack flexWrap="wrap" space="sm">
      {tags.map((tag: Tag) => (
        <TagItem key={tag.id} tag={tag} onPress={onEditTag} />
      ))}
    </HStack>
  </Section>
);

interface NotesSectionProps {
  notes?: string | null;
  onEditNotes: () => void;
}

const NotesSection: React.FC<NotesSectionProps> = ({ notes, onEditNotes }) => (
  <Section title="Notes" actionIcon={Edit} onActionPress={onEditNotes}>
    <Text color={'$textLight600'} lineHeight={'$md'}>{notes || 'No notes yet.'}</Text>
  </Section>
);

interface RemindersSectionProps {
  reminders: Reminder[];
  onAddReminder: () => void;
  onCompleteReminder: (reminderId: string, reminderTitle: string) => void;
  onEditReminder: (reminder: Reminder) => void;
}

const RemindersSection: React.FC<RemindersSectionProps> = ({ reminders, onAddReminder, onCompleteReminder, onEditReminder }) => (
  <Section title="Reminders" actionIcon={PlusCircle} onActionPress={onAddReminder}>
    {reminders.length > 0 ? (
      reminders.map((reminder: Reminder) => (
        <ReminderItem key={reminder.id} reminder={reminder} onComplete={onCompleteReminder} onPress={onEditReminder} />
      ))
    ) : (
      <Text textAlign="center" color={'$textLight500'} mt={'$2'}>No reminders set.</Text>
    )}
  </Section>
);



export default ProfileTab;
