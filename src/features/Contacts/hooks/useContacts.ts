import { useState, useEffect } from 'react';
import { Contact } from '../../../types';

// Mock data for initial development
const mockContacts: Contact[] = [
  { id: '1', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?u=1', position: 'Software Engineer', company: 'Tech Solutions' },
  { id: '2', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?u=2', position: 'Product Manager', company: 'Innovate Inc.' },
  { id: '3', name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?u=3', position: 'UX Designer', company: 'Creative Minds' },
];

export const useContacts = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // In a real app, you would fetch this data from an API
    try {
      setContacts(mockContacts);
    } catch (e) {
      setError('Failed to fetch contacts');
    }
    setLoading(false);
  }, []);

  return { contacts, loading, error };
};
