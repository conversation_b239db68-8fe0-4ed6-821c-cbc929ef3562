# ContactCreateScreen Refactor Tasks

- **重构状态:** ToDo
- **主要区域/视图:**
    - **1. 顶部导航/操作区 (Header)**
        - **描述:** 包含取消按钮、屏幕标题 (\"Create Contact\" 或 \"Edit Contact\")、保存按钮。
        - **现有主要组件:** `View`, `TouchableOpacity` (Cancel, Save), `Text` (Title), `Ionicons`.
        - **Gluestack UI 迁移建议:**
            - `View` (header container) -> `HStack` or `Box` with `justifyContent=\"space-between\"`, `alignItems=\"center\"`.
            - `TouchableOpacity` (Cancel) -> `Button` (`variant=\"link\"`, `action=\"secondary\"` or `textual`).
            - `TouchableOpacity` (Save) -> `Button` (`variant=\"solid\"`, `action=\"primary\"`).
            - `Text` (Title) -> `Heading`.
            - `Ionicons` (if any for save/cancel) -> `lucide-react-native` icons within `Button`.
        - **设计规范参考:** 页面顶部标题与内容间距, 按钮样式.

    - **2. 联系人头像选择区 (Avatar Picker)**
        - **描述:** 显示当前头像或占位符，点击可选择图片。
        - **现有主要组件:** `TouchableOpacity`, `Image` (avatar), `Ionicons` (camera icon overlay).
        - **Gluestack UI 迁移建议:**
            - `TouchableOpacity` (wrapper) -> `Pressable` wrapping an `Avatar`.
            - `Image` (avatar) -> `Avatar` component (`size=\"xl\"` or `2xl\"`).
            - `Ionicons` (camera icon) -> `Icon` (e.g., `Camera` from `lucide-react-native`) positioned over/near the `Avatar`, possibly using `Badge` for the icon background if desired.
            - Image picker logic (`ImagePicker.launchImageLibraryAsync`) remains.
        - **设计规范参考:** 头像尺寸, 图标样式.

    - **3. 基本信息输入区 (Basic Info Form)**
        - **描述:** 包含姓、名、公司、职位、电话、邮箱等输入字段。
        - **现有主要组件:** `ScrollView`, `View` (form group), `Text` (label), `TextInput`.
        - **Gluestack UI 迁移建议:**
            - `ScrollView` -> `ScrollView`.
            - `View` (form group, e.g., for a label + input) -> `VStack` or `FormControl` from `gluestack-ui`.
                - `FormControl.Label` -> `Text` for the label.
                - `TextInput` -> `Input` with `InputField`.
                - `FormControl.HelperText` or `FormControl.ErrorMessage` for hints or validation errors.
            - For multiple phone numbers/emails: Logic for adding/removing fields will remain, with each field being an `Input`.
        - **设计规范参考:** 输入框样式, 标签文本, 错误提示文本, 间距.

    - **4. 详细信息输入区 (Detailed Info Form - e.g., Address, Website, Social Media)**
        - **描述:** 结构类似基本信息区，但针对地址（街道、城市、州、邮编、国家）、网站、社交媒体链接等。
        - **现有主要组件:** `View`, `Text` (label), `TextInput`.
        - **Gluestack UI 迁移建议:**
            - Similar to Basic Info: `FormControl`, `Input` with `InputField`.
            - Address block might be a `VStack` of `Input` components.
        - **设计规范参考:** 输入框样式, 布局.

    - **5. 标签选择区 (Tag Selection)**
        - **描述:** 允许用户为联系人选择或创建标签。可能以Tab形式展示不同类别的标签，或一个可搜索的列表。
        - **现有主要组件:** `View` (tabs/section), `TouchableOpacity` (tag item), `Text` (tag name), `Ionicons` (add tag icon). `LinearGradient` might be used for tab backgrounds.
        - **Gluestack UI 迁移建议:**
            - If Tabs: `Tabs` component.
                - `Tabs.TabList`, `Tabs.Tab`, `Tabs.TabTitle`.
                - `Tabs.TabPanels`, `Tabs.TabPanel` containing lists of selectable tags.
            - Selectable tags: `Pressable` wrapping a `Badge` or `Checkbox` with `Text`.
            - \"Add Tag\" button -> `Button` (`variant=\"outline\"` or `link`).
            - `LinearGradient`: If `gluestack-ui` components don't directly support `LinearGradient` as a background, wrap the `gluestack-ui` component with a `LinearGradient` component, or use `sx` prop to apply gradient if possible (less likely for complex gradients). The primary approach is to use solid colors from the theme. If gradient is critical, it might require custom styling.
        - **设计规范参考:** 标签/徽章样式, Tab样式, 按钮.

    - **6. 备注区 (Notes Section)**
        - **描述:** 多行文本输入框，用于添加备注。
        - **现有主要组件:** `TextInput` (multiline).
        - **Gluestack UI 迁移建议:**
            - `TextInput` (multiline) -> `Textarea` with `TextareaInput`.
        - **设计规范参考:** 多行输入框样式.

    - **7. 模态框 (Modals - e.g., for Tag Creation)**
        - **描述:** 如果创建新标签是在模态框中进行的。
        - **现有主要组件:** `Modal`, `View`, `TextInput`, `Button`.
        - **Gluestack UI 迁移建议:**
            - `Modal` -> `Modal` component.
                - `Modal.Backdrop`, `Modal.Content`, `Modal.Header`, `Modal.Body`, `Modal.Footer`.
            - Components within modal: `Input`, `Button`.
        - **设计规范参考:** 模态框样式.

- **整体注意事项:**
    - **图标替换:** All `Ionicons`, `MaterialCommunityIcons`, etc., to `lucide-react-native`.
    - **颜色和样式:** Migrate `StyleSheet` to `gluestack-ui` props/`sx`, using theme tokens.
    - **表单验证:** Implement or enhance form validation, showing error states on `Input` components using `FormControl.ErrorMessage`.
    - **`LinearGradient`:** As noted, direct `LinearGradient` application on `gluestack-ui` components might be tricky. Prioritize theme colors. If gradient is essential, explore wrapping or check `gluestack-ui` documentation for advanced styling or custom component creation.
    - **State Management:** Form state (`useState`, `useReducer`, or a form library) will be preserved.
    - **Accessibility:** Ensure all form controls have proper labels and accessibility props.
