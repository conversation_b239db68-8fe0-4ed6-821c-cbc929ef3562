import React from 'react';
import {
  Box,
  Text,
  Pressable,
  VStack,
  HStack,
  Icon,
  Badge,
  BadgeText,
  BadgeIcon,
} from '@gluestack-ui/themed';
import { Users, Mail, Phone, FileText, Trophy, Gift, CheckSquare, Square } from 'lucide-react-native';
import { Interaction, ActionItem } from '../hooks/useContactDetail';

interface InteractionItemProps {
  interaction: Interaction;
  onPress: () => void;
  expanded: boolean;
  onToggleExpand: () => void;
}

const InteractionItem: React.FC<InteractionItemProps> = ({
  interaction,
  onPress,
  expanded,
  onToggleExpand,
}) => {
  const getInteractionIcon = () => {
    switch (interaction.type) {
      case 'meeting':
        return <Icon as={Users} size="md" color="$teal700" />;
      case 'email':
        return <Icon as={Mail} size="md" color="$blue700" />;
      case 'call':
        return <Icon as={Phone} size="md" color="$purple700" />;
      case 'note':
        return <Icon as={FileText} size="md" color="$orange700" />;
      case 'milestone':
        return <Icon as={Trophy} size="md" color="$yellow700" />;
      case 'personal_event':
        return <Icon as={Gift} size="md" color="$pink700" />;
      default:
        return null;
    }
  };

  const handlePress = () => {
    if (interaction.type === 'meeting') {
      onToggleExpand();
    } else {
      onPress();
    }
  };

  const sentimentColorScheme: Record<'positive' | 'neutral' | 'negative', 'success' | 'muted' | 'error'> = {
    positive: 'success',
    neutral: 'muted',
    negative: 'error',
  };

  return (
    <Pressable 
      onPress={handlePress} 
      bg={ (interaction.type === 'milestone' || interaction.type === 'personal_event') ? "$primary100" : "$backgroundLight0"}
      p={'$3'} 
      mb={'$3'} 
      borderRadius={'$lg'} 
      borderWidth={1} 
      borderColor={'$backgroundLight200'}
    >
      <HStack space="md" alignItems="center">
        <Box w={'$10'} alignItems="center">
          {getInteractionIcon()}
        </Box>
        <VStack flex={1}>
          <Text fontWeight={'$semibold'} fontSize={'$md'}>{interaction.title}</Text>
          <Text fontSize={'$xs'} color={'$textLight500'}>
            {typeof interaction.date === 'string' ? interaction.date : new Date(interaction.date).toLocaleDateString()}
          </Text>
          <HStack space="xs" flexWrap="wrap" mt={'$1'}>
            {interaction.sentiment && (
              <Badge size="sm" variant="solid" action={sentimentColorScheme[interaction.sentiment] || 'muted'} borderRadius={'$full'}>
                <BadgeText>{interaction.sentiment}</BadgeText>
              </Badge>
            )}
            {interaction.contentClassification && (
              <Badge size="sm" variant="solid" action="info" borderRadius={'$full'}>
                <BadgeText>{interaction.contentClassification}</BadgeText>
              </Badge>
            )}
            {interaction.userEvaluation && (
              <Badge size="sm" variant="solid" action="warning" borderRadius={'$full'}>
                <BadgeText>{interaction.userEvaluation.replace('_', ' ')}</BadgeText>
              </Badge>
            )}
          </HStack>
        </VStack>
      </HStack>
      {interaction.type === 'meeting' && expanded && (
        <VStack mt={'$3'} space="sm" pl={'$12'}> {/* Indent details */}
          {interaction.agenda && (
            <Box>
              <Text fontWeight={'$medium'} fontSize={'$sm'}>Agenda:</Text>
              <Text fontSize={'$sm'} color={'$textLight600'}>{interaction.agenda}</Text>
            </Box>
          )}
          {interaction.meetingSummary && (
            <Box>
              <Text fontWeight={'$medium'} fontSize={'$sm'}>Summary:</Text>
              <Text fontSize={'$sm'} color={'$textLight600'}>{interaction.meetingSummary}</Text>
            </Box>
          )}
          {interaction.actionItems && interaction.actionItems.length > 0 && (
            <Box>
              <Text fontWeight={'$medium'} fontSize={'$sm'}>Action Items:</Text>
              {interaction.actionItems.map((item: ActionItem) => (
                <HStack key={item.id} alignItems="center" space="sm" mt={'$1'}>
                  <Icon as={item.isCompleted ? CheckSquare : Square} size="sm" color={item.isCompleted ? '$success600' : '$textLight400'} />
                  <VStack flex={1}>
                    <Text fontSize={'$sm'} color={'$textLight700'}>{item.description}</Text>
                    {(item.assignedTo || item.dueDate) && (
                       <Text fontSize={'$xs'} color={'$textLight500'}>
                         {item.assignedTo && `Assigned: ${item.assignedTo}`}{item.assignedTo && item.dueDate && ' - '}
                         {item.dueDate && `Due: ${new Date(item.dueDate).toLocaleDateString()}`}
                       </Text>
                    )}
                  </VStack>
                </HStack>
              ))}
            </Box>
          )}
        </VStack>
      )}
    </Pressable>
  );
};

export default InteractionItem;
