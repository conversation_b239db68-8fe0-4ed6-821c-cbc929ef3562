import React from 'react';
import {
  VStack,
  HStack,
  Avatar,
  AvatarImage,
  AvatarFallbackText,
  Text,
  Pressable,
  Icon,
  Badge,
  BadgeText
} from '@gluestack-ui/themed';
import { ChevronRight } from 'lucide-react-native';

interface User {
  name: string;
  email: string;
  avatar: string;
  plan: string;
}

interface ProfileSectionProps {
  user: User;
  onPress?: () => void;
}

/**
 * 用户资料区域组件
 * 显示用户头像、姓名、邮箱和订阅计划
 */
const ProfileSection: React.FC<ProfileSectionProps> = ({ user, onPress }) => {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Pressable
      onPress={onPress}
      bg="$backgroundLight0"
      p="$5"
      flexDirection="row"
      alignItems="center"
      borderBottomWidth={1}
      borderBottomColor="$borderLight200"
      sx={{
        _dark: {
          bg: '$backgroundDark900',
          borderBottomColor: '$borderDark700'
        }
      }}
    >
      <Avatar size="xl" bg="$primary300" mr="$4">
        <AvatarImage 
          source={{ uri: user.avatar }} 
          alt={`${user.name} avatar`} 
        />
        <AvatarFallbackText>
          {getInitials(user.name)}
        </AvatarFallbackText>
      </Avatar>

      <VStack flex={1} space="xs">
        <Text 
          fontSize="$lg" 
          fontWeight="$semibold" 
          color="$textLight900"
          sx={{ _dark: { color: '$textDark100' } }}
        >
          {user.name}
        </Text>
        
        <Text 
          fontSize="$sm" 
          color="$textLight600"
          sx={{ _dark: { color: '$textDark400' } }}
        >
          {user.email}
        </Text>
        
        <Badge
          variant="solid"
          action="info"
          size="sm"
          alignSelf="flex-start"
          mt="$1"
        >
          <BadgeText>{user.plan}</BadgeText>
        </Badge>
      </VStack>

      {onPress && (
        <Icon 
          as={ChevronRight} 
          size="sm" 
          color="$textLight400"
          sx={{ _dark: { color: '$textDark600' } }}
        />
      )}
    </Pressable>
  );
};

export default ProfileSection;
