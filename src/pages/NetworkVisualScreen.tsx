import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  Modal,
  Platform
} from 'react-native';
import {
  Text,
  useToast, // Added
  Toast, // Added
  ToastTitle, // Added
  ToastDescription // Added
} from '@gluestack-ui/themed';
import { useNavigation } from '@react-navigation/native';
import { HelpCircle, Share, Plus, Minus, RefreshCw, ArrowLeft, SlidersHorizontal, UserPlus, ChevronRight, UserCircle2 } from 'lucide-react-native';
import { PanGestureHandler } from 'react-native-gesture-handler';
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
// import { NativeStackNavigationProp } from '@react-navigation/native-stack'; // If navigation prop is typed directly
import { RootStackParamList } from '../types'; // Assuming '../types' is correct
import { PanGestureHandlerGestureEvent } from 'react-native-gesture-handler'; // For gesture handler

type IndustryKey = 'Technology' | 'Sales' | 'Finance' | 'Marketing' | 'self';

interface NetworkNode {
  id: string;
  name: string;
  type: 'self' | 'contact';
  x: number;
  y: number;
  connections: string[];
  industry?: IndustryKey;
}

interface ConnectionLink {
  from: string;
  to: string;
}

type NetworkVisualScreenRouteProp = RouteProp<RootStackParamList, 'NetworkVisual'>; // Ensure 'NetworkVisual' is in RootStackParamList

interface NetworkVisualScreenProps {
  route: NetworkVisualScreenRouteProp;
}

type GestureContext = {
  startX: number;
  startY: number;
};

import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring
} from 'react-native-reanimated';

const { width, height } = Dimensions.get('window');

// Sample network data - in a real app, this would come from an API
const networkNodes: NetworkNode[] = [
  { id: 'user', name: 'Alex', type: 'self' as 'self', x: width / 2, y: height / 2.5, connections: ['1', '2', '3', '4', '5'] },
  { id: '1', name: 'Sarah Chen', type: 'contact' as 'contact', industry: 'Technology', x: width / 2 - 90, y: height / 2.5 - 150, connections: ['user', '2', '4'] },
  { id: '2', name: 'Michael Rodriguez', type: 'contact' as 'contact', industry: 'Sales', x: width / 2 + 110, y: height / 2.5 - 120, connections: ['user', '1'] },
  { id: '3', name: 'Aisha Johnson', type: 'contact' as 'contact', industry: 'Finance', x: width / 2 + 160, y: height / 2.5 + 50, connections: ['user', '5'] },
  { id: '4', name: 'David Lee', type: 'contact' as 'contact', industry: 'Technology', x: width / 2 - 130, y: height / 2.5 + 10, connections: ['user', '1'] },
  { id: '5', name: 'Emma Wilson', type: 'contact' as 'contact', industry: 'Marketing', x: width / 2 - 30, y: height / 2.5 + 140, connections: ['user', '3'] },
];

// Different colors for different industries
const industryColors: Record<IndustryKey, string> = {
  'Technology': '#3498db',
  'Sales': '#2ecc71',
  'Finance': '#9b59b6',
  'Marketing': '#e67e22',
  'self': '#e74c3c',
};

const NetworkVisualScreen = ({ route }: NetworkVisualScreenProps) => {
  type NetworkVisualNavigationProp = StackNavigationProp<RootStackParamList, 'NetworkVisual'>;
  const navigation = useNavigation<NetworkVisualNavigationProp>();
  const toast = useToast(); // Added
  const [selectedNode, setSelectedNode] = useState<NetworkNode | null>(null);
  const [filter, setFilter] = useState('All');
  const [scale, setScale] = useState(1);
  const [isInfoModalVisible, setInfoModalVisible] = useState(false);
  
  // Check if we need to focus on a specific contact
  useEffect(() => {
    if (route.params?.focusedContactId) {
      const focusedNode = networkNodes.find(node => node.id === route.params.focusedContactId);
      if (focusedNode) {
        setSelectedNode(focusedNode);
        // If the node has an industry, filter to show only that industry
        if (focusedNode.industry) {
          setFilter(focusedNode.industry);
        }
      }
    }
  }, [route.params]);
  
  // Animated values for pan gesture
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  
  // Pan gesture handler
  const panGestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent, GestureContext>({
    onStart: (_, ctx) => {
      ctx.startX = translateX.value;
      ctx.startY = translateY.value;
    },
    onActive: (event, ctx) => {
      translateX.value = ctx.startX + event.translationX;
      translateY.value = ctx.startY + event.translationY;
    },
    onEnd: () => {
      // Optional: Add spring animation when releasing the drag
    },
  });
  
  // Animated styles for the graph container
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale }
      ],
    };
  });
  
  // Filter nodes based on selected filter
  const filteredNodes = filter === 'All' 
    ? networkNodes 
    : networkNodes.filter(node => node.industry === filter || node.type === 'self');
  
  // Calculate which connections should be shown based on filtered nodes
  const visibleConnections: ConnectionLink[] = [];
  filteredNodes.forEach(node => {
    if (node.connections) {
      node.connections.forEach(connId => {
        const connExists = filteredNodes.some(n => n.id === connId);
        if (connExists && node.id < connId) { // Avoid duplicate connections
          visibleConnections.push({ from: node.id, to: connId });
        }
      });
    }
  });

  // Handle node selection
  const handleNodePress = (node: NetworkNode) => {
    setSelectedNode(node);
    setInfoModalVisible(true);
  };

  // Handle view contact detail
  const handleViewContactDetail = (contactId: string) => {
    if (contactId === 'user') {
      toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={`toast-${id}`} action="info" variant="accent">
            <ToastTitle>Profile</ToastTitle>
            <ToastDescription>Viewing your own profile.</ToastDescription>
          </Toast>
        )
      });
      return;
    }
    navigation.navigate('ContactDetail', { contactId });
  };

  // Handle filter change
  const handleFilterChange = (filterOption: string) => {
    setFilter(filterOption);
    // Clear selected node when changing filters
    setSelectedNode(null);
  };

  // Handle add connection
  const handleAddConnection = () => {
    toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={`toast-${id}`} action="info" variant="accent">
            <ToastTitle>Add Connection</ToastTitle>
            <ToastDescription>Connection creation form will appear here.</ToastDescription>
          </Toast>
        )
      });
    // In a real app, this would open a form to create a new connection
  };

  // Handle customize view
  const handleCustomizeView = () => {
    toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={`toast-${id}`} action="info" variant="accent">
            <ToastTitle>Customize View</ToastTitle>
            <ToastDescription>Options to customize the network graph visualization will appear here.</ToastDescription>
          </Toast>
        )
      });
    // In a real app, this would open a modal with customization options
  };

  // Handle export or share
  const handleExportOrShare = () => {
    toast.show({
        placement: "top",
        render: ({ id }) => (
          <Toast nativeID={`toast-${id}`} action="info" variant="accent">
            <ToastTitle>Export/Share</ToastTitle>
            <ToastDescription>Options to export or share the network visualization will appear here.</ToastDescription>
          </Toast>
        )
      });
    // In a real app, this would open a modal with export/share options
  };

  // Zoom controls
  const handleZoomIn = () => {
    if (scale < 2) {
      setScale(prev => prev + 0.2);
    }
  };
  
  const handleZoomOut = () => {
    if (scale > 0.5) {
      setScale(prev => prev - 0.2);
    }
  };
  
  // Reset position and zoom
  const handleReset = () => {
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    setScale(1);
  };

  // Contact Info Modal
  const renderContactModal = () => {
    if (!selectedNode) return null;
    
    const nodeColor = selectedNode.type === 'self'
      ? industryColors.self
      : (selectedNode.industry && industryColors[selectedNode.industry]) || '#999';
    
    return (
      <Modal
        visible={isInfoModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setInfoModalVisible(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setInfoModalVisible(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <View style={[styles.modalAvatar, { backgroundColor: nodeColor }]}>
                <Text style={styles.modalAvatarText}>
                  {selectedNode.name.charAt(0)}
                </Text>
              </View>
              <Text style={styles.modalName}>{selectedNode.name}</Text>
              {selectedNode.type !== 'self' && (
                <Text style={styles.modalIndustry}>{selectedNode.industry}</Text>
              )}
            </View>
            
            <Text style={styles.modalInfoHeader}>Network Information</Text>
            <View style={styles.modalConnectionInfo}>
              <View style={styles.modalInfoItem}>
                <Text style={styles.modalInfoValue}>
                  {selectedNode.connections ? selectedNode.connections.length : 0}
                </Text>
                <Text style={styles.modalInfoLabel}>Connections</Text>
              </View>
              <View style={styles.divider} />
              <View style={styles.modalInfoItem}>
                <Text style={styles.modalInfoValue}>
                  {selectedNode.type === 'self' ? 'You' : selectedNode.id === '1' ? '1st' : '2nd'}
                </Text>
                <Text style={styles.modalInfoLabel}>Degree</Text>
              </View>
            </View>
            
            <View style={styles.modalActions}>
              {selectedNode.type !== 'self' && (
                <>
                  <TouchableOpacity 
                    style={[styles.modalButton, { backgroundColor: '#f0f0f0' }]}
                    onPress={() => {
                      setInfoModalVisible(false);
                      handleViewContactDetail(selectedNode.id);
                    }}
                  >
                    <Text style={styles.modalButtonText}>View Profile</Text>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={[styles.modalButton, { backgroundColor: nodeColor }]}
                    onPress={() => {
                      setInfoModalVisible(false);
                      toast.show({
                        placement: "top",
                        render: ({ id }) => (
                          <Toast nativeID={id} action="info" variant="accent">
                            <ToastTitle>Connect</ToastTitle>
                            <ToastDescription>
                              Messaging {selectedNode.name}.
                            </ToastDescription>
                          </Toast>
                        ),
                      });
                    }}
                  >
                    <Text style={[styles.modalButtonText, { color: 'white' }]}>Message</Text>
                  </TouchableOpacity>
                </>
              )}
              {selectedNode.type === 'self' && (
                <TouchableOpacity 
                  style={[styles.modalButton, { backgroundColor: nodeColor }]}
                  onPress={() => {
                    setInfoModalVisible(false);
                    handleAddConnection();
                  }}
                >
                  <Text style={[styles.modalButtonText, { color: 'white' }]}>Add New Connection</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <ArrowLeft size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Your Network</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity 
            style={styles.headerAction}
            onPress={handleCustomizeView}
          >
            <SlidersHorizontal size={22} color="#333" />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.headerAction}
            onPress={handleExportOrShare}
          >
            <Share size={22} color="#333" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Filter Options */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filtersContainer}
      >
        {['All', 'Technology', 'Sales', 'Finance', 'Marketing'].map(filterOption => (
          <TouchableOpacity 
            key={filterOption}
            style={[
              styles.filterChip,
              filter === filterOption && { 
                backgroundColor: Object.prototype.hasOwnProperty.call(industryColors, filterOption)
                  ? industryColors[filterOption as IndustryKey]
                  : (filterOption === 'All' ? '#3498db' : undefined) 
              }
            ]}
            onPress={() => handleFilterChange(filterOption)}
          >
            <Text 
              style={[
                styles.filterText,
                filter === filterOption && { color: 'white' }
              ]}
            >
              {filterOption}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Action Button - Add Connection */}
      <TouchableOpacity 
        style={styles.fabButton}
        onPress={handleAddConnection}
      >
        <UserPlus size={24} color="white" />
      </TouchableOpacity>

      {/* Zoom Controls */}
      <View style={styles.zoomControls}>
        <TouchableOpacity style={styles.zoomButton} onPress={handleZoomIn}>
          <Plus size={20} color="#555" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.zoomButton} onPress={handleZoomOut}>
          <Minus size={20} color="#555" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.zoomButton} onPress={handleReset}>
          <RefreshCw size={20} color="#555" />
        </TouchableOpacity>
      </View>

      {/* Selected Node Info Card (when a node is selected without modal) */}
      {selectedNode && !isInfoModalVisible && (
        <TouchableOpacity 
          style={styles.nodeInfoCard}
          onPress={() => setInfoModalVisible(true)}
        >
          <View style={[styles.nodeAvatar, { 
            backgroundColor: selectedNode.type === 'self' 
              ? industryColors.self 
              : (selectedNode.industry && industryColors[selectedNode.industry]) || '#999'
          }]}>
            <Text style={styles.nodeAvatarText}>{selectedNode.name.charAt(0)}</Text>
          </View>
          <View style={styles.nodeInfo}>
            <Text style={styles.nodeName}>{selectedNode.name}</Text>
            {selectedNode.type !== 'self' && (
              <Text style={styles.nodeIndustry}>{selectedNode.industry}</Text>
            )}
          </View>
          <TouchableOpacity 
            style={styles.nodeAction}
            onPress={() => {
              if (selectedNode.type !== 'self') {
                handleViewContactDetail(selectedNode.id);
              }
            }}
          >
            {selectedNode.type !== 'self' ? (
              <ChevronRight size={22} color="#3498db" />
            ) : (
              <UserCircle2 size={22} color="#3498db" />
            )}
          </TouchableOpacity>
        </TouchableOpacity>
      )}

      {/* Network Graph */}
      <PanGestureHandler onGestureEvent={panGestureHandler}>
        <Animated.View style={[styles.graphContainer, animatedStyle]}>
          {/* Connection Lines */}
          {visibleConnections.map((conn, index) => {
            const fromNode = networkNodes.find(n => n.id === conn.from);
            const toNode = networkNodes.find(n => n.id === conn.to);
            
            if (!fromNode || !toNode) return null;
            
            // Calculate line coordinates
            const fromX = fromNode.x;
            const fromY = fromNode.y;
            const toX = toNode.x;
            const toY = toNode.y;
            
            // Determine style based on relationship
            const isUserConnection = conn.from === 'user' || conn.to === 'user';
            
            return (
              <View 
                key={`line-${index}`} 
                style={[
                  styles.connectionLine,
                  {
                    left: Math.min(fromX, toX),
                    top: Math.min(fromY, toY),
                    width: Math.abs(toX - fromX),
                    height: Math.abs(toY - fromY),
                    backgroundColor: 'transparent',
                  }
                ]}
              >
                <View
                  style={{
                    position: 'absolute',
                    height: 2,
                    width: Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toY - fromY, 2)),
                    backgroundColor: isUserConnection ? '#3498db' : '#aaa',
                    left: 0,
                    top: (Math.abs(toY - fromY)) / 2,
                    transformOrigin: 'left center',
                    transform: [
                      { translateX: 0 },
                      { translateY: 0 },
                      { rotateZ: `${Math.atan2(toY - fromY, toX - fromX)}rad` },
                    ],
                  }}
                />
              </View>
            );
          })}
          
          {/* Network Nodes */}
          {filteredNodes.map(node => {
            const isSelected = selectedNode && selectedNode.id === node.id;
            const isCurrentUser = node.type === 'self';
            const nodeColor = node.type === 'self'
      ? industryColors.self
      : (node.industry && industryColors[node.industry]) || '#CCCCCC';
            
            return (
              <TouchableOpacity
                key={node.id}
                style={[
                  styles.networkNode,
                  { 
                    left: node.x - 30, 
                    top: node.y - 30,
                    backgroundColor: nodeColor,
                    borderWidth: isSelected ? 3 : 0,
                    width: isCurrentUser ? 70 : 60,
                    height: isCurrentUser ? 70 : 60,
                  }
                ]}
                onPress={() => handleNodePress(node)}
              >
                <Text style={styles.nodeLabel}>{node.name.split(' ')[0]}</Text>
              </TouchableOpacity>
            );
          })}
        </Animated.View>
      </PanGestureHandler>
      
      {/* Contact Info Modal */}
      {renderContactModal()}
    </View>
  );
};

export default NetworkVisualScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingHorizontal: 16,
    paddingBottom: 10,
    backgroundColor: 'white',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerAction: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    ...(Platform.OS === 'web' ? 
      { boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.05)' } 
    : 
      { shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 1, }
    ),
  },
  filterChip: {
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: '#f0f0f0',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#555',
  },
  zoomControls: {
    position: 'absolute',
    right: 16,
    top: 120,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 4,
    ...(Platform.OS === 'web' ? 
      { boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.1)' } 
    : 
      { shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3, }
    ),
    zIndex: 10,
  },
  zoomButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  fabButton: {
    position: 'absolute',
    right: 16,
    bottom: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3498db',
    justifyContent: 'center',
    alignItems: 'center',
    ...(Platform.OS === 'web' ? 
      { boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.2)' } 
    : 
      { shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.2, shadowRadius: 4, elevation: 4, }
    ),
    zIndex: 10,
  },
  nodeInfoCard: {
    position: 'absolute',
    bottom: 24,
    left: 16,
    right: 88,
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 10,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    ...(Platform.OS === 'web' ? 
      { boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.1)' } 
    : 
      { shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3, }
    ),
    zIndex: 10,
  },
  nodeAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  nodeAvatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  nodeInfo: {
    flex: 1,
    marginLeft: 12,
  },
  nodeName: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
  },
  nodeIndustry: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
  nodeAction: {
    padding: 8,
  },
  graphContainer: {
    position: 'absolute',
    width: width * 2,
    height: height * 2,
    left: -width / 2,
    top: -height / 2,
  },
  connectionLine: {
    position: 'absolute',
    overflow: 'visible',
  },
  networkNode: {
    position: 'absolute',
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: 'white',
  },
  nodeLabel: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 13,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: width - 64,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalAvatarText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  modalName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 10,
  },
  modalIndustry: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  modalInfoHeader: {
    fontSize: 14,
    fontWeight: '500',
    color: '#777',
    marginBottom: 10,
  },
  modalConnectionInfo: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 12,
    marginBottom: 20,
  },
  modalInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  modalInfoValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalInfoLabel: {
    fontSize: 12,
    color: '#777',
    marginTop: 4,
  },
  divider: {
    width: 1,
    backgroundColor: '#ddd',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 6,
  },
  modalButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
});