import React, { useState, useMemo } from 'react';
import { FlatList } from 'react-native';
import { 
  Box, 
  VStack, 
  Heading, 
  Input, 
  InputField, 
  InputSlot, 
  InputIcon, 
  Spinner, 
  Text, 
  Button, 
  ButtonIcon, 
  HStack 
} from '@gluestack-ui/themed';
import { Search, Plus } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { CompositeNavigationProp } from '@react-navigation/native';

import { useContacts } from '../features/Contacts/hooks/useContacts';
import ContactListItem from '../features/Contacts/components/ContactListItem';
import { RootStackParamList, BottomTabParamList } from '../types';

// Define the navigation prop type for this screen
type ContactsScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<BottomTabParamList, 'ContactsTab'>,
  NativeStackNavigationProp<RootStackParamList>
>;

const ContactsScreen = () => {
  const navigation = useNavigation<ContactsScreenNavigationProp>();
  const { contacts, loading, error } = useContacts();
  const [searchQuery, setSearchQuery] = useState('');

  const filteredContacts = useMemo(() => {
    if (!searchQuery) {
      return contacts;
    }
    return contacts.filter(contact =>
      contact.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [contacts, searchQuery]);

  const handleAddContact = () => {
    navigation.navigate('ContactCreate', {});
  };

  if (loading) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center">
        <Spinner size="large" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center">
        <Text color="$error700">{error}</Text>
      </Box>
    );
  }

  return (
    <Box flex={1} bg="$white" sx={{ _dark: { bg: '$black' } }}>
      <VStack p="$4" space="md">
        <HStack justifyContent="space-between" alignItems="center">
          <Heading size="xl">Contacts</Heading>
          <Button size="sm" action="primary" onPress={handleAddContact}>
            <ButtonIcon as={Plus} />
          </Button>
        </HStack>
        <Input>
          <InputSlot pl="$3">
            <InputIcon as={Search} />
          </InputSlot>
          <InputField 
            placeholder="Search contacts..." 
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </Input>
      </VStack>
      <FlatList
        data={filteredContacts}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <ContactListItem
            contact={item}
            onPress={() => navigation.navigate('ContactDetail', { contactId: item.id })}
          />
        )}
        contentContainerStyle={{ paddingBottom: 20 }}
      />
    </Box>
  );
};

export default ContactsScreen;
