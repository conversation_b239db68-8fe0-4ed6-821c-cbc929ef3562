import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Box, Text, Icon, Pressable } from '@gluestack-ui/themed';
import { Home, Users, Share2, Bell, Plus } from 'lucide-react-native';

import HomeScreen from '../pages/HomeScreen';
import ContactsScreen from '../pages/ContactsScreen';
import GraphScreen from '../pages/GraphScreen';
import TasksScreen from '../pages/TasksScreen';
import { Platform } from 'react-native';
import { VStack } from '@gluestack-ui/themed';

// Placeholder component for tabs that only have a custom button and no screen content
const EmptyScreenPlaceholder = () => null;

const Tab = createBottomTabNavigator();

const BottomTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarShowLabel: false,
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopWidth: 0,
          height: 60,
          ...(Platform.OS === 'web' ? {
            boxShadow: '0 -3px 4px rgba(0, 0, 0, 0.1)', // Standard CSS box-shadow for web
          } : {
            elevation: 10, // Android shadow
            shadowColor: '#000', // iOS shadow
            shadowOffset: { width: 0, height: -3 }, // iOS shadow
            shadowOpacity: 0.1, // iOS shadow
            shadowRadius: 4, // iOS shadow
          }),
        },
      }}
    >
            <Tab.Screen 
        name="HomeTab" 
        component={HomeScreen} 
        options={{
          tabBarIcon: ({ focused }) => (
            <VStack alignItems="center" space="xs">
              <Icon as={Home} color={focused ? '$primary500' : '$trueGray400'} />
              <Text size="2xs" color={focused ? '$primary500' : '$trueGray400'}>首页</Text>
            </VStack>
          ),
        }}
      />
      <Tab.Screen 
        name="ContactsTab" 
        component={ContactsScreen} 
        options={{
          tabBarIcon: ({ focused }) => (
            <VStack alignItems="center" space="xs">
              <Icon as={Users} color={focused ? '$primary500' : '$trueGray400'} />
              <Text size="2xs" color={focused ? '$primary500' : '$trueGray400'}>联系人</Text>
            </VStack>
          ),
        }}
      />
      <Tab.Screen
        name="CreateTab"
        component={EmptyScreenPlaceholder} // Use a defined component to avoid inline function warning
        options={({ navigation }) => ({ // options can be a function to access navigation
          tabBarButton: () => (
            <Box mt={-20}>
              <Pressable 
                bg="$primary500" 
                rounded="$full" 
                w={56} h={56} 
                justifyContent="center" 
                alignItems="center"
                onPress={() => navigation.navigate('ContactCreate', {})} // Navigate to ContactCreateScreen
                accessibilityRole="button"
                accessibilityLabel="Create new contact or item"
              >
                <Icon as={Plus} color="$white" size="xl" />
              </Pressable>
            </Box>
          ),
        })}
      />
      <Tab.Screen 
        name="GraphTab" 
        component={GraphScreen} 
        options={{
          tabBarIcon: ({ focused }) => (
            <VStack alignItems="center" space="xs">
              <Icon as={Share2} color={focused ? '$primary500' : '$trueGray400'} />
              <Text size="2xs" color={focused ? '$primary500' : '$trueGray400'}>图谱</Text>
            </VStack>
          ),
        }}
      />
      <Tab.Screen 
        name="TasksTab" 
        component={TasksScreen} 
        options={{
          tabBarIcon: ({ focused }) => (
            <VStack alignItems="center" space="xs">
              <Icon as={Bell} color={focused ? '$primary500' : '$trueGray400'} />
              <Text size="2xs" color={focused ? '$primary500' : '$trueGray400'}>提醒</Text>
            </VStack>
          ),
        }}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigator;
