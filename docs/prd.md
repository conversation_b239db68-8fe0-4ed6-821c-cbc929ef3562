# RelationHub 产品需求文档 (PRD)

## 1. 引言

### 1.1 文档目的
本文档旨在详细定义 RelationHub 移动应用的产品需求，作为产品设计、开发、测试和迭代的依据。

### 1.2 产品概述
RelationHub 是一款专为商务人士设计的智能人际关系管理移动应用。它利用 AI 技术和先进的数据可视化功能，帮助用户高效地构建、维护、拓展和利用其商务人脉网络，从而提升个人和企业的商业价值。

### 1.3 产品愿景
成为商务人士首选的人脉管理与价值发现平台，通过智能化工具赋能用户，使其在复杂的商业环境中游刃有余。

### 1.4 设计哲学与UX原则

*   **用户中心 (User-Centricity):** 一切设计决策必须以用户的需求、目标和使用场景为首要考虑，致力于解决用户的真实痛点。
*   **简洁直观 (Simplicity & Clarity):** 追求界面清晰、信息结构合理、操作路径最短。即使用户首次接触，也能快速理解和上手复杂功能。避免信息过载和不必要的复杂性。
*   **高效流畅 (Efficiency & Flow):** 帮助用户快速、轻松地完成任务。优化核心操作流程，减少不必要的步骤和干扰。
*   **一致性 (Consistency):** 在整个应用中保持统一的视觉风格、交互模式和术语表达，降低用户的学习成本，建立稳定的用户预期。
*   **及时反馈与响应 (Feedback & Responsiveness):** 对用户的每一个操作都应有及时、明确的反馈。应用应表现出良好的响应速度，让用户感觉一切尽在掌控。
*   **美学愉悦与情感连接 (Aesthetic Appeal & Delight):** 打造视觉上精致、现代且符合品牌调性的用户界面。通过精心设计的微交互和动画，提升用户体验的愉悦感，建立情感连接。
*   **无障碍设计 (Accessibility - A11y):** 秉持包容性设计理念，确保不同能力的用户都能顺畅使用产品。遵循移动端无障碍设计规范（如WCAG AA标准），关注对比度、触摸目标大小、屏幕阅读器兼容性等。
*   **容错与引导 (Forgiveness & Error Prevention):** 通过优秀的设计预防用户出错。当错误无法避免时，提供清晰、友好的错误提示，并引导用户轻松修正。鼓励探索，允许用户安全地撤销操作。


## 2. 目标用户

主要面向以下用户群体：
*   **企业高管与决策者**：需要管理人脉资源以促进业务发展。
*   **销售与市场专业人士**：依赖广泛的人脉网络来拓展客户和市场。
*   **创业者与投资者**：积极寻找合作伙伴、投资机会和行业资源。
*   **咨询顾问与自由职业者**：项目驱动，需要高效管理人脉以获取新机会。
*   **所有希望通过有效管理人脉提升职业发展的商务人士**。

## 3. 核心功能

以下功能模块基于 `relationhub-features.md` 文档：

### 3.1 智能通讯录管理

#### 3.1.1 名片扫描识别
*   **OCR技术**：支持中英文名片自动识别。
*   **多格式支持**：兼容纸质名片、电子名片、微信名片。
*   **准确率**：目标识别准确率达95%以上。
*   **批量处理**：支持批量扫描和导入。

    **UX & Design Considerations:**
    *   **User Flow:** 从启动扫描到联系人信息预填的流程应极致顺畅。提供清晰的拍摄引导（如取景框、光线提示）。
    *   **Feedback:** 扫描过程中提供实时视觉反馈（如卡片边缘检测）。OCR处理时显示明确的进度指示。识别完成后，高亮显示已识别字段，并允许用户快速校对和修改。
    *   **Interaction:** 支持点击识别错误的字段直接编辑。对于多名片批量扫描，提供便捷的切换和管理界面。
    *   **Animation:** 扫描成功、字段自动填充等关键节点可使用微妙、积极的动画效果增强用户感知。
    *   **Empty/Error States:** 未检测到名片、识别率低等情况，提供友好提示和操作建议。

#### 3.1.2 多渠道信息整合
*   **社交媒体集成**：自动关联LinkedIn、微博等社交账号信息（用户授权后）。
*   **企业信息补全**：通过公开数据源自动获取公司官网、规模等企业信息。
*   **实时更新**：联系人职位变动等重要信息更新时（基于用户输入或第三方数据源）提醒用户。
*   **去重机制**：智能识别和合并重复联系人，提供合并建议供用户确认。

    **UX & Design Considerations:**
    *   **User Flow:** 社交账号关联流程需清晰授权指引。信息补全和更新应在后台静默完成，并通过非打扰方式通知用户（如在联系人详情页提示更新）。
    *   **Feedback:** 清晰展示已关联的社交账号。信息更新时，在联系人列表或详情页用小红点或标签提示。去重合并时，清晰展示待合并信息对比，允许用户选择保留哪些信息。
    *   **Interaction:** 用户可方便地管理已关联的社交账号。对于自动更新的信息，用户应有权接受或拒绝变更。

#### 3.1.3 云端同步备份
*   **多设备同步**：支持 iOS 和 Android 移动设备间的数据同步。
*   **增量备份**：仅同步变更数据，优化流量消耗和同步速度。
*   **版本管理**：支持关键数据的历史版本查看与回滚（具体范围待定）。
*   **离线访问**：核心联系人信息和近期互动支持离线查看。

    **UX & Design Considerations:**
    *   **Feedback:** 清晰展示当前的同步状态（如“正在同步...”、“上次同步时间：X分钟前”、“同步完成/失败”）。同步失败时提供一键重试功能和简要原因。
    *   **Interaction:** 用户可手动触发同步。版本管理（若实现）应提供清晰的版本列表和回滚操作确认。
    *   **Performance:** 增量备份和同步过程应在后台执行，不影响前台操作的流畅性。

### 3.2 联系人标签系统

#### 3.2.1 行业领域标签
*   **预设行业**：提供覆盖主流行业的分类标签。
*   **细分领域**：支持二级子分类标签。
*   **自定义行业**：允许用户创建和管理个性化行业标签。
*   **行业趋势分析**：可视化展示用户通讯录中各行业联系人的分布和增长趋势。

    **UX & Design Considerations (for 标签系统 as a whole):**
    *   **Interaction:** 标签的添加、删除、修改操作应便捷高效。在联系人列表或详情页，标签应清晰展示并易于管理。支持批量为联系人打标签。
    *   **Visuals:** 标签可使用不同颜色或图标辅助区分（用户可自定义或系统预设）。标签选择器应支持搜索和快速选择常用标签。
    *   **Feedback:** 操作成功后给予即时反馈。行业趋势分析图表应简洁易懂，支持交互探索。
    *   **Customization:** 用户应能方便地创建、编辑和组织自定义标签。

#### 3.2.2 合作关系标签
*   **关系类型**：如客户、潜在客户、供应商、合作伙伴、同事、导师等。
*   **合作状态**：如潜在、初步接触、洽谈中、合作中、已结束、已流失。
*   **重要程度**：用户可自定义（如星级）标记联系人重要性。
*   **项目关联**：标签可与具体项目或业务机会关联。

#### 3.2.3 个性化标签
*   **兴趣爱好**：记录联系人的个人兴趣点，便于建立情感连接。
*   **性格特点**：备注沟通偏好、性格特征等，辅助商务交往。
*   **专业技能**：标记联系人的核心专业领域和技能。
*   **社交风格**：记录其社交偏好和沟通习惯。

### 3.3 人际关系图谱

#### 3.3.1 可视化网络展示
*   **多层级展示**：支持展示直接联系人（1度）及其共同联系人（2度），未来可拓展至更高层级。
*   **交互式操作**：图谱节点支持点击查看详情、缩放、拖拽等交互。
*   **关系类型可视化**：不同颜色或样式的线条表示不同的关系类型（如同事、合作伙伴）。
*   **影响力分析**：节点大小或高亮程度可表示联系人的人脉影响力或互动频率（基于算法）。

    **UX & Design Considerations (for 人际关系图谱 as a whole):**
    *   **Onboarding & Education:** 首次使用时，提供简洁明了的引导教程，解释图谱的构成、交互方式及价值。避免信息过载。
    *   **Interaction:** 支持流畅的平移、缩放手势。节点点击后应有平滑的过渡动画展开详情或相关操作。提供筛选器（按标签、关系类型、行业等）和搜索功能，帮助用户快速定位和聚焦。
    *   **Visual Design:** 节点和边的视觉设计应清晰、美观，信息层级分明。不同类型的关系可用不同颜色或线条样式区分。考虑暗黑模式的适配。图谱整体应避免视觉混乱，保持良好的可读性。
    *   **Performance:** 保证图谱加载和交互的流畅性，即使在节点较多时。可采用懒加载、分级渲染等技术优化。加载过程中提供明确的进度指示。
    *   **Accessibility:** 确保图谱信息可以通过非视觉方式获取（如列表视图、节点详情的文本描述）。颜色选择需考虑色盲用户。
    *   **Contextual Actions:** 选中节点后，方便地执行相关操作（如查看联系人详情、发起沟通、添加笔记、标记关系等）。
    *   **Empty/Initial State:** 当图谱数据不足或无法生成时，提供友好的空状态提示和引导。

#### 3.3.2 关系强度分析
*   **互动频率**：基于用户记录的沟通频次（电话、会议、邮件等）计算关系强度。
*   **时间衰减**：长期无互动的关系强度会自然衰减，提醒用户维护。
*   **影响因子**：可综合考虑职位、行业、合作项目等因素调整关系强度权重。
*   **关系预警**：智能提醒用户维护重要但近期疏远的关系。

#### 3.3.3 智能推荐功能 (AI驱动)
*   **共同联系人发现**：基于通讯录数据，发现用户之间可能存在的共同联系人，辅助引荐。
*   **行业关联推荐**：推荐用户可能感兴趣的同行业或相关行业的人脉。
*   **项目匹配推荐**：根据用户当前关注的项目或业务需求，推荐可能提供帮助的人脉。
*   **活动推荐**：基于用户标签和行为，推荐相关的商务活动或会议。

### 3.4 商务场景记录

#### 3.4.1 会议记录关联
*   **会议信息**：记录会议时间、地点、主题、议程。
*   **参会人员**：自动关联通讯录中的联系人作为参会者。
*   **要点摘要**：支持文本、语音（转文字）记录会议关键讨论内容和结论。
*   **后续任务**：为会议创建待办事项，并关联负责人和截止日期。

    **UX & Design Considerations (for 商务场景记录 as a whole):**
    *   **Interaction:** 记录创建表单应简洁高效，支持快速填写和关联。例如，会议记录可快速选择参会联系人，自动填充已知信息。
    *   **Visuals:** 交流历史、会议列表等应以清晰的时间轴或卡片列表形式展示，易于回顾和查找。重要事件可突出显示。
    *   **Feedback:** 保存记录、创建任务等操作后有明确的成功提示。
    *   **Integration:** 若能与日历、任务管理工具集成，将极大提升用户体验。

#### 3.4.2 交流历史追踪
*   **沟通记录**：手动或半自动（如邮件插件集成）记录电话、邮件、面谈等沟通历史。
*   **内容分类**：用户可对沟通记录进行分类，如商务洽谈、日常问候、问题解决等。
*   **情感分析 (AI驱动)**：对用户记录的沟通文本进行情感倾向分析（积极、消极、中性）。
*   **效果评估**：用户可对单次沟通效果进行主观评价或备注。

#### 3.4.3 重要事件管理
*   **里程碑事件**：记录合作项目中的重要节点、合同签订、关键决策等。
*   **个人事件**：记录联系人的生日、升职、获奖、家庭重要事件等。
*   **项目进展**：追踪与联系人相关的合作项目的进展状态。
*   **纪念日提醒**：如初识纪念日、合作周年纪念日等，自动提醒。

### 3.5 智能提醒系统 (AI驱动部分)

#### 3.5.1 时间类提醒
*   **生日提醒**：支持自定义提前提醒时间（如提前1周、3天、当天）。
*   **纪念日提醒**：自动提醒重要的合作纪念日、初次见面纪念日等。
*   **约会提醒**：与日历集成或手动创建的会议、餐饮等约会提醒。
*   **任务截止提醒**：关联到联系人或会议的待办任务截止日期提醒。

#### 3.5.2 关系维护提醒
*   **定期联系提醒**：用户可基于关系重要性设定联系频率（如重要客户每月联系一次）。
*   **疏远预警**：AI分析互动数据，对长期无联系的重要人脉进行预警。
*   **生日祝福建议 (AI驱动)**：根据联系人标签和过往互动，AI辅助生成个性化生日祝福语草稿。
*   **节日问候建议 (AI驱动)**：重要节日提供批量问候建议或模板。

#### 3.5.3 商务机会提醒 (AI驱动)
*   **行业动态推送**：基于用户关注的行业标签，推送相关的行业新闻和潜在机会点。
*   **人事变动关注**：提醒用户关注其联系网络中重要人事变动信息（需数据源支持或用户手动记录）。
*   **潜在合作机会匹配**：AI分析用户需求和人脉资源，发现可能的合作机会并提醒。
*   **相关活动推荐**：基于用户画像和行为，推荐相关的商务活动、线上研讨会等。

    **UX & Design Considerations (for 智能提醒系统 as a whole):**
    *   **Notification Management:** 用户可以精细化控制接收哪些类型的提醒，以及提醒的方式（应用内、推送、铃声等）。避免过度打扰。
    *   **Actionable Notifications:** 推送通知应尽可能包含快捷操作按钮（如“发送祝福”、“查看详情”、“推迟提醒”）。
    *   **Personalization & Relevance:** AI生成的建议（如祝福语、维护提醒）应高度个性化，并允许用户编辑和反馈，以持续优化推荐质量。
    *   **Clarity & Transparency:** 清晰说明提醒的原因（如“您与XX已X天未联系”）。AI建议应明确标注为“建议”。
    *   **Visual Cues:** 应用内提醒可使用小红点、徽章或专门的提醒中心进行展示。

## 4. 技术栈与实现方案

*   **前端框架**：React Native
*   **UI 组件库**：NativeBase (v3) - 用于构建美观、一致的用户界面。
        *   **Project-Specific Style Guide:** 基于 NativeBase (v3) 的能力，应创建并遵循一份项目专属的UI样式与交互指南。该指南将确保应用整体视觉风格的统一性和品牌形象的专业性，同时提升开发效率。内容应至少包括：
            *   **核心色板 (Color Palette):** 定义主色、辅色、强调色、中性色（灰阶）、以及状态色（成功、警告、错误、信息）。确保颜色选择符合品牌定位，并满足WCAG对比度要求。
            *   **字体规范 (Typography):** 规定应用内各级标题、正文、辅助文字等的字体家族、字号、字重、行高及颜色。优先考虑系统字体以获得最佳性能和平台一致性。
            *   **间距与布局 (Spacing & Layout):** 建立统一的间距规范（如基于4dp或8dp的网格系统），用于元素内外边距、组件间距等。定义通用的布局模式和断点规则。
            *   **图标系统 (Iconography):** 选用一套风格统一、表意清晰的图标库（可来自NativeBase或第三方）。规定图标尺寸、颜色和使用场景。
            *   **组件状态 (Component States):** 详细定义常用交互组件（按钮、输入框、列表项、卡片等）在不同状态下（如默认、悬停/触摸反馈、激活、禁用、加载中、错误等）的视觉表现。
            *   **阴影与层级 (Shadows & Elevation):** 规范阴影的使用，以创建视觉层级和区分交互元素。
            *   **圆角规范 (Border Radius):** 统一卡片、按钮、输入框等元素的圆角大小，形成一致的视觉风格。
            *   **动效原则 (Animation Principles):** 定义应用内动画和过渡效果的基本原则，如时长、缓动函数等，确保动效自然、流畅且有意义，不干扰用户操作。
*   **开发与调试**：Expo - 加速开发流程，简化构建和部署，提供良好的开发体验。
*   **数据存储与同步**：
    *   **初期**：使用 Mock 数据进行页面开发和功能演示，确保数据结构的合理性。
    *   **后期**：迁移至 Supabase (PostgreSQL + Realtime + Auth) 或自建服务端 API (Node.js/Python/Go等) 进行持久化存储和云同步。
*   **AI 能力集成**：通过 OpenRouter API 统一调用各类大语言模型，用于实现如智能推荐、情感分析、祝福语生成等功能。API Key 管理需遵循安全最佳实践。
