/**
 * 本地通知服务
 * 处理推送通知的调度、发送和管理
 */

import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { Reminder, ReminderNotification } from '../types';

// 配置通知处理器
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export class NotificationService {
  private isInitialized = false;

  /**
   * 初始化通知服务
   */
  async initialize(): Promise<boolean> {
    try {
      // 请求通知权限
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('通知权限未授予');
        return false;
      }

      // 配置通知渠道（Android）
      if (Platform.OS === 'android') {
        await this.setupAndroidChannels();
      }

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('初始化通知服务失败:', error);
      return false;
    }
  }

  /**
   * 设置Android通知渠道
   */
  private async setupAndroidChannels() {
    await Notifications.setNotificationChannelAsync('reminders', {
      name: '提醒通知',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#4A78D9',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync('birthdays', {
      name: '生日提醒',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF6B6B',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync('follow_ups', {
      name: '联系提醒',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#4ECDC4',
      sound: 'default',
    });
  }

  /**
   * 调度提醒通知
   */
  async scheduleReminderNotification(reminder: Reminder): Promise<string | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: reminder.title,
          body: reminder.description || '',
          data: {
            reminderId: reminder.id,
            contactId: reminder.contactId,
            type: reminder.type,
            actions: reminder.suggestedActions
          },
          categoryIdentifier: this.getCategoryIdentifier(reminder.type),
          sound: 'default',
        },
        trigger: {
          date: reminder.triggerDate,
        },
      });

      console.log(`已调度通知: ${notificationId} for reminder: ${reminder.id}`);
      return notificationId;
    } catch (error) {
      console.error('调度通知失败:', error);
      return null;
    }
  }

  /**
   * 批量调度提醒通知
   */
  async scheduleMultipleReminders(reminders: Reminder[]): Promise<Map<string, string>> {
    const results = new Map<string, string>();

    for (const reminder of reminders) {
      const notificationId = await this.scheduleReminderNotification(reminder);
      if (notificationId) {
        results.set(reminder.id, notificationId);
      }
    }

    return results;
  }

  /**
   * 取消通知
   */
  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      console.log(`已取消通知: ${notificationId}`);
    } catch (error) {
      console.error('取消通知失败:', error);
    }
  }

  /**
   * 取消多个通知
   */
  async cancelMultipleNotifications(notificationIds: string[]): Promise<void> {
    for (const id of notificationIds) {
      await this.cancelNotification(id);
    }
  }

  /**
   * 取消所有通知
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('已取消所有通知');
    } catch (error) {
      console.error('取消所有通知失败:', error);
    }
  }

  /**
   * 获取已调度的通知
   */
  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('获取已调度通知失败:', error);
      return [];
    }
  }

  /**
   * 立即发送通知
   */
  async sendImmediateNotification(
    title: string, 
    body: string, 
    data?: Record<string, any>
  ): Promise<string | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
          sound: 'default',
        },
        trigger: null, // 立即发送
      });

      return notificationId;
    } catch (error) {
      console.error('发送即时通知失败:', error);
      return null;
    }
  }

  /**
   * 获取通知分类标识符
   */
  private getCategoryIdentifier(reminderType: string): string {
    switch (reminderType) {
      case 'birthday':
        return 'birthdays';
      case 'follow_up':
      case 'relationship_maintenance':
        return 'follow_ups';
      default:
        return 'reminders';
    }
  }

  /**
   * 设置应用图标徽章数字
   */
  async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('设置徽章数字失败:', error);
    }
  }

  /**
   * 获取当前徽章数字
   */
  async getBadgeCount(): Promise<number> {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error('获取徽章数字失败:', error);
      return 0;
    }
  }

  /**
   * 清除徽章数字
   */
  async clearBadge(): Promise<void> {
    await this.setBadgeCount(0);
  }

  /**
   * 检查通知权限状态
   */
  async getPermissionStatus(): Promise<'granted' | 'denied' | 'undetermined'> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return status;
    } catch (error) {
      console.error('检查通知权限失败:', error);
      return 'denied';
    }
  }

  /**
   * 请求通知权限
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('请求通知权限失败:', error);
      return false;
    }
  }

  /**
   * 添加通知接收监听器
   */
  addNotificationReceivedListener(
    listener: (notification: Notifications.Notification) => void
  ): Notifications.Subscription {
    return Notifications.addNotificationReceivedListener(listener);
  }

  /**
   * 添加通知响应监听器
   */
  addNotificationResponseReceivedListener(
    listener: (response: Notifications.NotificationResponse) => void
  ): Notifications.Subscription {
    return Notifications.addNotificationResponseReceivedListener(listener);
  }

  /**
   * 移除监听器
   */
  removeNotificationSubscription(subscription: Notifications.Subscription): void {
    subscription.remove();
  }

  /**
   * 生成智能通知内容
   */
  generateSmartNotificationContent(reminder: Reminder): {
    title: string;
    body: string;
  } {
    const { type, contactId, metadata } = reminder;
    
    let title = reminder.title;
    let body = reminder.description || '';

    // 根据提醒类型生成个性化内容
    switch (type) {
      case 'birthday':
        const advanceDays = metadata?.advanceDays || 0;
        if (advanceDays === 0) {
          title = `🎂 生日快乐！`;
          body = `今天是${this.getContactName(contactId)}的生日，记得送上祝福哦！`;
        } else {
          title = `🎂 生日提醒`;
          body = `${this.getContactName(contactId)}的生日还有${advanceDays}天，提前准备礼物吧！`;
        }
        break;

      case 'follow_up':
        const lastContactDays = metadata?.lastContactDays || 0;
        title = `💬 该联系了`;
        body = `已经${lastContactDays}天没有联系${this.getContactName(contactId)}了，保持联系很重要哦！`;
        break;

      case 'meeting':
        title = `📅 会议提醒`;
        body = `即将开始与${this.getContactName(contactId)}的会议`;
        break;

      case 'anniversary':
        title = `🎉 纪念日`;
        body = `今天是与${this.getContactName(contactId)}的特殊纪念日`;
        break;
    }

    return { title, body };
  }

  /**
   * 获取联系人姓名（简化版，实际应该从store获取）
   */
  private getContactName(contactId?: string): string {
    // TODO: 从联系人store获取真实姓名
    return contactId ? '联系人' : '某位联系人';
  }

  /**
   * 创建通知操作按钮
   */
  async setupNotificationCategories(): Promise<void> {
    try {
      await Notifications.setNotificationCategoryAsync('reminder_actions', [
        {
          identifier: 'complete',
          buttonTitle: '完成',
          options: {
            opensAppToForeground: false,
          },
        },
        {
          identifier: 'snooze',
          buttonTitle: '稍后提醒',
          options: {
            opensAppToForeground: false,
          },
        },
        {
          identifier: 'view',
          buttonTitle: '查看',
          options: {
            opensAppToForeground: true,
          },
        },
      ]);

      await Notifications.setNotificationCategoryAsync('birthday_actions', [
        {
          identifier: 'call',
          buttonTitle: '打电话',
          options: {
            opensAppToForeground: true,
          },
        },
        {
          identifier: 'message',
          buttonTitle: '发消息',
          options: {
            opensAppToForeground: true,
          },
        },
        {
          identifier: 'dismiss',
          buttonTitle: '忽略',
          options: {
            opensAppToForeground: false,
          },
        },
      ]);
    } catch (error) {
      console.error('设置通知分类失败:', error);
    }
  }
}

// 导出单例实例
export const notificationService = new NotificationService();
export default notificationService;
