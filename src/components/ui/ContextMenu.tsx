/**
 * 长按上下文菜单组件
 * 支持长按显示操作菜单
 */

import React, { useState, useRef } from 'react';
import {
  Pressable,
  Modal,
  ModalBackdrop,
  ModalContent,
  Box,
  VStack,
  HStack,
  Text,
  Divider,
} from '@gluestack-ui/themed';
import * as Haptics from 'expo-haptics';
import {
  Edit,
  Trash2,
  Copy,
  Share,
  Star,
  StarOff,
  Archive,
  Phone,
  MessageCircle,
  Calendar,
  Eye
} from 'lucide-react-native';
import { StandardIcon } from './StandardIcon';

export interface ContextMenuItem {
  id: string;
  title: string;
  icon?: any;
  color?: string;
  destructive?: boolean;
  disabled?: boolean;
  onPress: () => void;
}

export interface ContextMenuSection {
  id: string;
  items: ContextMenuItem[];
}

interface ContextMenuProps {
  children: React.ReactNode;
  items?: ContextMenuItem[];
  sections?: ContextMenuSection[];
  disabled?: boolean;
  hapticFeedback?: boolean;
  longPressDelay?: number;
}

export const ContextMenu: React.FC<ContextMenuProps> = ({
  children,
  items = [],
  sections = [],
  disabled = false,
  hapticFeedback = true,
  longPressDelay = 500,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const pressableRef = useRef<any>(null);

  const handleLongPress = () => {
    if (disabled) return;

    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    // 获取按钮位置
    if (pressableRef.current) {
      pressableRef.current.measure((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
        setMenuPosition({
          x: pageX + width / 2,
          y: pageY + height,
        });
        setIsVisible(true);
      });
    } else {
      setIsVisible(true);
    }
  };

  const handleItemPress = (item: ContextMenuItem) => {
    if (item.disabled) return;
    
    setIsVisible(false);
    
    // 延迟执行，确保模态框关闭动画完成
    setTimeout(() => {
      item.onPress();
    }, 100);
  };

  const handleClose = () => {
    setIsVisible(false);
  };

  // 合并items和sections
  const allSections: ContextMenuSection[] = [
    ...(items.length > 0 ? [{ id: 'default', items }] : []),
    ...sections,
  ];

  const renderMenuItem = (item: ContextMenuItem) => (
    <Pressable
      key={item.id}
      onPress={() => handleItemPress(item)}
      disabled={item.disabled}
      p="$3"
      borderRadius="$md"
      sx={{
        _pressed: {
          bg: '$backgroundLight100',
          _dark: { bg: '$backgroundDark800' }
        }
      }}
      opacity={item.disabled ? 0.5 : 1}
    >
      <HStack space="md" alignItems="center">
        {item.icon && (
          <StandardIcon
            as={item.icon}
            size="md"
            color={item.destructive ? '$error600' : item.color || '$textLight700'}
          />
        )}
        <Text
          flex={1}
          color={item.destructive ? '$error600' : item.color || '$textLight900'}
          sx={{
            _dark: {
              color: item.destructive ? '$error400' : item.color || '$textDark100'
            }
          }}
          fontWeight="$medium"
        >
          {item.title}
        </Text>
      </HStack>
    </Pressable>
  );

  const renderSection = (section: ContextMenuSection, index: number) => (
    <VStack key={section.id} space="xs">
      {index > 0 && <Divider />}
      {section.items.map(renderMenuItem)}
    </VStack>
  );

  return (
    <>
      <Pressable
        ref={pressableRef}
        onLongPress={handleLongPress}
        delayLongPress={longPressDelay}
        disabled={disabled}
      >
        {children}
      </Pressable>

      <Modal isOpen={isVisible} onClose={handleClose}>
        <ModalBackdrop onPress={handleClose} />
        <ModalContent
          maxWidth="$72"
          position="absolute"
          top={menuPosition.y}
          left={Math.max(16, Math.min(menuPosition.x - 144, 400 - 288 - 16))} // 确保不超出屏幕
          bg="$backgroundLight0"
          borderRadius="$xl"
          borderWidth="$1"
          borderColor="$borderLight200"
          sx={{
            _dark: {
              bg: '$backgroundDark900',
              borderColor: '$borderDark700'
            }
          }}
          shadowColor="$shadowColor"
          shadowOffset={{ width: 0, height: 4 }}
          shadowOpacity={0.1}
          shadowRadius={12}
          elevation={8}
        >
          <Box p="$2">
            <VStack space="xs">
              {allSections.map(renderSection)}
            </VStack>
          </Box>
        </ModalContent>
      </Modal>
    </>
  );
};

// 预定义的常用菜单项
export const ContextMenuItems = {
  edit: (onPress: () => void): ContextMenuItem => ({
    id: 'edit',
    title: '编辑',
    icon: Edit,
    onPress,
  }),

  delete: (onPress: () => void): ContextMenuItem => ({
    id: 'delete',
    title: '删除',
    icon: Trash2,
    destructive: true,
    onPress,
  }),

  duplicate: (onPress: () => void): ContextMenuItem => ({
    id: 'duplicate',
    title: '复制',
    icon: Copy,
    onPress,
  }),

  share: (onPress: () => void): ContextMenuItem => ({
    id: 'share',
    title: '分享',
    icon: Share,
    onPress,
  }),

  favorite: (onPress: () => void, isFavorite: boolean = false): ContextMenuItem => ({
    id: 'favorite',
    title: isFavorite ? '取消收藏' : '添加收藏',
    icon: isFavorite ? StarOff : Star,
    color: isFavorite ? '$warning600' : '$warning500',
    onPress,
  }),

  archive: (onPress: () => void): ContextMenuItem => ({
    id: 'archive',
    title: '归档',
    icon: Archive,
    onPress,
  }),

  call: (onPress: () => void): ContextMenuItem => ({
    id: 'call',
    title: '拨打电话',
    icon: Phone,
    color: '$success600',
    onPress,
  }),

  message: (onPress: () => void): ContextMenuItem => ({
    id: 'message',
    title: '发送消息',
    icon: MessageCircle,
    color: '$info600',
    onPress,
  }),

  createEvent: (onPress: () => void): ContextMenuItem => ({
    id: 'createEvent',
    title: '创建活动',
    icon: Calendar,
    color: '$primary600',
    onPress,
  }),

  viewDetails: (onPress: () => void): ContextMenuItem => ({
    id: 'viewDetails',
    title: '查看详情',
    icon: Eye,
    onPress,
  }),
};

export default ContextMenu;
