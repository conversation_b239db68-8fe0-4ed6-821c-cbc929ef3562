/**
 * 提醒规则引擎
 * 负责计算、生成和管理智能提醒
 */

import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>minderR<PERSON>, 
  ReminderType, 
  ReminderStatus, 
  ReminderPriority,
  SmartReminderSuggestion,
  Contact 
} from '../types';

export class ReminderEngine {
  /**
   * 根据联系人生成生日提醒
   */
  generateBirthdayReminders(contacts: Contact[]): <PERSON>minder[] {
    const reminders: <PERSON>minder[] = [];
    const today = new Date();
    const currentYear = today.getFullYear();

    contacts.forEach(contact => {
      // 检查生日字段
      const birthday = contact.birthday || 
        contact.importantDates?.find(date => date.type === 'birthday')?.date;

      if (!birthday) return;

      const birthdayDate = new Date(birthday);
      const thisYearBirthday = new Date(currentYear, birthdayDate.getMonth(), birthdayDate.getDate());
      
      // 如果今年生日已过，计算明年的
      if (thisYearBirthday < today) {
        thisYearBirthday.setFullYear(currentYear + 1);
      }

      // 生成提醒（提前3天、1天、当天）
      const advanceNotices = [3, 1, 0];
      
      advanceNotices.forEach(days => {
        const reminderDate = new Date(thisYearBirthday);
        reminderDate.setDate(reminderDate.getDate() - days);

        // 只生成未来的提醒
        if (reminderDate >= today) {
          const reminder: Reminder = {
            id: `birthday_${contact.id}_${days}d`,
            type: 'birthday',
            title: days === 0 
              ? `🎂 ${contact.name}的生日`
              : `🎂 ${contact.name}的生日 (${days}天后)`,
            description: days === 0 
              ? `今天是${contact.name}的生日，别忘了送上祝福！`
              : `${contact.name}的生日即将到来，提前准备祝福吧！`,
            contactId: contact.id,
            triggerDate: reminderDate,
            status: 'pending',
            priority: 'medium',
            suggestedActions: [
              {
                id: 'call',
                type: 'call',
                label: '打电话祝福',
                data: { phone: contact.phone }
              },
              {
                id: 'message',
                type: 'message',
                label: '发送祝福消息',
                data: { phone: contact.phone }
              }
            ],
            metadata: {
              autoGenerated: true,
              birthdayDate: thisYearBirthday.toISOString(),
              advanceDays: days
            },
            createdAt: new Date(),
            updatedAt: new Date()
          };

          reminders.push(reminder);
        }
      });
    });

    return reminders;
  }

  /**
   * 生成定期联系提醒
   */
  generateFollowUpReminders(contacts: Contact[]): Reminder[] {
    const reminders: Reminder[] = [];
    const today = new Date();

    contacts.forEach(contact => {
      const suggestion = this.analyzeContactFrequency(contact);
      
      if (suggestion.shouldRemind) {
        const reminder: Reminder = {
          id: `followup_${contact.id}_${Date.now()}`,
          type: 'follow_up',
          title: `💬 该联系${contact.name}了`,
          description: suggestion.reason,
          contactId: contact.id,
          triggerDate: suggestion.suggestedDate,
          status: 'pending',
          priority: this.calculatePriority(contact, suggestion.urgency),
          suggestedActions: this.generateContactActions(contact),
          metadata: {
            autoGenerated: true,
            lastContactDays: suggestion.daysSinceLastContact,
            relationshipStrength: contact.connectionStrength || 0,
            contactFrequency: contact.contactFrequency
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };

        reminders.push(reminder);
      }
    });

    return reminders;
  }

  /**
   * 分析联系人的联系频率
   */
  private analyzeContactFrequency(contact: Contact): {
    shouldRemind: boolean;
    reason: string;
    suggestedDate: Date;
    daysSinceLastContact: number;
    urgency: number;
  } {
    const today = new Date();
    const lastContactDate = contact.lastContactDate ? new Date(contact.lastContactDate) : null;
    const daysSinceLastContact = lastContactDate 
      ? Math.floor((today.getTime() - lastContactDate.getTime()) / (1000 * 60 * 60 * 24))
      : 365; // 如果没有记录，假设很久没联系

    // 根据关系类型和重要程度确定理想联系频率
    const idealFrequency = this.getIdealContactFrequency(contact);
    const urgency = Math.min(daysSinceLastContact / idealFrequency, 2); // 最大紧急度为2

    const shouldRemind = daysSinceLastContact >= idealFrequency;
    
    let reason = '';
    if (daysSinceLastContact >= idealFrequency * 2) {
      reason = `已经${daysSinceLastContact}天没有联系了，关系可能疏远`;
    } else if (daysSinceLastContact >= idealFrequency) {
      reason = `根据你们的关系，建议定期保持联系`;
    }

    // 建议在1-3天内联系
    const suggestedDate = new Date();
    suggestedDate.setDate(suggestedDate.getDate() + Math.min(3, Math.max(1, Math.floor(urgency))));

    return {
      shouldRemind,
      reason,
      suggestedDate,
      daysSinceLastContact,
      urgency
    };
  }

  /**
   * 获取理想联系频率（天数）
   */
  private getIdealContactFrequency(contact: Contact): number {
    // 基础频率根据关系类型
    let baseFrequency = 90; // 默认3个月

    switch (contact.relationshipType) {
      case 'family':
        baseFrequency = 7; // 1周
        break;
      case 'friend':
        baseFrequency = 14; // 2周
        break;
      case 'colleague':
        baseFrequency = 30; // 1个月
        break;
      case 'client':
        baseFrequency = 21; // 3周
        break;
      case 'mentor':
      case 'mentee':
        baseFrequency = 30; // 1个月
        break;
      case 'acquaintance':
        baseFrequency = 180; // 6个月
        break;
    }

    // 根据重要程度调整
    switch (contact.priority) {
      case 'critical':
        baseFrequency *= 0.5;
        break;
      case 'high':
        baseFrequency *= 0.7;
        break;
      case 'medium':
        baseFrequency *= 1;
        break;
      case 'low':
        baseFrequency *= 1.5;
        break;
    }

    // 根据连接强度调整
    if (contact.connectionStrength) {
      const strengthFactor = contact.connectionStrength / 100;
      baseFrequency *= (2 - strengthFactor); // 强度越高，频率越高
    }

    // 根据用户设置的联系频率调整
    if (contact.contactFrequency) {
      switch (contact.contactFrequency) {
        case 'daily':
          baseFrequency = Math.min(baseFrequency, 1);
          break;
        case 'weekly':
          baseFrequency = Math.min(baseFrequency, 7);
          break;
        case 'monthly':
          baseFrequency = Math.min(baseFrequency, 30);
          break;
        case 'quarterly':
          baseFrequency = Math.min(baseFrequency, 90);
          break;
        case 'yearly':
          baseFrequency = Math.min(baseFrequency, 365);
          break;
        case 'rarely':
          baseFrequency = Math.max(baseFrequency, 180);
          break;
      }
    }

    return Math.round(baseFrequency);
  }

  /**
   * 计算提醒优先级
   */
  private calculatePriority(contact: Contact, urgency: number): ReminderPriority {
    // 基础优先级
    let priority: ReminderPriority = 'medium';

    // 根据联系人重要程度
    switch (contact.priority) {
      case 'critical':
        priority = 'urgent';
        break;
      case 'high':
        priority = 'high';
        break;
      case 'medium':
        priority = 'medium';
        break;
      case 'low':
        priority = 'low';
        break;
    }

    // 根据紧急程度调整
    if (urgency >= 1.5) {
      priority = priority === 'low' ? 'medium' : 
                 priority === 'medium' ? 'high' : 
                 priority === 'high' ? 'urgent' : 'urgent';
    }

    return priority;
  }

  /**
   * 生成联系人操作建议
   */
  private generateContactActions(contact: Contact): any[] {
    const actions = [];

    if (contact.phone) {
      actions.push({
        id: 'call',
        type: 'call',
        label: '打电话',
        data: { phone: contact.phone }
      });

      actions.push({
        id: 'message',
        type: 'message',
        label: '发消息',
        data: { phone: contact.phone }
      });
    }

    if (contact.email) {
      actions.push({
        id: 'email',
        type: 'email',
        label: '发邮件',
        data: { email: contact.email }
      });
    }

    actions.push({
      id: 'meeting',
      type: 'meeting',
      label: '安排会面',
      data: { contactId: contact.id }
    });

    actions.push({
      id: 'note',
      type: 'note',
      label: '添加备注',
      data: { contactId: contact.id }
    });

    return actions;
  }

  /**
   * 生成智能提醒建议
   */
  generateSmartSuggestions(contacts: Contact[]): SmartReminderSuggestion[] {
    const suggestions: SmartReminderSuggestion[] = [];
    const today = new Date();

    contacts.forEach(contact => {
      // 分析是否需要生日提醒
      if (contact.birthday || contact.importantDates?.some(d => d.type === 'birthday')) {
        const birthdayAnalysis = this.analyzeBirthdayReminder(contact);
        if (birthdayAnalysis.shouldSuggest) {
          suggestions.push({
            id: `suggestion_birthday_${contact.id}`,
            contactId: contact.id,
            type: 'birthday',
            reason: birthdayAnalysis.reason,
            confidence: birthdayAnalysis.confidence,
            suggestedDate: birthdayAnalysis.suggestedDate,
            suggestedFrequency: 'yearly',
            metadata: {
              lastContactDays: birthdayAnalysis.lastContactDays,
              relationshipStrength: contact.connectionStrength || 0,
              contactImportance: this.getContactImportanceScore(contact),
              analysisData: birthdayAnalysis
            },
            createdAt: new Date()
          });
        }
      }

      // 分析是否需要定期联系提醒
      const followUpAnalysis = this.analyzeContactFrequency(contact);
      if (followUpAnalysis.shouldRemind && followUpAnalysis.urgency > 0.8) {
        suggestions.push({
          id: `suggestion_followup_${contact.id}`,
          contactId: contact.id,
          type: 'follow_up',
          reason: followUpAnalysis.reason,
          confidence: Math.min(followUpAnalysis.urgency, 1),
          suggestedDate: followUpAnalysis.suggestedDate,
          metadata: {
            lastContactDays: followUpAnalysis.daysSinceLastContact,
            relationshipStrength: contact.connectionStrength || 0,
            contactImportance: this.getContactImportanceScore(contact),
            analysisData: followUpAnalysis
          },
          createdAt: new Date()
        });
      }
    });

    return suggestions.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * 分析生日提醒建议
   */
  private analyzeBirthdayReminder(contact: Contact): {
    shouldSuggest: boolean;
    reason: string;
    confidence: number;
    suggestedDate: Date;
    lastContactDays: number;
  } {
    const today = new Date();
    const birthday = contact.birthday || 
      contact.importantDates?.find(date => date.type === 'birthday')?.date;
    
    if (!birthday) {
      return {
        shouldSuggest: false,
        reason: '',
        confidence: 0,
        suggestedDate: today,
        lastContactDays: 0
      };
    }

    const birthdayDate = new Date(birthday);
    const thisYearBirthday = new Date(today.getFullYear(), birthdayDate.getMonth(), birthdayDate.getDate());
    
    if (thisYearBirthday < today) {
      thisYearBirthday.setFullYear(today.getFullYear() + 1);
    }

    const daysUntilBirthday = Math.floor((thisYearBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    const lastContactDate = contact.lastContactDate ? new Date(contact.lastContactDate) : null;
    const lastContactDays = lastContactDate 
      ? Math.floor((today.getTime() - lastContactDate.getTime()) / (1000 * 60 * 60 * 24))
      : 365;

    const shouldSuggest = daysUntilBirthday <= 30 && daysUntilBirthday > 0;
    const confidence = shouldSuggest ? Math.max(0.5, 1 - (daysUntilBirthday / 30)) : 0;

    const suggestedDate = new Date(thisYearBirthday);
    suggestedDate.setDate(suggestedDate.getDate() - 3); // 提前3天提醒

    return {
      shouldSuggest,
      reason: shouldSuggest ? `${contact.name}的生日即将到来（${daysUntilBirthday}天后）` : '',
      confidence,
      suggestedDate,
      lastContactDays
    };
  }

  /**
   * 计算联系人重要程度分数
   */
  private getContactImportanceScore(contact: Contact): number {
    let score = 50; // 基础分数

    // 根据优先级调整
    switch (contact.priority) {
      case 'critical': score += 40; break;
      case 'high': score += 25; break;
      case 'medium': score += 0; break;
      case 'low': score -= 15; break;
    }

    // 根据关系类型调整
    switch (contact.relationshipType) {
      case 'family': score += 30; break;
      case 'friend': score += 20; break;
      case 'client': score += 25; break;
      case 'mentor': score += 20; break;
      case 'colleague': score += 10; break;
      case 'acquaintance': score -= 10; break;
    }

    // 根据连接强度调整
    if (contact.connectionStrength) {
      score += (contact.connectionStrength - 50) * 0.3;
    }

    // 根据交互次数调整
    if (contact.interactionCount) {
      score += Math.min(contact.interactionCount * 2, 20);
    }

    // 是否为收藏
    if (contact.isFavorite) {
      score += 15;
    }

    return Math.max(0, Math.min(100, score));
  }
}

// 导出单例实例
export const reminderEngine = new ReminderEngine();
export default reminderEngine;
