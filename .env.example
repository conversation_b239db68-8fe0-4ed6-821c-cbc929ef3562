# RelationHub Environment Variables
# Copy this file to .env and fill in your API keys

# OpenRouter API Key for AI parsing
# Get your API key from: https://openrouter.ai/keys
EXPO_PUBLIC_OPENROUTER_API_KEY=your_openrouter_api_key_here

# Google Vision API Key for OCR (optional)
# Get your API key from: https://console.cloud.google.com/
EXPO_PUBLIC_GOOGLE_VISION_API_KEY=your_google_vision_api_key_here

# App Configuration
EXPO_PUBLIC_APP_ENV=development
EXPO_PUBLIC_API_BASE_URL=https://api.relationhub.app

# Feature Flags
EXPO_PUBLIC_ENABLE_AI_PARSING=true
EXPO_PUBLIC_ENABLE_OCR=true
EXPO_PUBLIC_ENABLE_SMART_REMINDERS=true
